#!/usr/bin/env python3
"""Mobile-optimized launcher for Deep Sea Explorer."""

import pygame
import sys
import os
from game.submarine_game import SubmarineGame
from game.constants import set_responsive_dimensions

def detect_mobile_environment():
    """Detect if running on a mobile-like environment."""
    # Check for common mobile indicators
    mobile_indicators = [
        'ANDROID_ROOT',
        'ANDROID_DATA', 
        'TERMUX_VERSION',
        'PYDROID',
        'KIVY_WINDOW'
    ]
    
    for indicator in mobile_indicators:
        if indicator in os.environ:
            return True
            
    # Check screen resolution
    pygame.init()
    info = pygame.display.Info()
    
    # Typical mobile resolutions or small screens
    if (info.current_w <= 800 and info.current_h <= 600) or \
       (info.current_w <= 1024 and info.current_h <= 768):
        return True
        
    return False

def setup_mobile_optimizations():
    """Setup optimizations for mobile devices."""
    # Set environment variables for better mobile performance
    os.environ['SDL_HINT_TOUCH_MOUSE_EVENTS'] = '1'
    os.environ['SDL_HINT_MOUSE_TOUCH_EVENTS'] = '1'
    os.environ['SDL_HINT_ORIENTATIONS'] = 'LandscapeLeft LandscapeRight'
    
    # Enable touch events
    pygame.init()
    
def main():
    """Main entry point optimized for mobile."""
    print("🌊 Deep Sea Explorer - Mobile Edition")
    print("Detecting device capabilities...")
    
    is_mobile = detect_mobile_environment()
    
    if is_mobile:
        print("📱 Mobile device detected - applying optimizations")
        setup_mobile_optimizations()
    else:
        print("🖥️ Desktop device detected - using standard settings")
    
    # Set responsive dimensions
    width, height = set_responsive_dimensions()
    print(f"Screen resolution: {width}x{height}")
    
    try:
        print("🚀 Starting Deep Sea Explorer...")
        game = SubmarineGame()
        
        if is_mobile:
            print("📱 Mobile controls active:")
            print("   • Tap resources to deploy grappling hook")
            print("   • Tap automation buttons to toggle systems")
            print("   • Pinch to zoom (if supported)")
            print("   • Game runs automatically!")
        else:
            print("🖱️ Desktop controls active:")
            print("   • Click resources to deploy grappling hook")
            print("   • Click automation buttons to toggle systems")
            print("   • Resize window for different screen sizes")
            print("   • Game runs automatically!")
            
        print("\n🎮 Game starting... Have fun exploring the deep sea!")
        game.run()
        
    except KeyboardInterrupt:
        print("\n👋 Game interrupted by user")
    except Exception as e:
        print(f"❌ Error running game: {e}")
        import traceback
        traceback.print_exc()
    finally:
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    main()
