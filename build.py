#!/usr/bin/env python3
"""Build script for Deep Sea Explorer cross-platform deployment."""

import os
import sys
import shutil
import zipfile
from pathlib import Path

def create_distribution():
    """Create distribution packages for different platforms."""
    
    print("🌊 Deep Sea Explorer - Build Script")
    print("Creating cross-platform distribution packages...")
    
    # Create dist directory
    dist_dir = Path("dist")
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    dist_dir.mkdir()
    
    # Files to include in distribution
    game_files = [
        "main.py",
        "mobile_main.py", 
        "requirements.txt",
        "README.md",
        "game/"
    ]
    
    # Create desktop package
    desktop_dir = dist_dir / "deep-sea-explorer-desktop"
    desktop_dir.mkdir()
    
    print("📦 Creating desktop package...")
    for file_path in game_files:
        src = Path(file_path)
        if src.is_file():
            shutil.copy2(src, desktop_dir / src.name)
        elif src.is_dir():
            shutil.copytree(src, desktop_dir / src.name)
    
    # Create desktop launcher scripts
    with open(desktop_dir / "run_desktop.bat", "w") as f:
        f.write("@echo off\n")
        f.write("echo Starting Deep Sea Explorer - Desktop Edition\n")
        f.write("python main.py\n")
        f.write("pause\n")
    
    with open(desktop_dir / "run_desktop.sh", "w") as f:
        f.write("#!/bin/bash\n")
        f.write("echo 'Starting Deep Sea Explorer - Desktop Edition'\n")
        f.write("python3 main.py\n")
    
    # Make shell script executable
    os.chmod(desktop_dir / "run_desktop.sh", 0o755)
    
    # Create mobile package
    mobile_dir = dist_dir / "deep-sea-explorer-mobile"
    mobile_dir.mkdir()
    
    print("📱 Creating mobile package...")
    for file_path in game_files:
        src = Path(file_path)
        if src.is_file():
            shutil.copy2(src, mobile_dir / src.name)
        elif src.is_dir():
            shutil.copytree(src, mobile_dir / src.name)
    
    # Create mobile-specific files
    with open(mobile_dir / "MOBILE_INSTALL.md", "w") as f:
        f.write("""# 📱 Mobile Installation Guide

## Android (Termux)
1. Install Termux from F-Droid
2. Run: `pkg install python python-pip git`
3. Run: `pip install pygame noise`
4. Copy these files to your device
5. Run: `python mobile_main.py`

## Android (Pydroid 3)
1. Install Pydroid 3 from Google Play
2. Install dependencies in pip console
3. Copy files to Pydroid directory
4. Run mobile_main.py

## iOS (Pythonista)
1. Install Pythonista 3 from App Store
2. Copy files to Pythonista
3. Install pygame if available
4. Run mobile_main.py
""")
    
    # Create ZIP packages
    print("🗜️ Creating ZIP packages...")
    
    # Desktop ZIP
    with zipfile.ZipFile(dist_dir / "deep-sea-explorer-desktop.zip", "w", zipfile.ZIP_DEFLATED) as zf:
        for root, dirs, files in os.walk(desktop_dir):
            for file in files:
                file_path = Path(root) / file
                arc_path = file_path.relative_to(desktop_dir)
                zf.write(file_path, arc_path)
    
    # Mobile ZIP
    with zipfile.ZipFile(dist_dir / "deep-sea-explorer-mobile.zip", "w", zipfile.ZIP_DEFLATED) as zf:
        for root, dirs, files in os.walk(mobile_dir):
            for file in files:
                file_path = Path(root) / file
                arc_path = file_path.relative_to(mobile_dir)
                zf.write(file_path, arc_path)
    
    # Create universal package
    universal_dir = dist_dir / "deep-sea-explorer-universal"
    universal_dir.mkdir()
    
    print("🌍 Creating universal package...")
    for file_path in game_files:
        src = Path(file_path)
        if src.is_file():
            shutil.copy2(src, universal_dir / src.name)
        elif src.is_dir():
            shutil.copytree(src, universal_dir / src.name)
    
    # Copy launcher scripts
    shutil.copy2(desktop_dir / "run_desktop.bat", universal_dir)
    shutil.copy2(desktop_dir / "run_desktop.sh", universal_dir)
    shutil.copy2(mobile_dir / "MOBILE_INSTALL.md", universal_dir)
    
    # Create universal launcher
    with open(universal_dir / "run_mobile.py", "w") as f:
        f.write("#!/usr/bin/env python3\n")
        f.write("import subprocess\n")
        f.write("import sys\n")
        f.write("subprocess.run([sys.executable, 'mobile_main.py'])\n")
    
    # Universal ZIP
    with zipfile.ZipFile(dist_dir / "deep-sea-explorer-universal.zip", "w", zipfile.ZIP_DEFLATED) as zf:
        for root, dirs, files in os.walk(universal_dir):
            for file in files:
                file_path = Path(root) / file
                arc_path = file_path.relative_to(universal_dir)
                zf.write(file_path, arc_path)
    
    print("\n✅ Build complete!")
    print(f"📁 Distribution packages created in: {dist_dir.absolute()}")
    print("\n📦 Available packages:")
    print("   • deep-sea-explorer-desktop.zip - Optimized for desktop")
    print("   • deep-sea-explorer-mobile.zip - Optimized for mobile")
    print("   • deep-sea-explorer-universal.zip - Works on all platforms")
    print("\n🚀 Ready for deployment!")

if __name__ == "__main__":
    create_distribution()
