"""Resource management for the submarine game."""

import pygame
import random
import math
from .constants import *

class Resource:
    """Individual resource node that can be collected."""

    def __init__(self, x, y, resource_type, depth=0):
        self.x = x
        self.y = y
        self.type = resource_type
        self.depth = depth
        self.collected = False

        # Calculate size and value based on depth
        # Surface resources (depth 0-50) are smaller, deeper resources are larger
        depth_factor = min(1.0, depth / 200.0)  # Normalize depth to 0-1 over 200 pixels
        base_size = RESOURCE_TYPES[resource_type]['base_size']
        size_variation = random.uniform(0.8, 1.2)  # ±20% size variation

        # Size increases with depth: surface = 60% of base, deep = 140% of base
        self.max_size = int(base_size * (0.6 + 0.8 * depth_factor) * size_variation)
        self.current_size = self.max_size

        # Value also increases with depth
        base_value = RESOURCE_TYPES[resource_type]['value']
        self.max_value = int(base_value * (0.5 + 1.0 * depth_factor))
        self.current_value = self.max_value

        # Visual properties
        self.color = RESOURCE_TYPES[resource_type]['color']

        # Collection progress (0.0 = full, 1.0 = depleted)
        self.collection_progress = 0.0
        self.being_collected = False
        self.collection_rate = 0.003  # How fast the resource depletes when being collected (reduced for slower, more strategic collection)

        # Animation
        self.float_offset = random.random() * math.pi * 2
        self.float_speed = 0.02 + random.random() * 0.02
        self.glow_intensity = 0
        
    def update(self, dt, divers=None):
        """Update resource animations and collection progress."""
        # dt is now in seconds, so use it directly for smooth animations
        self.float_offset += self.float_speed * dt * 60  # Scale for visible animation speed
        self.glow_intensity = (math.sin(self.float_offset * 2) + 1) * 0.5

        # Update collection progress if being collected AND a diver is actually nearby collecting
        if self.being_collected and not self.collected:
            # Check if any diver is actually collecting this resource
            diver_actively_collecting = False
            if divers:
                for diver in divers:
                    if (diver.state == 'collecting' and
                        diver.target_resource == self):
                        # Check if diver is close enough to actually be collecting
                        distance = math.sqrt((diver.x - self.x)**2 + (diver.y - self.y)**2)
                        if distance < 15:  # Diver must be within 15 pixels to actually collect
                            diver_actively_collecting = True
                            break

            # Only deplete resource if a diver is actively collecting it
            if diver_actively_collecting:
                # Use delta time for consistent collection rate regardless of framerate
                self.collection_progress += self.collection_rate * dt * 60  # Scale for reasonable collection speed

                # Update visual size and current value based on collection progress
                remaining_factor = max(0.0, 1.0 - self.collection_progress)
                self.current_size = max(1, int(self.max_size * remaining_factor))
                self.current_value = max(1, int(self.max_value * remaining_factor))

                # Mark as collected when fully depleted
                if self.collection_progress >= 1.0:
                    self.collected = True
                    self.being_collected = False
            else:
                # No diver is actively collecting, stop the collection process
                self.being_collected = False
        
    def draw(self, screen):
        """Draw the resource with glow effect."""
        if self.collected or self.current_size <= 0:
            return

        # Calculate floating position with smoother animation
        float_y = self.y + math.sin(self.float_offset) * 3

        # Draw glow effect based on current size
        glow_radius = self.current_size + int(self.glow_intensity * 5)
        glow_color = tuple(min(255, c + int(self.glow_intensity * 50)) for c in self.color)

        # Add collection effect - make glow more intense when being collected
        if self.being_collected:
            # Pulsing effect during collection
            collection_pulse = math.sin(self.float_offset * 8) * 0.5 + 0.5
            collection_glow = int(100 * collection_pulse)
            glow_color = tuple(min(255, c + collection_glow) for c in glow_color)
            glow_radius += int(collection_pulse * 3)

        # Draw multiple circles for glow effect
        for i in range(3):
            alpha = 50 - i * 15
            glow_size = glow_radius - i * 2
            if glow_size > 0:
                pygame.draw.circle(screen, glow_color,
                                 (int(self.x), int(float_y)), glow_size)

        # Draw main resource using current size
        pygame.draw.circle(screen, self.color,
                         (int(self.x), int(float_y)), self.current_size)

        # Draw border - make it thicker and animated when being collected
        if self.being_collected:
            border_width = 3 + int(math.sin(self.float_offset * 6) * 1)
            border_color = tuple(min(255, c + 50) for c in WHITE)
        else:
            border_width = 2
            border_color = WHITE

        pygame.draw.circle(screen, border_color,
                         (int(self.x), int(float_y)), self.current_size, border_width)

        # Draw collection progress indicator
        if self.being_collected and self.collection_progress > 0:
            # Draw a small progress bar above the resource
            bar_width = self.current_size * 2
            bar_height = 3
            bar_x = int(self.x - bar_width // 2)
            bar_y = int(float_y - self.current_size - 8)

            # Background bar
            pygame.draw.rect(screen, (50, 50, 50),
                           (bar_x, bar_y, bar_width, bar_height))

            # Progress bar
            progress_width = int(bar_width * self.collection_progress)
            if progress_width > 0:
                pygame.draw.rect(screen, (255, 255, 0),
                               (bar_x, bar_y, progress_width, bar_height))
        
    def get_position(self):
        """Get current position for collision detection."""
        return (self.x, self.y + math.sin(self.float_offset) * 3)
        
    def start_collection(self):
        """Start the collection process."""
        if not self.collected and not self.being_collected:
            self.being_collected = True
            return True
        return False

    def stop_collection(self):
        """Stop the collection process."""
        self.being_collected = False

    def collect(self):
        """Mark resource as collected and return current value."""
        self.collected = True
        self.being_collected = False
        return self.type, self.current_value

    def collect_increment(self, amount=1):
        """Collect a small increment from the resource without fully depleting it."""
        if self.collected or not self.being_collected:
            return self.type, 0

        # Calculate how much we can actually collect
        collectible_amount = min(amount, self.current_value)

        if collectible_amount > 0:
            # Reduce the resource value and size
            self.current_value -= collectible_amount

            # Update collection progress based on value reduction
            value_progress = 1.0 - (self.current_value / max(1, self.max_value))
            self.collection_progress = value_progress

            # Update visual size
            remaining_factor = max(0.0, self.current_value / max(1, self.max_value))
            self.current_size = max(1, int(self.max_size * remaining_factor))

            # Mark as collected if fully depleted
            if self.current_value <= 0:
                self.collected = True
                self.being_collected = False

            return self.type, collectible_amount

        return self.type, 0

    def get_collection_yield(self):
        """Get the amount that would be collected if harvested now."""
        return self.current_value

class ResourceManager:
    """Manages all resources in the game."""
    
    def __init__(self):
        self.resources = []
        self.spawn_timer = 0
        self.spawn_interval = 240  # 4 seconds at 60 FPS (balanced for gameplay)
        
    def update(self, dt, submarine_depth, divers=None, submarine_pos=None):
        """Update all resources and spawn new ones."""
        # Performance optimization: Update and filter in single pass
        active_resources = []
        for resource in self.resources:
            if not resource.collected:
                resource.update(dt, divers)  # Pass divers to resource update
                active_resources.append(resource)

        self.resources = active_resources

        # Spawn new resources
        self.spawn_timer += 1
        if self.spawn_timer >= self.spawn_interval:
            self.spawn_timer = 0

            self._spawn_resource(submarine_depth, submarine_pos)
            
    def _spawn_resource(self, submarine_depth, submarine_pos=None):
        """Spawn a new resource based on current depth with intelligent positioning."""
        if len(self.resources) >= 12:  # Reduced to 12 for less crowding and more strategic resource management
            return

        # Choose resource type based on depth
        available_types = []
        for res_type, properties in RESOURCE_TYPES.items():
            if submarine_depth >= properties['depth_min']:
                available_types.append(res_type)

        if not available_types:
            available_types = ['kelp']  # Always have kelp available

        # Weight rarer resources less likely
        weights = {'kelp': 50, 'coral': 30, 'pearl': 15, 'treasure': 5}
        resource_type = random.choices(available_types,
                                     weights=[weights.get(t, 10) for t in available_types])[0]

        # Intelligent depth-based spawning
        if submarine_pos:
            # Define depth zones for spawning
            current_depth = submarine_depth

            # Spawn resources in a wider depth range to encourage vertical exploration
            # This creates layers of resources at different depths for exploration
            depth_variation = 250  # Increased variation for more vertical exploration
            min_spawn_depth = max(50, current_depth - depth_variation)  # Don't spawn too shallow
            max_spawn_depth = min(current_depth + depth_variation, WORLD_HEIGHT - SURFACE_LEVEL - 50)

            # Choose spawn depth based on resource type - rarer resources spawn deeper
            resource_depth_preferences = {
                'kelp': (0, 200),      # Shallow waters
                'coral': (100, 400),   # Medium depths
                'pearl': (200, 600),   # Deeper waters
                'treasure': (300, 800) # Deep waters
            }

            pref_min, pref_max = resource_depth_preferences.get(resource_type, (0, 400))

            # Combine current depth range with resource preferences
            depth_min = max(min_spawn_depth, pref_min)
            depth_max = min(max_spawn_depth, pref_max)

            # Ensure valid range for randint
            if depth_min >= depth_max:
                # Fallback to current depth if ranges don't overlap
                spawn_depth = max(50, min(current_depth, WORLD_HEIGHT - SURFACE_LEVEL - 50))
            else:
                # Additional safety check
                depth_min = int(depth_min)
                depth_max = int(depth_max)
                if depth_min < depth_max:
                    spawn_depth = random.randint(depth_min, depth_max)
                else:
                    spawn_depth = depth_min

            # Convert depth to Y coordinate
            y = SURFACE_LEVEL + spawn_depth

            # Horizontal spawning - encourage exploration with wider distribution
            # Use much wider horizontal range to encourage submarine movement
            horizontal_range = 600  # Much wider range for exploration

            # 40% chance to spawn within tether range, 60% chance to spawn further for exploration
            if random.random() < 0.4:
                # Spawn within tether range for divers
                min_distance = 80   # Close enough for divers
                max_distance = 180  # Within extended tether range
                offset = random.randint(min_distance, max_distance)
                if random.random() < 0.5:
                    offset = -offset  # Randomly choose left or right
                x = submarine_pos[0] + offset
            else:
                # Spawn further away for exploration (submarine will need to move)
                min_distance = 300  # Increased minimum distance to encourage more movement
                max_distance = horizontal_range
                offset = random.randint(min_distance, max_distance)
                if random.random() < 0.5:
                    offset = -offset  # Randomly choose left or right
                x = submarine_pos[0] + offset

            # Keep within world bounds
            x = max(50, min(WORLD_WIDTH - 50, x))
            y = max(SURFACE_LEVEL + 50, min(WORLD_HEIGHT - 50, y))
        else:
            # Fallback to procedural spawning if no submarine position
            # Add validation to ensure valid ranges
            x_min = 50
            x_max = WORLD_WIDTH - 50
            y_min = SURFACE_LEVEL + 100
            y_max = WORLD_HEIGHT - 50

            # Ensure valid ranges with additional safety
            x_min = int(x_min)
            x_max = int(x_max)
            y_min = int(y_min)
            y_max = int(y_max)

            if x_min >= x_max:
                x = WORLD_WIDTH // 2  # Fallback to center
            else:
                x = random.randint(x_min, x_max)

            if y_min >= y_max:
                y = SURFACE_LEVEL + 200  # Fallback to shallow depth
            else:
                y = random.randint(y_min, y_max)

        # Calculate final depth for this resource position
        resource_depth = max(0, y - SURFACE_LEVEL)

        self.resources.append(Resource(x, y, resource_type, resource_depth))
        
    def draw(self, screen):
        """Draw all resources."""
        for resource in self.resources:
            resource.draw(screen)
            
    def get_nearest_resource(self, pos, resource_type=None):
        """Get the nearest uncollected resource to a position."""
        available_resources = [r for r in self.resources 
                             if not r.collected and (resource_type is None or r.type == resource_type)]
        
        if not available_resources:
            return None
            
        nearest = min(available_resources, 
                     key=lambda r: math.sqrt((r.x - pos[0])**2 + (r.y - pos[1])**2))
        return nearest
        
    def collect_resource_at(self, pos, radius=20):
        """Collect resource at given position."""
        for resource in self.resources:
            if not resource.collected:
                resource_pos = resource.get_position()
                distance = math.sqrt((resource_pos[0] - pos[0])**2 + (resource_pos[1] - pos[1])**2)
                if distance <= radius:
                    return resource.collect()
        return None, 0

    def start_collection_at(self, pos, radius=20):
        """Start collecting resource at given position."""
        for resource in self.resources:
            if not resource.collected:
                resource_pos = resource.get_position()
                distance = math.sqrt((resource_pos[0] - pos[0])**2 + (resource_pos[1] - pos[1])**2)
                if distance <= radius:
                    return resource.start_collection()
        return False

    def stop_collection_at(self, pos, radius=20):
        """Stop collecting resource at given position."""
        for resource in self.resources:
            if resource.being_collected:
                resource_pos = resource.get_position()
                distance = math.sqrt((resource_pos[0] - pos[0])**2 + (resource_pos[1] - pos[1])**2)
                if distance <= radius:
                    resource.stop_collection()
                    return True
        return False
