"""User interface for the submarine game."""

import pygame
from .constants import *

class UI:
    """Handles all user interface elements."""
    
    def __init__(self):
        pygame.font.init()
        self.font_large = pygame.font.Font(None, 36)
        self.font_medium = pygame.font.Font(None, 24)
        self.font_small = pygame.font.Font(None, 18)
        
    def draw(self, screen, game_state):
        """Draw all UI elements."""
        self._draw_depth_indicator(screen, game_state['submarine'])
        self._draw_resource_panel(screen, game_state['resources'])
        self._draw_diver_status(screen, game_state['divers'])
        self._draw_grappling_status(screen, game_state['submarine'])
        self._draw_automation_status(screen, game_state.get('automation', {}))
        self._draw_controls_help(screen)

        if game_state['paused']:
            self._draw_pause_overlay(screen)
            
    def _draw_depth_indicator(self, screen, submarine):
        """Draw depth and submarine status."""
        depth_text = self.font_large.render(f"Depth: {int(submarine.depth)}m", True, WHITE)
        screen.blit(depth_text, (10, 10))
        
        # Max depth indicator
        max_depth_text = self.font_medium.render(f"Max: {submarine.max_depth}m", True, WHITE)
        screen.blit(max_depth_text, (10, 50))
        
        # Speed indicator
        speed_text = self.font_medium.render(f"Speed: {submarine.speed}", True, WHITE)
        screen.blit(speed_text, (10, 75))
        
    def _draw_resource_panel(self, screen, resources):
        """Draw resource inventory."""
        panel_x = SCREEN_WIDTH - 250
        panel_y = 10
        panel_width = 240
        panel_height = 150
        
        # Background panel
        panel_surface = pygame.Surface((panel_width, panel_height))
        panel_surface.set_alpha(200)
        panel_surface.fill(BLACK)
        screen.blit(panel_surface, (panel_x, panel_y))
        
        # Border
        pygame.draw.rect(screen, WHITE, (panel_x, panel_y, panel_width, panel_height), 2)
        
        # Title
        title = self.font_medium.render("Resources", True, WHITE)
        screen.blit(title, (panel_x + 10, panel_y + 10))
        
        # Resource list
        y_offset = 35
        for resource_type, amount in resources.items():
            color = WHITE
            if resource_type in RESOURCE_TYPES:
                color = RESOURCE_TYPES[resource_type]['color']
            elif resource_type == 'oxygen':
                color = (100, 200, 255)
                
            text = self.font_small.render(f"{resource_type.title()}: {amount}", True, color)
            screen.blit(text, (panel_x + 10, panel_y + y_offset))
            y_offset += 20
            
    def _draw_diver_status(self, screen, divers):
        """Draw diver status panel."""
        panel_x = SCREEN_WIDTH - 250
        panel_y = 180
        panel_width = 240
        panel_height = 120
        
        # Background panel
        panel_surface = pygame.Surface((panel_width, panel_height))
        panel_surface.set_alpha(200)
        panel_surface.fill(BLACK)
        screen.blit(panel_surface, (panel_x, panel_y))
        
        # Border
        pygame.draw.rect(screen, WHITE, (panel_x, panel_y, panel_width, panel_height), 2)
        
        # Title
        title = self.font_medium.render("Crew Status", True, WHITE)
        screen.blit(title, (panel_x + 10, panel_y + 10))
        
        # Diver status
        y_offset = 35
        for i, diver in enumerate(divers):
            state_color = WHITE
            if diver.state == 'swimming':
                state_color = (100, 200, 255)
            elif diver.state == 'collecting':
                state_color = YELLOW
            elif diver.state == 'returning':
                state_color = GREEN
                
            status_text = f"Diver {i+1}: {diver.state.title()}"
            if diver.state != 'idle':
                status_text += f" (O2: {int(diver.oxygen)}%)"
                
            text = self.font_small.render(status_text, True, state_color)
            screen.blit(text, (panel_x + 10, panel_y + y_offset))
            y_offset += 20

    def _draw_grappling_status(self, screen, submarine):
        """Draw grappling hook system status."""
        panel_x = SCREEN_WIDTH - 250
        panel_y = 320
        panel_width = 240
        panel_height = 140

        # Background panel
        panel_surface = pygame.Surface((panel_width, panel_height))
        panel_surface.set_alpha(200)
        panel_surface.fill(BLACK)
        screen.blit(panel_surface, (panel_x, panel_y))

        # Border
        pygame.draw.rect(screen, WHITE, (panel_x, panel_y, panel_width, panel_height), 2)

        # Title
        title = self.font_medium.render("🪝 Grappling Systems", True, WHITE)
        screen.blit(title, (panel_x + 10, panel_y + 10))

        # Get grappling stats
        stats = submarine.get_grappling_stats()

        # Automated hooks status
        y_offset = 35
        auto_stats = stats['automated']
        auto_text = f"Auto Hooks: {auto_stats['hook_count']}"
        text = self.font_small.render(auto_text, True, GREEN)
        screen.blit(text, (panel_x + 10, panel_y + y_offset))
        y_offset += 18

        # Player hook status
        player_stats = stats['player']
        player_color = YELLOW if player_stats['is_busy'] else WHITE
        player_text = f"Player Hook: {'DEPLOYED' if player_stats['is_busy'] else 'READY'}"
        text = self.font_small.render(player_text, True, player_color)
        screen.blit(text, (panel_x + 10, panel_y + y_offset))
        y_offset += 18

        # Unified collection range (same for both systems)
        unified_range = player_stats['max_range']  # Both systems now use same range
        range_text = f"Collection Range: {unified_range}"
        text = self.font_small.render(range_text, True, WHITE)
        screen.blit(text, (panel_x + 10, panel_y + y_offset))
        y_offset += 18

        # Instructions
        instruction = "Click resources to deploy player hook"
        text = self.font_small.render(instruction, True, (200, 200, 200))
        screen.blit(text, (panel_x + 10, panel_y + y_offset))

    def _draw_automation_status(self, screen, automation):
        """Draw automation system status."""
        panel_x = SCREEN_WIDTH - 250
        panel_y = 480
        panel_width = 240
        panel_height = 140  # Increased height for autopilot and mouse status

        # Background panel
        panel_surface = pygame.Surface((panel_width, panel_height))
        panel_surface.set_alpha(200)
        panel_surface.fill(BLACK)
        screen.blit(panel_surface, (panel_x, panel_y))

        # Border
        pygame.draw.rect(screen, WHITE, (panel_x, panel_y, panel_width, panel_height), 2)

        # Title
        title = self.font_medium.render("Automation Status", True, WHITE)
        screen.blit(title, (panel_x + 10, panel_y + 10))

        # Automation status
        y_offset = 35
        systems = [
            ('AutoPilot', automation.get('autopilot', True)),
            ('Crew AI', automation.get('crew_ai', True)),
            ('AutoCraft', automation.get('auto_craft', True))
        ]

        for system_name, enabled in systems:
            status_color = GREEN if enabled else RED
            status_text = f"{system_name}: {'ON' if enabled else 'OFF'}"
            text = self.font_small.render(status_text, True, status_color)
            screen.blit(text, (panel_x + 10, panel_y + y_offset))
            y_offset += 20

        # Autopilot detailed status
        autopilot_status = automation.get('autopilot_status', 'Unknown')
        status_color = YELLOW if 'Waiting' in autopilot_status else GREEN
        text = self.font_small.render(f"AP: {autopilot_status}", True, status_color)
        screen.blit(text, (panel_x + 10, panel_y + y_offset))
        y_offset += 18

        # Mouse controller status
        mouse_status = automation.get('mouse_status', 'Unknown')
        status_color = YELLOW if 'Manual' in mouse_status else GREEN
        text = self.font_small.render(f"Control: {mouse_status}", True, status_color)
        screen.blit(text, (panel_x + 10, panel_y + y_offset))
            
    def _draw_controls_help(self, screen):
        """Draw control instructions."""
        panel_x = 10
        panel_y = SCREEN_HEIGHT - 180
        panel_width = 320
        panel_height = 170

        # Background panel
        panel_surface = pygame.Surface((panel_width, panel_height))
        panel_surface.set_alpha(180)
        panel_surface.fill(BLACK)
        screen.blit(panel_surface, (panel_x, panel_y))

        # Border
        pygame.draw.rect(screen, WHITE, (panel_x, panel_y, panel_width, panel_height), 2)

        # Title
        title = self.font_medium.render("🖱️ MOUSE-ONLY CONTROLS", True, YELLOW)
        screen.blit(title, (panel_x + 10, panel_y + 10))

        # Subtitle
        subtitle = self.font_small.render("Fully automated idle gameplay!", True, GREEN)
        screen.blit(subtitle, (panel_x + 10, panel_y + 30))

        # Control instructions
        controls = [
            "🪝 Click Resource - Deploy player grappling hook",
            "🎯 Click Empty Area - Deploy hook to position",
            "🤖 Divers work automatically",
            "⚙️ Click AutoCraft - Toggle auto crafting",
            "⏸️ SPACE - Pause game",
            "❌ ESC - Quit"
        ]

        y_offset = 55
        for control in controls:
            text = self.font_small.render(control, True, WHITE)
            screen.blit(text, (panel_x + 10, panel_y + y_offset))
            y_offset += 16
            
    def _draw_pause_overlay(self, screen):
        """Draw pause overlay."""
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        screen.blit(overlay, (0, 0))
        
        pause_text = self.font_large.render("PAUSED", True, WHITE)
        text_rect = pause_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
        screen.blit(pause_text, text_rect)
        
        resume_text = self.font_medium.render("Press SPACE to resume", True, WHITE)
        resume_rect = resume_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 50))
        screen.blit(resume_text, resume_rect)
