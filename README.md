# 🌊 Deep Sea Explorer - Cross-Platform Edition

A responsive submarine exploration game that works on both desktop and mobile devices!

## 🎮 Game Features

- **🪝 Dual Collection System**: Automated divers + player-controlled grappling hook
- **🖱️ Mouse/Touch Controls**: Click or tap to deploy your grappling hook
- **🤖 Full Automation**: Game runs itself with intelligent AI systems
- **📱 Cross-Platform**: Works on desktop, mobile, and tablets
- **🎨 Responsive Design**: Adapts to any screen size
- **🌊 Immersive Visuals**: Procedural underwater terrain and effects
- **🌤️ Dynamic Surface/Underwater**: Clear visual distinction between surface and depths
- **☁️ Animated Sky & Clouds**: Beautiful sky system with realistic cloud movement
- **🌊 Wave-Masked Rendering**: Sky only appears above animated water surface
- **📏 Depth Indicators**: Visual markers showing distance from surface level

## 🚀 Quick Start

### Desktop Installation

1. **Install Python 3.7+**
2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```
3. **Run the game:**
   ```bash
   python main.py          # Standard version
   python mobile_main.py   # Mobile-optimized version
   ```

### Mobile Installation (Android)

#### Option 1: Termux (Recommended)
1. **Install Termux** from F-Droid or Google Play
2. **Setup Python:**
   ```bash
   pkg update
   pkg install python
   pkg install python-pip
   pkg install git
   ```
3. **Install game dependencies:**
   ```bash
   pip install pygame noise
   ```
4. **Download and run:**
   ```bash
   git clone <repository-url>
   cd deep-sea-explorer
   python mobile_main.py
   ```

#### Option 2: Pydroid 3
1. **Install Pydroid 3** from Google Play
2. **Install dependencies** in Pydroid's pip console:
   ```
   pip install pygame noise
   ```
3. **Copy game files** to your device
4. **Run** `mobile_main.py` in Pydroid 3

### iOS Installation
1. **Install Pythonista 3** from App Store
2. **Copy game files** to Pythonista
3. **Install dependencies** (may require manual setup)
4. **Run** the game in Pythonista

## 🎯 How to Play

### 🖱️ Desktop Controls
- **Click Resources**: Deploy your grappling hook to collect specific resources
- **Click Empty Areas**: Deploy hook to explore specific positions
- **Mouse Drag**: Move camera around (free cam mode)
- **Mouse Wheel**: Zoom in/out for better view
- **Right Click**: Temporary submarine control
- **Automation Buttons**: Toggle AutoPilot, Crew AI, and AutoCraft systems
- **Window Resize**: Game adapts to any window size
- **ESC**: Quit game
- **SPACE**: Pause/unpause
- **D**: Toggle debug information

### 📱 Mobile Controls
- **Tap Resources**: Deploy your grappling hook to collect resources
- **Tap Empty Areas**: Deploy hook to explore positions
- **Tap Automation Buttons**: Toggle AI systems
- **Pinch Gesture**: Zoom in/out (where supported)
- **Game Auto-Rotates**: Supports landscape orientations

## 🤖 Automation Systems

The game features intelligent automation that works on all platforms:

- **🚤 AutoPilot**: Submarine explores automatically
- **👥 Crew AI**: Divers collect resources autonomously  
- **⚙️ AutoCraft**: Automatically crafts upgrades
- **🪝 Player Hook**: Manual grappling hook control

## 📱 Mobile Optimizations

- **Responsive UI**: Scales to any screen size
- **Touch-Friendly**: Large buttons and touch zones
- **Performance Optimized**: Smooth gameplay on mobile devices
- **Battery Efficient**: Optimized rendering and updates
- **Orientation Support**: Works in landscape mode

## 🛠️ Technical Requirements

### Minimum Requirements
- **Python**: 3.7 or higher
- **RAM**: 512MB available
- **Storage**: 50MB free space
- **Screen**: 480x320 minimum resolution

### Recommended
- **Python**: 3.9+
- **RAM**: 1GB available
- **Storage**: 100MB free space
- **Screen**: 800x600 or higher

## 🔧 Troubleshooting

### Common Issues

**Game won't start:**
- Ensure Python 3.7+ is installed
- Install dependencies: `pip install pygame noise`
- Try mobile launcher: `python mobile_main.py`

**Touch not working:**
- Use mobile launcher: `python mobile_main.py`
- Check device supports touch events
- Try clicking instead of tapping

**Performance issues:**
- Close other applications
- Use mobile launcher for optimizations
- Reduce window size on desktop

**Screen too small:**
- Game automatically adapts to screen size
- Try landscape orientation on mobile
- Resize window on desktop

## 🎨 Game Mechanics

### 🌊 Surface vs Underwater Experience
- **Surface Level**: Beautiful sky with animated clouds, dock operations, no underwater elements visible
- **Diving Transition**: Smooth transition between surface and underwater views
- **Underwater Exploration**: Full terrain, resources, and diver operations visible
- **Fixed Surface Level**: Surface stays at consistent world position regardless of camera movement
- **Wave Masking**: Sky only appears above the animated water surface for realistic immersion

### Resource Collection
- **🌿 Kelp**: Common, found in shallow waters, used for oxygen tanks
- **🪸 Coral**: Medium rarity, deeper waters, used for upgrades
- **🦪 Pearl**: Rare, deep ocean, valuable for advanced upgrades
- **💎 Treasure**: Very rare, deepest depths, premium upgrade material

### Progression System
- **Submarine Upgrades**: Speed, depth, hull improvements with visual changes
- **Grappling Hooks**: Range, speed, additional hooks for better collection
- **Diver Crew**: Hire and upgrade autonomous divers for resource gathering
- **Dock Facilities**: Upgrade storage, automation, and processing capabilities
- **Automation**: Enhanced AI capabilities for hands-off gameplay

## 🌟 Tips for Success

1. **Let Automation Work**: The game is designed to run itself efficiently
2. **Strategic Hook Deployment**: Use your hook for high-value targets and precise collection
3. **Upgrade Wisely**: Focus on grappling hook and diver improvements first
4. **Explore Depths**: Deeper areas have rarer resources but require better equipment
5. **Watch Oxygen**: Manage your submarine's air supply during deep dives
6. **Use Camera Controls**: Drag to look around, zoom to get better views of resources
7. **Surface Operations**: Return to dock regularly to unload resources and upgrade
8. **Depth Awareness**: Use the depth markers to understand your position relative to surface

## 🎨 Visual Features

### 🌤️ Surface & Sky System
- **Dynamic Sky**: Beautiful blue gradient sky with realistic cloud formations
- **Animated Clouds**: Clouds drift naturally across the sky with varying sizes and opacity
- **Wave-Based Masking**: Sky only appears above the water surface, creating realistic boundaries
- **Fixed Surface Level**: Surface remains at consistent world position (100m depth marker)

### 🌊 Water & Surface Effects
- **Animated Waves**: Multi-layered wave system with realistic movement patterns
- **Color Blending**: Surface water blends naturally with depth-based underwater colors
- **Surface Reflections**: Subtle reflection effects when camera is near surface
- **Depth-Based Water Color**: Water color changes from light blue to deep blue based on depth

### 📏 Navigation & Depth Indicators
- **Surface Level Line**: Clear white line marking the exact surface boundary
- **Depth Markers**: Visual markers every 50m showing distance from surface
- **Color-Coded Depths**: Sky blue markers above surface, deep blue below
- **Distance Labels**: Depth measurements at major intervals (100m, 200m, etc.)

### 🎮 Camera & Visibility System
- **Smart Visibility**: Underwater elements (terrain, resources, divers) only visible when underwater
- **Surface Mode**: When at surface, only sky, clouds, dock, and submarine are visible
- **Smooth Transitions**: Gradual fade between surface and underwater views
- **Free Camera**: Drag to look around while maintaining realistic visibility rules

## 📄 License

This project is private and proprietary.

---

**🌊 Dive deep, explore far, and discover the treasures of the ocean! 🌊**
