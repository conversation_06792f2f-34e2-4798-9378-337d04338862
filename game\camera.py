"""Camera system for the submarine game with zoom and free cam functionality."""

import pygame
import math
from .constants import *

class Camera:
    """Camera system that follows the submarine with zoom and limited free cam."""
    
    def __init__(self, viewport_width, viewport_height):
        # Viewport dimensions (playable area size)
        self.viewport_width = viewport_width
        self.viewport_height = viewport_height
        
        # Camera position in world coordinates - start at submarine position
        self.x = WORLD_WIDTH // 2  # Start at center of world
        self.y = SURFACE_LEVEL + 50  # Start near surface where submarine spawns

        # Target position (what the camera should follow)
        self.target_x = self.x
        self.target_y = self.y
        
        # Zoom settings
        self.zoom = DEFAULT_ZOOM
        self.target_zoom = DEFAULT_ZOOM
        
        # Free cam mode - start in free cam by default for mouse-only control
        self.free_cam_active = True
        self.free_cam_offset_x = 0
        self.free_cam_offset_y = 0
        self.submarine_x = self.x  # Track submarine position for free cam limits
        self.submarine_y = self.y

        # Mouse drag state
        self.dragging = False
        self.last_mouse_pos = None
        
        # Smooth movement
        self.follow_speed = 0.1  # Faster following for better responsiveness
        
    def update(self, submarine_x, submarine_y, dt):
        """Update camera position and zoom."""
        # Store submarine position for free cam limits
        self.submarine_x = submarine_x
        self.submarine_y = submarine_y

        if not self.free_cam_active:
            # Follow submarine directly for better responsiveness
            self.target_x = submarine_x
            self.target_y = submarine_y
        else:
            # Free cam mode - limit distance from submarine (scale with zoom for more freedom when zoomed out)
            max_distance = FREE_CAM_RANGE / max(0.01, self.zoom)  # Prevent division by zero

            # Calculate desired free cam position
            desired_x = submarine_x + self.free_cam_offset_x
            desired_y = submarine_y + self.free_cam_offset_y

            # Limit free cam to maximum range from submarine
            dx = desired_x - submarine_x
            dy = desired_y - submarine_y
            distance = math.sqrt(dx*dx + dy*dy)

            if distance > max_distance and distance > 0:  # Prevent division by zero
                # Clamp to maximum distance
                factor = max_distance / distance
                self.free_cam_offset_x = dx * factor
                self.free_cam_offset_y = dy * factor

            self.target_x = submarine_x + self.free_cam_offset_x
            self.target_y = submarine_y + self.free_cam_offset_y

        # Smooth camera movement - use frame-rate independent movement
        dt_clamped = min(dt, 1.0/30.0)  # Clamp dt to prevent large jumps
        lerp_factor = self.follow_speed * dt_clamped * 60

        self.x += (self.target_x - self.x) * lerp_factor
        self.y += (self.target_y - self.y) * lerp_factor

        # Smooth zoom - also frame-rate independent
        zoom_lerp = 0.15 * dt_clamped * 60
        self.zoom += (self.target_zoom - self.zoom) * zoom_lerp
        self.zoom = max(MIN_ZOOM, min(MAX_ZOOM, self.zoom))  # Enforce bounds

        # Keep camera within world bounds
        self._clamp_to_world_bounds()
    
    def _clamp_to_world_bounds(self):
        """Keep camera within world boundaries."""
        # Calculate visible area at current zoom
        safe_zoom = max(0.01, self.zoom)  # Prevent division by zero
        visible_width = self.viewport_width / safe_zoom
        visible_height = self.viewport_height / safe_zoom
        
        # Clamp camera position
        self.x = max(visible_width / 2, min(WORLD_WIDTH - visible_width / 2, self.x))
        self.y = max(visible_height / 2, min(WORLD_HEIGHT - visible_height / 2, self.y))
        
        # Also clamp target to prevent oscillation
        self.target_x = max(visible_width / 2, min(WORLD_WIDTH - visible_width / 2, self.target_x))
        self.target_y = max(visible_height / 2, min(WORLD_HEIGHT - visible_height / 2, self.target_y))
    
    def set_zoom(self, zoom_level):
        """Set target zoom level."""
        self.target_zoom = max(MIN_ZOOM, min(MAX_ZOOM, zoom_level))
    
    def zoom_in(self):
        """Zoom in by zoom speed."""
        self.set_zoom(self.target_zoom + ZOOM_SPEED)
    
    def zoom_out(self):
        """Zoom out by zoom speed."""
        self.set_zoom(self.target_zoom - ZOOM_SPEED)
    
    def start_free_cam(self):
        """Start free cam mode."""
        self.free_cam_active = True
        # Initialize free cam offset to current offset from submarine
        self.free_cam_offset_x = self.x - self.submarine_x
        self.free_cam_offset_y = self.y - self.submarine_y
    
    def stop_free_cam(self):
        """Stop free cam mode and return to following submarine."""
        self.free_cam_active = False
        self.free_cam_offset_x = 0
        self.free_cam_offset_y = 0
    
    def move_free_cam(self, dx, dy):
        """Move free cam by given offset (in world coordinates)."""
        if self.free_cam_active:
            self.free_cam_offset_x += dx
            self.free_cam_offset_y += dy

    def start_mouse_drag(self, mouse_pos):
        """Start mouse drag for camera movement."""
        self.dragging = True
        self.last_mouse_pos = mouse_pos

        # Automatically enable free cam mode when user starts dragging
        if not self.free_cam_active:
            self.start_free_cam()

    def update_mouse_drag(self, mouse_pos):
        """Update camera position based on mouse drag."""
        if self.dragging and self.last_mouse_pos:
            # Calculate mouse movement
            dx = mouse_pos[0] - self.last_mouse_pos[0]
            dy = mouse_pos[1] - self.last_mouse_pos[1]

            # Convert screen movement to world movement (inverted for natural feel)
            safe_zoom = max(0.01, self.zoom)  # Prevent division by zero
            world_dx = -dx / safe_zoom
            world_dy = -dy / safe_zoom

            # Move free cam
            self.move_free_cam(world_dx, world_dy)

            self.last_mouse_pos = mouse_pos

    def stop_mouse_drag(self):
        """Stop mouse drag."""
        self.dragging = False
        self.last_mouse_pos = None

    def focus_on_submarine(self):
        """Return camera focus to submarine (disable free cam)."""
        self.free_cam_active = False
        self.free_cam_offset_x = 0
        self.free_cam_offset_y = 0
        self.dragging = False
        self.last_mouse_pos = None
    
    def world_to_screen(self, world_x, world_y):
        """Convert world coordinates to screen coordinates."""
        # Calculate position relative to camera center
        relative_x = world_x - self.x
        relative_y = world_y - self.y

        # Apply zoom
        screen_x = relative_x * self.zoom + self.viewport_width * 0.5
        screen_y = relative_y * self.zoom + self.viewport_height * 0.5

        return screen_x, screen_y
    
    def screen_to_world(self, screen_x, screen_y):
        """Convert screen coordinates to world coordinates."""
        # Convert screen position to relative position
        safe_zoom = max(0.01, self.zoom)  # Prevent division by zero
        relative_x = (screen_x - self.viewport_width / 2) / safe_zoom
        relative_y = (screen_y - self.viewport_height / 2) / safe_zoom
        
        # Add camera position
        world_x = relative_x + self.x
        world_y = relative_y + self.y
        
        return world_x, world_y
    
    def is_visible(self, world_x, world_y, object_width=0, object_height=0):
        """Check if an object at world coordinates is visible on screen."""
        screen_x, screen_y = self.world_to_screen(world_x, world_y)
        
        # Add some margin for objects that are partially visible
        margin = 50
        return (-margin <= screen_x <= self.viewport_width + margin and
                -margin <= screen_y <= self.viewport_height + margin)
    
    def get_visible_world_bounds(self):
        """Get the world coordinates of the visible area."""
        # Calculate corners of visible area
        top_left_world = self.screen_to_world(0, 0)
        bottom_right_world = self.screen_to_world(self.viewport_width, self.viewport_height)
        
        return {
            'left': top_left_world[0],
            'top': top_left_world[1],
            'right': bottom_right_world[0],
            'bottom': bottom_right_world[1],
            'width': bottom_right_world[0] - top_left_world[0],
            'height': bottom_right_world[1] - top_left_world[1]
        }
    
    def resize_viewport(self, new_width, new_height):
        """Update viewport size when window is resized."""
        self.viewport_width = new_width
        self.viewport_height = new_height
        self._clamp_to_world_bounds()
