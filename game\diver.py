"""Diver class for crew management."""

import pygame
import math
from .constants import *

class Diver:
    """Individual diver that can collect resources."""
    
    def __init__(self, x, y, diver_id):
        self.x = x
        self.y = y
        self.start_x = x
        self.start_y = y
        self.target_x = x
        self.target_y = y
        self.id = diver_id
        
        # State management
        self.state = 'idle'  # idle, swimming, collecting, returning
        self.base_speed = DIVER_SPEED
        self.speed = DIVER_SPEED
        self.oxygen = 60  # Reduced from 100 to make oxygen management more important
        self.max_oxygen = 60  # Reduced from 100
        self.collected_resource = None

        # Upgrade effects
        self.speed_bonus = 0.0
        self.oxygen_efficiency = 0.0
        self.collection_speed_bonus = 0.0
        self.tether_range_bonus = 0.0

        # Tether system - unified with grappling hook range
        self.base_tether_range = 100  # Unified base range with grappling hooks
        self.tether_range = self.base_tether_range

        # Visual effects
        self.swim_animation = 0
        self.collection_timer = 0
        self.base_collection_time = 360  # Increased to 360 frames (6 seconds) for slower, more strategic collection
        self.target_resource = None  # Track which resource we're collecting
        
    def update(self, dt, submarine_pos, submarine=None):
        """Update diver state and position."""
        # dt is now in seconds, so scale appropriately for animation speeds
        if self.state == 'swimming':
            self.swim_animation += dt * 9.0  # Faster animation when swimming
        elif self.state == 'collecting':
            self.swim_animation += dt * 15.0  # Fastest animation when collecting
        elif self.state == 'returning':
            self.swim_animation += dt * 7.2  # Moderate animation when returning
        else:  # idle
            self.swim_animation += dt * 3.0  # Slow animation when idle

        if self.state == 'idle':
            # When idle, diver should be inside the submarine (not visible as separate entity)
            # Position divers at submarine center with small random offset for variety
            small_offset_x = (self.id * 3) - 6  # Small offset based on diver ID (-6 to +6 pixels)
            small_offset_y = (self.id * 2) - 4  # Small offset based on diver ID (-4 to +4 pixels)
            self.x = submarine_pos[0] + small_offset_x
            self.y = submarine_pos[1] + small_offset_y

        elif self.state == 'swimming':
            self._move_to_target(dt)
            # Apply oxygen efficiency upgrade (increased consumption for more pressure)
            oxygen_consumption = 0.25 * (1.0 - self.oxygen_efficiency)
            self.oxygen = max(0, self.oxygen - oxygen_consumption)

            # Check if reached target
            distance = math.sqrt((self.target_x - self.x)**2 + (self.target_y - self.y)**2)
            if distance < 10:
                # Double-check that we're actually close to the resource before collecting
                if self.target_resource:
                    resource_distance = math.sqrt((self.target_resource.x - self.x)**2 + (self.target_resource.y - self.y)**2)
                    if resource_distance < 15:  # Must be within 15 pixels of actual resource
                        self.state = 'collecting'
                        # Apply collection speed upgrade
                        collection_time = int(self.base_collection_time * (1.0 - self.collection_speed_bonus))
                        self.collection_timer = max(60, collection_time)  # Minimum 1.0 seconds
                        # Start collection process on the resource
                        self.target_resource.start_collection()
                    else:
                        # Target position reached but resource is too far, update target to resource position
                        self.target_x = self.target_resource.x
                        self.target_y = self.target_resource.y
                else:
                    # No target resource, return to submarine
                    self.state = 'returning'
                    self.target_x, self.target_y = submarine_pos

        elif self.state == 'collecting':
            self.collection_timer -= 1

            # Consume oxygen while collecting (slower than swimming but still consuming)
            oxygen_consumption = 0.12 * (1.0 - self.oxygen_efficiency)
            self.oxygen = max(0, self.oxygen - oxygen_consumption)

            # Check if diver is still close enough to the resource to collect it
            if self.target_resource:
                distance_to_resource = math.sqrt((self.target_resource.x - self.x)**2 + (self.target_resource.y - self.y)**2)
                if distance_to_resource > 15:  # If diver drifted too far from resource (reduced threshold)
                    # Stop collecting and return to submarine
                    if self.target_resource:
                        self.target_resource.stop_collection()
                        self.target_resource = None  # Clear target reference
                    # Keep any collected resources we've accumulated
                    self.state = 'returning'
                    self.target_x, self.target_y = submarine_pos
                    return

            # Check if submarine can no longer store this resource type (storage became full)
            if self.target_resource and submarine:
                if not submarine.can_store_resource(self.target_resource.type, 1):
                    # Storage is full for this resource type, abandon collection and return
                    if self.target_resource:
                        self.target_resource.stop_collection()
                        self.target_resource = None  # Clear target reference
                    # Keep any collected resources we've accumulated
                    self.state = 'returning'
                    self.target_x, self.target_y = submarine_pos
                    return

            # Collect incrementally every few frames
            if self.collection_timer % 60 == 0:  # Collect every 1.0 seconds (slower for better balance)
                if self.target_resource and not self.target_resource.collected:
                    # Collect a small increment
                    resource_type, collected_amount = self.target_resource.collect_increment(1)
                    if collected_amount > 0:
                        # Store the collected amount (accumulate)
                        if not hasattr(self, 'collected_amount'):
                            self.collected_amount = 0
                        self.collected_amount += collected_amount
                        self.collected_resource = (resource_type, self.collected_amount)

            # Check if target resource is fully collected, timer expired, or oxygen is low
            should_return = False
            if not self.target_resource:
                should_return = True
            elif self.target_resource.collected:
                # Resource is fully depleted
                should_return = True
            elif self.collection_timer <= 0:
                # Timer expired - reset and continue collecting if resource still has value
                if self.target_resource and self.target_resource.current_value > 0:
                    # Reset timer to continue collecting
                    collection_time = int(self.base_collection_time * (1.0 - self.collection_speed_bonus))
                    self.collection_timer = max(60, collection_time)
                else:
                    should_return = True
            elif self.oxygen <= 10:
                # Emergency oxygen level - must return
                should_return = True

            if should_return:
                # Stop collection process
                if self.target_resource:
                    self.target_resource.stop_collection()
                    self.target_resource = None
                self.state = 'returning'
                self.target_x, self.target_y = submarine_pos
                
        elif self.state == 'returning':
            # Continuously update target to current submarine position
            self.target_x, self.target_y = submarine_pos

            # Check distance to submarine for emergency return
            distance_to_sub = math.sqrt((submarine_pos[0] - self.x)**2 + (submarine_pos[1] - self.y)**2)

            # Emergency return if too far from submarine or low oxygen (increased threshold)
            if distance_to_sub > self.tether_range * 1.5 or self.oxygen <= 20:
                # Emergency teleport back to submarine
                self.state = 'idle'
                self.oxygen = max(10, self.oxygen)  # Ensure some oxygen remains

                # Position diver inside submarine when returning via emergency (same as idle)
                small_offset_x = (self.id * 3) - 6  # Small offset based on diver ID
                small_offset_y = (self.id * 2) - 4  # Small offset based on diver ID
                self.x = submarine_pos[0] + small_offset_x
                self.y = submarine_pos[1] + small_offset_y

                # Stop any ongoing collection and return collected resource
                if self.target_resource:
                    self.target_resource.stop_collection()

                collected = self.collected_resource
                self.collected_resource = None
                self.target_resource = None
                # Reset collected amount for next collection cycle
                if hasattr(self, 'collected_amount'):
                    self.collected_amount = 0
                return collected

            self._move_to_target(dt)
            # Apply oxygen efficiency upgrade (increased consumption when returning for more pressure)
            oxygen_consumption = 0.15 * (1.0 - self.oxygen_efficiency)
            self.oxygen = max(0, self.oxygen - oxygen_consumption)

            # Check if reached submarine (using current submarine position)
            if distance_to_sub < 35:  # Increased to 35 for more reliable docking with new positioning
                # Properly dock at submarine

                self.state = 'idle'
                self.oxygen = self.max_oxygen  # Refill oxygen

                # Position diver inside submarine when idle (same as idle state)
                small_offset_x = (self.id * 3) - 6  # Small offset based on diver ID
                small_offset_y = (self.id * 2) - 4  # Small offset based on diver ID
                self.x = submarine_pos[0] + small_offset_x
                self.y = submarine_pos[1] + small_offset_y

                # Return collected resource and reset
                collected = self.collected_resource
                self.collected_resource = None
                self.target_resource = None
                # Reset collected amount for next collection cycle
                if hasattr(self, 'collected_amount'):
                    self.collected_amount = 0
                return collected
                
        return None
        
    def _move_to_target(self, dt):
        """Move towards target position."""
        dx = self.target_x - self.x
        dy = self.target_y - self.y
        distance = math.sqrt(dx**2 + dy**2)
        
        if distance > 0:
            # Apply speed bonus from upgrades
            effective_speed = self.base_speed * (1.0 + self.speed_bonus)
            move_x = (dx / distance) * effective_speed
            move_y = (dy / distance) * effective_speed
            self.x += move_x
            self.y += move_y
            
    def send_to_resource(self, resource_pos, resource_type, submarine_pos, resource_obj=None, submarine=None):
        """Send diver to collect a specific resource if within tether range and submarine has storage."""
        try:
            if self.state == 'idle':
                # Don't deploy if submarine is docked
                if submarine and submarine.is_docked:
                    return False  # Submarine is docked, no resource collection allowed

                # Check if submarine has storage space
                if submarine and not submarine.can_store_resource(resource_type, 1):
                    return False  # Submarine storage is full

                # Check if resource is within tether range
                distance_to_resource = math.sqrt((resource_pos[0] - submarine_pos[0])**2 +
                                               (resource_pos[1] - submarine_pos[1])**2)

                if distance_to_resource <= self.tether_range:
                    self.target_x, self.target_y = resource_pos
                    self.collected_resource = resource_type
                    self.target_resource = resource_obj  # Track the actual resource object
                    self.state = 'swimming'

                    return True
                else:
                    return False  # Resource is out of tether range
            return False
        except Exception as e:
            print(f"Error in send_to_resource: {e}")
            return False
        
    def draw(self, screen, submarine_pos, show_tether_range=False, submarine_stationary=True):
        """Draw the diver with animations and tether."""
        try:
            # Individual diver tether range indicators are no longer drawn
            # Unified collection range is drawn by the main game system

            # Draw tether line to submarine
            if self.state != 'idle':
                # Color tether line based on distance
                distance_to_sub = math.sqrt((self.x - submarine_pos[0])**2 + (self.y - submarine_pos[1])**2)
                if distance_to_sub > self.tether_range * 0.9:  # Near limit
                    tether_color = (255, 200, 200)  # Reddish
                else:
                    tether_color = (255, 255, 255)  # White

                pygame.draw.line(screen, tether_color,
                               submarine_pos, (int(self.x), int(self.y)), 2)

            # Diver color based on state with enhanced visual feedback
            if self.state == 'collecting':
                # Pulsing yellow when collecting
                pulse = math.sin(self.swim_animation * 2) * 0.3 + 0.7
                color = (int(255 * pulse), int(255 * pulse), 0)
            elif self.state == 'returning' and self.collected_resource:
                # Bright green when returning with resource
                color = (0, 255, 100)
            elif self.oxygen <= 10:
                color = (255, 0, 255)  # Magenta for emergency oxygen
            elif self.oxygen < 30:
                color = RED
            elif self.state == 'idle' and not submarine_stationary:
                color = (150, 150, 150)  # Gray when submarine moving (can't deploy)
            else:
                color = (100, 200, 255)

            # Enhanced animation based on state
            if self.state == 'swimming':
                # Swimming animation - bobbing and swaying
                swim_offset_x = math.sin(self.swim_animation) * 2
                swim_offset_y = math.cos(self.swim_animation * 1.5) * 1
            elif self.state == 'collecting':
                # Collecting animation - rapid vibration
                swim_offset_x = math.sin(self.swim_animation * 4) * 1.5
                swim_offset_y = math.cos(self.swim_animation * 3) * 1.5
            elif self.state == 'returning':
                # Returning animation - steady movement
                swim_offset_x = math.sin(self.swim_animation * 0.8) * 1
                swim_offset_y = 0
            else:
                # Idle animation - gentle floating
                swim_offset_x = math.sin(self.swim_animation * 0.5) * 0.5
                swim_offset_y = math.cos(self.swim_animation * 0.3) * 0.5

            diver_x = int(self.x + swim_offset_x)
            diver_y = int(self.y + swim_offset_y)

            # Draw diver with enhanced visuals
            pygame.draw.circle(screen, color, (diver_x, diver_y), 8)
            pygame.draw.circle(screen, WHITE, (diver_x, diver_y), 8, 2)

            # Draw collection indicator when collecting
            if self.state == 'collecting':
                # Draw small sparks around diver
                for i in range(3):
                    spark_angle = self.swim_animation * 4 + i * 2.1
                    spark_x = diver_x + math.cos(spark_angle) * 12
                    spark_y = diver_y + math.sin(spark_angle) * 12
                    pygame.draw.circle(screen, YELLOW, (int(spark_x), int(spark_y)), 2)

            # Draw oxygen bar
            if self.state != 'idle':
                bar_width = 20
                bar_height = 4
                bar_x = int(self.x - bar_width // 2)
                bar_y = int(self.y - 15)

                # Background
                pygame.draw.rect(screen, BLACK,
                               (bar_x, bar_y, bar_width, bar_height))

                # Oxygen level
                oxygen_width = int((self.oxygen / max(1, self.max_oxygen)) * bar_width)
                oxygen_color = GREEN if self.oxygen > 50 else YELLOW if self.oxygen > 25 else RED
                pygame.draw.rect(screen, oxygen_color,
                               (bar_x, bar_y, oxygen_width, bar_height))
        except Exception as e:
            print(f"Error in diver draw: {e}")
            # Draw a simple fallback circle
            pygame.draw.circle(screen, (100, 200, 255), (int(self.x), int(self.y)), 8)

    def apply_upgrade_effects(self, effects):
        """Apply upgrade effects to this diver."""
        self.speed_bonus = effects.get('speed_bonus', 0.0)
        self.oxygen_efficiency = effects.get('oxygen_efficiency', 0.0)
        self.collection_speed_bonus = effects.get('collection_speed', 0.0)
        # Use unified_range instead of tether_range for compatibility
        self.tether_range_bonus = effects.get('unified_range', effects.get('tether_range', 0.0))

        # Update effective speed and tether range
        self.speed = self.base_speed * (1.0 + self.speed_bonus)
        self.tether_range = self.base_tether_range + self.tether_range_bonus

    def is_available(self):
        """Check if diver is available for new tasks."""
        return self.state == 'idle' and self.oxygen > 25  # Reduced minimum oxygen requirement to allow more frequent trips
