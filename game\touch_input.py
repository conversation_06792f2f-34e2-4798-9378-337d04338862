"""Touch input handling for mobile devices."""

import pygame
import time
import math

class TouchInput:
    """Handles touch input and gestures for mobile devices."""
    
    def __init__(self, responsive_ui):
        self.responsive_ui = responsive_ui
        self.touches = {}  # Track active touches
        self.last_tap_time = 0
        self.last_tap_pos = (0, 0)
        self.double_tap_threshold = 0.3  # seconds
        self.touch_tolerance = responsive_ui.get_click_tolerance()
        
        # Gesture detection
        self.pinch_start_distance = 0
        self.pinch_active = False
        
    def handle_event(self, event):
        """Handle touch events."""
        if event.type == pygame.FINGERDOWN:
            return self._handle_finger_down(event)
        elif event.type == pygame.FINGERUP:
            return self._handle_finger_up(event)
        elif event.type == pygame.FINGERMOTION:
            return self._handle_finger_motion(event)
        elif event.type == pygame.MOUSEBUTTONDOWN:
            # Treat mouse as touch for desktop testing
            return self._handle_mouse_down(event)
        elif event.type == pygame.MOUSEBUTTONUP:
            return self._handle_mouse_up(event)
            
        return None
        
    def _handle_finger_down(self, event):
        """Handle finger down event."""
        finger_id = event.finger_id
        x = int(event.x * self.responsive_ui.screen_width)
        y = int(event.y * self.responsive_ui.screen_height)
        
        self.touches[finger_id] = {
            'start_pos': (x, y),
            'current_pos': (x, y),
            'start_time': time.time(),
            'moved': False
        }
        
        # Check for multi-touch gestures
        if len(self.touches) == 2:
            self._start_pinch_gesture()
            
        return {'type': 'touch_start', 'pos': (x, y), 'finger_id': finger_id}
        
    def _handle_finger_up(self, event):
        """Handle finger up event."""
        finger_id = event.finger_id
        x = int(event.x * self.responsive_ui.screen_width)
        y = int(event.y * self.responsive_ui.screen_height)
        
        if finger_id in self.touches:
            touch = self.touches[finger_id]
            duration = time.time() - touch['start_time']
            
            # Check if it's a tap (short duration, minimal movement)
            # Increased duration threshold to be more forgiving for UI interactions
            if duration < 1.0 and not touch['moved']:
                tap_result = self._handle_tap(x, y)
                del self.touches[finger_id]
                return tap_result
                
            del self.touches[finger_id]
            
        # End pinch gesture if active
        if self.pinch_active and len(self.touches) < 2:
            self.pinch_active = False
            
        return {'type': 'touch_end', 'pos': (x, y), 'finger_id': finger_id}
        
    def _handle_finger_motion(self, event):
        """Handle finger motion event."""
        finger_id = event.finger_id
        x = int(event.x * self.responsive_ui.screen_width)
        y = int(event.y * self.responsive_ui.screen_height)
        
        if finger_id in self.touches:
            touch = self.touches[finger_id]
            start_x, start_y = touch['start_pos']
            
            # Check if moved significantly
            distance = math.sqrt((x - start_x)**2 + (y - start_y)**2)
            if distance > self.touch_tolerance:
                touch['moved'] = True
                
            touch['current_pos'] = (x, y)
            
            # Handle pinch gesture
            if self.pinch_active and len(self.touches) == 2:
                self._update_pinch_gesture()
                
        return {'type': 'touch_move', 'pos': (x, y), 'finger_id': finger_id}
        
    def _handle_mouse_down(self, event):
        """Handle mouse down (for desktop testing)."""
        if event.button == 1:  # Left click
            return {'type': 'touch_start', 'pos': event.pos, 'finger_id': 'mouse', 'button': 'left'}
        elif event.button == 3:  # Right click
            return {'type': 'touch_start', 'pos': event.pos, 'finger_id': 'mouse', 'button': 'right'}
        return None

    def _handle_mouse_up(self, event):
        """Handle mouse up (for desktop testing)."""
        if event.button == 1:  # Left click
            result = self._handle_tap(event.pos[0], event.pos[1])
            if result:
                result['button'] = 'left'
            return result
        elif event.button == 3:  # Right click
            result = self._handle_tap(event.pos[0], event.pos[1])
            if result:
                result['button'] = 'right'
            return result
        return None
        
    def _handle_tap(self, x, y):
        """Handle tap/click event."""
        current_time = time.time()
        
        # Check for double tap
        if (current_time - self.last_tap_time < self.double_tap_threshold and
            math.sqrt((x - self.last_tap_pos[0])**2 + (y - self.last_tap_pos[1])**2) < self.touch_tolerance):
            self.last_tap_time = 0  # Reset to prevent triple tap
            return {'type': 'double_tap', 'pos': (x, y)}
        
        self.last_tap_time = current_time
        self.last_tap_pos = (x, y)
        
        return {'type': 'tap', 'pos': (x, y)}
        
    def _start_pinch_gesture(self):
        """Start pinch gesture detection."""
        if len(self.touches) == 2:
            touches = list(self.touches.values())
            pos1 = touches[0]['current_pos']
            pos2 = touches[1]['current_pos']
            
            self.pinch_start_distance = math.sqrt(
                (pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2
            )
            self.pinch_active = True
            
    def _update_pinch_gesture(self):
        """Update pinch gesture."""
        if len(self.touches) == 2:
            touches = list(self.touches.values())
            pos1 = touches[0]['current_pos']
            pos2 = touches[1]['current_pos']
            
            current_distance = math.sqrt(
                (pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2
            )
            
            if self.pinch_start_distance > 0:
                scale_factor = current_distance / self.pinch_start_distance
                
                # Detect zoom in/out
                if scale_factor > 1.2:
                    return {'type': 'zoom_in', 'factor': scale_factor}
                elif scale_factor < 0.8:
                    return {'type': 'zoom_out', 'factor': scale_factor}
                    
        return None
        
    def get_active_touches(self):
        """Get currently active touches."""
        return list(self.touches.values())
        
    def is_multi_touch_active(self):
        """Check if multi-touch is active."""
        return len(self.touches) > 1
        
class ResponsiveInput:
    """Unified input handler for both touch and mouse."""
    
    def __init__(self, responsive_ui):
        self.responsive_ui = responsive_ui
        self.touch_input = TouchInput(responsive_ui)
        self.is_touch_device = responsive_ui.is_touch_device()
        
    def handle_event(self, event):
        """Handle input events."""
        # Handle mouse events directly for better responsiveness
        if event.type == pygame.MOUSEBUTTONUP:
            if event.button == 1:  # Left click
                return {'type': 'tap', 'pos': event.pos, 'button': 'left'}
            elif event.button == 3:  # Right click
                return {'type': 'tap', 'pos': event.pos, 'button': 'right'}

        # Handle touch events through the touch system
        touch_result = self.touch_input.handle_event(event)
        if touch_result:
            return touch_result

        # Don't handle keyboard events through the input system - let main game handle them directly
        return None
        
    def get_click_tolerance(self):
        """Get appropriate click tolerance."""
        return self.responsive_ui.get_click_tolerance()
        
    def should_show_touch_ui(self):
        """Check if touch UI elements should be shown."""
        return self.is_touch_device or len(self.touch_input.touches) > 0
