import React from 'react'
import { useGame } from '../game/GameContext'
import './UpgradePanel.css'

const UpgradePanel: React.FC = () => {
  const { gameState, gameEngine } = useGame()

  const canPurchaseUpgrade = (upgradeId: string): boolean => {
    const upgrade = gameState.upgrades[upgradeId]
    if (!upgrade || !upgrade.unlocked || upgrade.purchased) {
      return false
    }

    return upgrade.cost.every(cost => {
      const resource = gameState.resources[cost.resourceId]
      return resource && resource.amount >= cost.amount
    })
  }

  const handlePurchase = (upgradeId: string) => {
    gameEngine.purchaseUpgrade(upgradeId)
  }

  const getEffectDescription = (effect: any): string => {
    switch (effect.type) {
      case 'production_multiplier':
        return `${effect.value}x production for ${effect.target}`
      case 'storage_increase':
        return `+${effect.value} storage ${effect.target === 'all' ? 'for all resources' : `for ${effect.target}`}`
      case 'crafting_speed':
        return `${effect.value}x crafting speed`
      case 'unlock_recipe':
        return `Unlocks ${effect.target} recipe`
      case 'unlock_building':
        return `Unlocks ${effect.target} building`
      default:
        return 'Unknown effect'
    }
  }

  const unlockedUpgrades = Object.values(gameState.upgrades).filter(upgrade => upgrade.unlocked)
  const availableUpgrades = unlockedUpgrades.filter(upgrade => !upgrade.purchased)
  const purchasedUpgrades = unlockedUpgrades.filter(upgrade => upgrade.purchased)

  return (
    <div className="upgrade-panel">
      <div className="upgrade-section">
        <h3>Available Upgrades</h3>
        
        {availableUpgrades.length === 0 ? (
          <div className="no-upgrades">
            <p>No upgrades available. Complete more achievements to unlock upgrades!</p>
          </div>
        ) : (
          <div className="upgrade-grid">
            {availableUpgrades.map(upgrade => {
              const canPurchase = canPurchaseUpgrade(upgrade.id)

              return (
                <div key={upgrade.id} className={`upgrade-card ${!canPurchase ? 'disabled' : ''}`}>
                  <div className="upgrade-header">
                    <span className="upgrade-icon">{upgrade.icon}</span>
                    <h4 className="upgrade-name">{upgrade.name}</h4>
                  </div>

                  <p className="upgrade-description">{upgrade.description}</p>

                  <div className="upgrade-effects">
                    <strong>Effects:</strong>
                    {upgrade.effects.map((effect, index) => (
                      <div key={index} className="effect-item">
                        • {getEffectDescription(effect)}
                      </div>
                    ))}
                  </div>

                  <div className="upgrade-cost">
                    <strong>Cost:</strong>
                    {upgrade.cost.map(cost => {
                      const resource = gameState.resources[cost.resourceId]
                      const hasEnough = resource && resource.amount >= cost.amount
                      return (
                        <div key={cost.resourceId} className={`cost-item ${!hasEnough ? 'insufficient' : ''}`}>
                          {resource?.icon} {cost.amount} {resource?.name}
                        </div>
                      )
                    })}
                  </div>

                  <button
                    className="purchase-button"
                    onClick={() => handlePurchase(upgrade.id)}
                    disabled={!canPurchase}
                  >
                    Purchase
                  </button>
                </div>
              )
            })}
          </div>
        )}
      </div>

      {purchasedUpgrades.length > 0 && (
        <div className="purchased-section">
          <h3>Purchased Upgrades</h3>
          <div className="purchased-grid">
            {purchasedUpgrades.map(upgrade => (
              <div key={upgrade.id} className="purchased-upgrade">
                <span className="upgrade-icon">{upgrade.icon}</span>
                <div className="upgrade-info">
                  <div className="upgrade-name">{upgrade.name}</div>
                  <div className="upgrade-effects">
                    {upgrade.effects.map((effect, index) => (
                      <div key={index} className="effect-item">
                        {getEffectDescription(effect)}
                      </div>
                    ))}
                  </div>
                </div>
                <div className="purchased-badge">✓</div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default UpgradePanel
