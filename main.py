import pygame
import sys
import traceback
from game.submarine_game import SubmarineGame

def main():
    """Main entry point for the Deep Sea Explorer game."""
    pygame.init()

    try:
        game = SubmarineGame()
        game.run()
    except Exception as e:
        print(f"Error running game: {e}")
        print("Full traceback:")
        traceback.print_exc()
    finally:
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    main()
