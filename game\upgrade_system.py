"""Comprehensive upgrade system for Deep Sea Explorer."""

import json
from .constants import *

class UpgradeManager:
    """Manages all upgrades and progression in the game."""
    
    def __init__(self):
        self.upgrades = {
            'submarine': {
                'hull': {'level': 0, 'max_level': 10},
                'engine': {'level': 0, 'max_level': 10},
                'depth_module': {'level': 0, 'max_level': 15},
                'oxygen_system': {'level': 0, 'max_level': 8},
                'navigation': {'level': 0, 'max_level': 6},
                'storage': {'level': 0, 'max_level': 12}
            },
            'grappling': {
                'hook_count': {'level': 0, 'max_level': 4},  # +1 hook per level
                'hook_speed': {'level': 0, 'max_level': 10},
                'hook_strength': {'level': 0, 'max_level': 8},
                'auto_targeting': {'level': 0, 'max_level': 5}
            },
            'dock': {
                'processing_speed': {'level': 0, 'max_level': 8},
                'storage_capacity': {'level': 0, 'max_level': 10},
                'automation': {'level': 0, 'max_level': 5},
                'efficiency': {'level': 0, 'max_level': 8},
                'crew_quarters': {'level': 0, 'max_level': 6},
                'dock_workers': {'level': 0, 'max_level': 8},
                'worker_speed': {'level': 0, 'max_level': 10},
                'worker_capacity': {'level': 0, 'max_level': 6}
            },
            'crew': {
                'diver_count': {'level': 0, 'max_level': 6},  # +1 diver per level
                'diver_speed': {'level': 0, 'max_level': 10},
                'oxygen_efficiency': {'level': 0, 'max_level': 8},
                'collection_speed': {'level': 0, 'max_level': 10},
                'ai_intelligence': {'level': 0, 'max_level': 7}
            },
            'warehouse': {
                'capacity': {'level': 0, 'max_level': 12},
                'efficiency': {'level': 0, 'max_level': 8},
                'automation': {'level': 0, 'max_level': 6},
                'security': {'level': 0, 'max_level': 5}
            },
            'collection': {
                'range_extension': {'level': 0, 'max_level': 15}  # Unified range for both divers and grappling hooks
            },
            'automation': {
                'autopilot': {'level': 0, 'max_level': 5},
                'resource_ai': {'level': 0, 'max_level': 8},
                'auto_craft': {'level': 0, 'max_level': 6},
                'efficiency': {'level': 0, 'max_level': 10}
            },
            'research': {
                'sonar': {'level': 0, 'max_level': 5},
                'deep_scan': {'level': 0, 'max_level': 4},
                'resource_detector': {'level': 0, 'max_level': 6},
                'treasure_finder': {'level': 0, 'max_level': 3}
            }
        }
        
        # Upgrade costs and effects
        self.upgrade_data = self._initialize_upgrade_data()
        
    def _initialize_upgrade_data(self):
        """Initialize upgrade costs and effects."""
        return {
            'submarine': {
                'hull': {
                    'name': 'Hull Reinforcement',
                    'description': 'Increases submarine durability and efficiency',
                    'icon': '🛡️',
                    'base_cost': {'kelp': 10, 'coral': 2},
                    'cost_multiplier': 1.5,
                    'effects': {
                        'oxygen_efficiency': 0.1,  # 10% better oxygen efficiency per level
                        'speed_bonus': 0.05        # 5% speed bonus per level
                    }
                },
                'engine': {
                    'name': 'Engine Upgrade',
                    'description': 'Improves submarine movement speed',
                    'icon': '⚡',
                    'base_cost': {'kelp': 15, 'coral': 3},
                    'cost_multiplier': 1.6,
                    'effects': {
                        'speed_multiplier': 0.15,  # 15% speed increase per level
                        'maneuverability': 0.1     # Better turning
                    }
                },
                'depth_module': {
                    'name': 'Depth Module',
                    'description': 'Allows diving to greater depths',
                    'icon': '🌊',
                    'base_cost': {'coral': 5, 'pearl': 1},
                    'cost_multiplier': 1.8,
                    'effects': {
                        'max_depth_bonus': 50,     # +50m per level
                        'pressure_resistance': 0.1  # Better deep-sea performance
                    }
                },
                'oxygen_system': {
                    'name': 'Oxygen System',
                    'description': 'Improves oxygen capacity and efficiency',
                    'icon': '💨',
                    'base_cost': {'kelp': 20, 'coral': 4},
                    'cost_multiplier': 1.4,
                    'effects': {
                        'oxygen_capacity': 100,    # +100 oxygen per level
                        'consumption_reduction': 0.1  # 10% less consumption per level
                    }
                },
                'navigation': {
                    'name': 'Navigation System',
                    'description': 'Enhanced autopilot and pathfinding',
                    'icon': '🧭',
                    'base_cost': {'coral': 8, 'pearl': 2},
                    'cost_multiplier': 2.0,
                    'effects': {
                        'autopilot_efficiency': 0.2,  # Better autopilot decisions
                        'resource_detection': 0.15    # Better resource finding
                    }
                },
                'storage': {
                    'name': 'Cargo Hold',
                    'description': 'Increases submarine storage capacity',
                    'icon': '📦',
                    'base_cost': {'kelp': 20, 'coral': 5},
                    'cost_multiplier': 1.8,
                    'effects': {
                        'storage_capacity': 10  # +10 storage per resource type per level
                    }
                }
            },
            'grappling': {
                'hook_count': {
                    'name': 'Additional Hooks',
                    'description': 'Deploy more grappling hooks simultaneously',
                    'icon': '🪝',
                    'base_cost': {'coral': 10, 'pearl': 3},
                    'cost_multiplier': 2.5,
                    'effects': {
                        'hook_count': 1  # +1 hook per level
                    }
                },

                'hook_speed': {
                    'name': 'Hook Speed',
                    'description': 'Faster hook deployment and retraction',
                    'icon': '💨',
                    'base_cost': {'kelp': 30, 'coral': 6},
                    'cost_multiplier': 1.4,
                    'effects': {
                        'speed_multiplier': 0.2  # 20% faster per level
                    }
                },
                'hook_strength': {
                    'name': 'Hook Strength',
                    'description': 'Collect multiple resources per deployment',
                    'icon': '💪',
                    'base_cost': {'coral': 15, 'pearl': 4, 'treasure': 1},
                    'cost_multiplier': 2.0,
                    'effects': {
                        'multi_collect': 0.25  # 25% chance to collect extra per level
                    }
                },
                'auto_targeting': {
                    'name': 'Auto-Targeting',
                    'description': 'Hooks automatically target valuable resources',
                    'icon': '🎯',
                    'base_cost': {'pearl': 5, 'treasure': 2},
                    'cost_multiplier': 3.0,
                    'effects': {
                        'smart_targeting': 0.2  # Better target selection per level
                    }
                }
            },
            'crew': {
                'diver_count': {
                    'name': 'Additional Divers',
                    'description': 'Recruit more crew members',
                    'icon': '👥',
                    'base_cost': {'kelp': 50, 'coral': 10, 'pearl': 2},
                    'cost_multiplier': 2.0,
                    'effects': {
                        'diver_count': 1  # +1 diver per level
                    }
                },
                'diver_speed': {
                    'name': 'Diver Training',
                    'description': 'Improves diver swimming speed',
                    'icon': '🏊',
                    'base_cost': {'kelp': 20, 'coral': 4},
                    'cost_multiplier': 1.5,
                    'effects': {
                        'speed_bonus': 0.15  # 15% faster per level
                    }
                },
                'oxygen_efficiency': {
                    'name': 'Oxygen Training',
                    'description': 'Divers use oxygen more efficiently',
                    'icon': '🫁',
                    'base_cost': {'kelp': 30, 'coral': 6},
                    'cost_multiplier': 1.6,
                    'effects': {
                        'oxygen_efficiency': 0.12  # 12% better efficiency per level
                    }
                },
                'collection_speed': {
                    'name': 'Collection Training',
                    'description': 'Faster resource collection',
                    'icon': '⚡',
                    'base_cost': {'coral': 8, 'pearl': 1},
                    'cost_multiplier': 1.4,
                    'effects': {
                        'collection_speed': 0.2  # 20% faster collection per level
                    }
                },

                'ai_intelligence': {
                    'name': 'AI Enhancement',
                    'description': 'Smarter diver decision making',
                    'icon': '🧠',
                    'base_cost': {'pearl': 3, 'treasure': 1},
                    'cost_multiplier': 2.5,
                    'effects': {
                        'ai_efficiency': 0.25  # Better AI decisions per level
                    }
                }
            },
            'collection': {
                'range_extension': {
                    'name': 'Collection Range',
                    'description': 'Increases range for both divers and grappling hooks',
                    'icon': '📏',
                    'base_cost': {'kelp': 25, 'coral': 5, 'pearl': 1},
                    'cost_multiplier': 1.5,
                    'effects': {
                        'unified_range': 35  # +35 pixels range per level for both systems
                    }
                }
            },
            'automation': {
                'autopilot': {
                    'name': 'Autopilot AI',
                    'description': 'Smarter submarine automation',
                    'icon': '🤖',
                    'base_cost': {'coral': 12, 'pearl': 3},
                    'cost_multiplier': 2.0,
                    'effects': {
                        'efficiency': 0.2  # 20% better autopilot per level
                    }
                },
                'resource_ai': {
                    'name': 'Resource AI',
                    'description': 'Better resource detection and prioritization',
                    'icon': '🔍',
                    'base_cost': {'coral': 10, 'pearl': 2},
                    'cost_multiplier': 1.8,
                    'effects': {
                        'detection_range': 0.15,  # 15% better detection per level
                        'priority_accuracy': 0.1   # Better prioritization
                    }
                },
                'auto_craft': {
                    'name': 'Auto-Crafting',
                    'description': 'Automatic crafting optimization',
                    'icon': '⚙️',
                    'base_cost': {'kelp': 40, 'coral': 8, 'pearl': 1},
                    'cost_multiplier': 1.7,
                    'effects': {
                        'craft_efficiency': 0.15  # 15% better crafting per level
                    }
                },
                'efficiency': {
                    'name': 'System Efficiency',
                    'description': 'Overall system optimization',
                    'icon': '📈',
                    'base_cost': {'pearl': 4, 'treasure': 1},
                    'cost_multiplier': 2.2,
                    'effects': {
                        'global_efficiency': 0.1  # 10% global efficiency per level
                    }
                }
            },
            'research': {
                'sonar': {
                    'name': 'Sonar System',
                    'description': 'Detect resources from greater distance',
                    'icon': '📡',
                    'base_cost': {'coral': 15, 'pearl': 5},
                    'cost_multiplier': 2.5,
                    'effects': {
                        'detection_range': 50  # +50 units detection range per level
                    }
                },
                'deep_scan': {
                    'name': 'Deep Scanner',
                    'description': 'Reveal rare resources in deep areas',
                    'icon': '🔬',
                    'base_cost': {'pearl': 8, 'treasure': 3},
                    'cost_multiplier': 3.0,
                    'effects': {
                        'rare_spawn_chance': 0.1  # 10% more rare resources per level
                    }
                },
                'resource_detector': {
                    'name': 'Resource Detector',
                    'description': 'Highlight valuable resources',
                    'icon': '💎',
                    'base_cost': {'coral': 20, 'pearl': 6, 'treasure': 1},
                    'cost_multiplier': 2.8,
                    'effects': {
                        'highlight_range': 100,    # Highlight resources within range
                        'value_indicator': 0.2     # Show resource values
                    }
                },
                'treasure_finder': {
                    'name': 'Treasure Finder',
                    'description': 'Locate legendary treasures',
                    'icon': '🏴‍☠️',
                    'base_cost': {'treasure': 5, 'pearl': 15},
                    'cost_multiplier': 4.0,
                    'effects': {
                        'treasure_spawn': 0.05,    # 5% more treasure spawns per level
                        'legendary_chance': 0.02   # 2% chance for legendary items
                    }
                }
            },
            'dock': {
                'processing_speed': {
                    'name': 'Processing Speed',
                    'description': 'Faster resource unloading',
                    'icon': '⚡',
                    'base_cost': {'kelp': 20, 'coral': 5},
                    'cost_multiplier': 1.8,
                    'effects': {
                        'unload_speed': 0.2,       # 20% faster unloading per level
                        'efficiency_bonus': 0.05   # 5% efficiency bonus per level
                    }
                },
                'storage_capacity': {
                    'name': 'Storage Expansion',
                    'description': 'Increase dock storage capacity',
                    'icon': '📦',
                    'base_cost': {'kelp': 25, 'coral': 8},
                    'cost_multiplier': 1.6,
                    'effects': {
                        'storage_bonus': 50,       # +50 storage per level
                        'batch_processing': 0.1    # 10% batch processing bonus
                    }
                },
                'automation': {
                    'name': 'Auto-Dock System',
                    'description': 'Submarine automatically docks',
                    'icon': '🤖',
                    'base_cost': {'coral': 15, 'pearl': 3},
                    'cost_multiplier': 2.2,
                    'effects': {
                        'auto_dock': 1.0,          # Enable auto-docking
                        'smart_scheduling': 0.15   # 15% better scheduling per level
                    }
                },
                'efficiency': {
                    'name': 'Resource Efficiency',
                    'description': 'Better resource yields from unloading',
                    'icon': '💰',
                    'base_cost': {'coral': 12, 'pearl': 2},
                    'cost_multiplier': 1.9,
                    'effects': {
                        'resource_multiplier': 0.1, # 10% more resources per level
                        'bonus_chance': 0.05        # 5% chance for bonus resources
                    }
                },
                'crew_quarters': {
                    'name': 'Crew Quarters',
                    'description': 'Support more crew members',
                    'icon': '👥',
                    'base_cost': {'kelp': 30, 'coral': 10, 'pearl': 1},
                    'cost_multiplier': 2.0,
                    'effects': {
                        'crew_capacity': 1,        # +1 crew member per level
                        'crew_efficiency': 0.1     # 10% crew efficiency per level
                    }
                },
                'dock_workers': {
                    'name': 'Dock Workers',
                    'description': 'Hire additional dock workers for faster unloading',
                    'icon': '👷',
                    'base_cost': {'kelp': 25, 'coral': 8},
                    'cost_multiplier': 2.2,
                    'effects': {
                        'worker_count': 1,         # +1 dock worker per level
                        'unload_efficiency': 0.15  # 15% faster unloading per level
                    }
                },
                'worker_speed': {
                    'name': 'Worker Training',
                    'description': 'Train dock workers to move faster',
                    'icon': '🏃',
                    'base_cost': {'kelp': 15, 'coral': 5},
                    'cost_multiplier': 1.7,
                    'effects': {
                        'movement_speed': 0.2,     # 20% faster movement per level
                        'work_speed': 0.15         # 15% faster work per level
                    }
                },
                'worker_capacity': {
                    'name': 'Heavy Lifting',
                    'description': 'Workers can carry more resources per trip',
                    'icon': '💪',
                    'base_cost': {'coral': 10, 'pearl': 2},
                    'cost_multiplier': 1.9,
                    'effects': {
                        'carry_capacity': 1,       # +1 resource per trip per level
                        'efficiency_bonus': 0.1    # 10% efficiency bonus per level
                    }
                }
            },
            'warehouse': {
                'capacity': {
                    'name': 'Storage Expansion',
                    'description': 'Increase warehouse storage capacity',
                    'icon': '📦',
                    'base_cost': {'kelp': 50, 'coral': 25},
                    'cost_multiplier': 1.5,
                    'effects': {
                        'storage_multiplier': 0.5,  # 50% more storage per level
                        'organization': 0.1          # 10% better organization per level
                    }
                },
                'efficiency': {
                    'name': 'Processing Systems',
                    'description': 'Faster resource processing and sorting',
                    'icon': '⚙️',
                    'base_cost': {'coral': 30, 'pearl': 15},
                    'cost_multiplier': 1.4,
                    'effects': {
                        'processing_speed': 0.2,    # 20% faster processing per level
                        'yield_bonus': 0.05          # 5% resource yield bonus per level
                    }
                },
                'automation': {
                    'name': 'Automated Systems',
                    'description': 'Automated sorting and inventory management',
                    'icon': '🤖',
                    'base_cost': {'pearl': 20, 'treasure': 5},
                    'cost_multiplier': 1.6,
                    'effects': {
                        'auto_sorting': 0.25,       # 25% auto-sorting efficiency per level
                        'maintenance_reduction': 0.1 # 10% less maintenance per level
                    }
                },
                'security': {
                    'name': 'Security Systems',
                    'description': 'Protect stored resources from loss',
                    'icon': '🔒',
                    'base_cost': {'treasure': 10, 'pearl': 25},
                    'cost_multiplier': 1.3,
                    'effects': {
                        'loss_prevention': 0.2,     # 20% less resource loss per level
                        'theft_protection': 0.15    # 15% theft protection per level
                    }
                }
            }
        }
        
    def get_upgrade_cost(self, category, upgrade_name):
        """Calculate the cost for the next level of an upgrade."""
        current_level = self.upgrades[category][upgrade_name]['level']
        max_level = self.upgrades[category][upgrade_name]['max_level']
        
        if current_level >= max_level:
            return None  # Already at max level
            
        upgrade_info = self.upgrade_data[category][upgrade_name]
        base_cost = upgrade_info['base_cost']
        multiplier = upgrade_info['cost_multiplier']
        
        # Calculate cost with exponential scaling
        cost = {}
        for resource, amount in base_cost.items():
            cost[resource] = int(amount * (multiplier ** current_level))
            
        return cost
        
    def can_afford_upgrade(self, category, upgrade_name, resources):
        """Check if player can afford an upgrade."""
        cost = self.get_upgrade_cost(category, upgrade_name)
        if cost is None:
            return False
            
        for resource, amount in cost.items():
            if resources.get(resource, 0) < amount:
                return False
                
        return True
        
    def purchase_upgrade(self, category, upgrade_name, resources):
        """Purchase an upgrade if affordable."""
        if not self.can_afford_upgrade(category, upgrade_name, resources):
            return False
            
        cost = self.get_upgrade_cost(category, upgrade_name)
        
        # Deduct resources
        for resource, amount in cost.items():
            resources[resource] -= amount
            
        # Increase upgrade level
        self.upgrades[category][upgrade_name]['level'] += 1
        
        return True
        
    def get_upgrade_effect(self, category, upgrade_name):
        """Get the current effect of an upgrade."""
        level = self.upgrades[category][upgrade_name]['level']
        if level == 0:
            return {}
            
        upgrade_info = self.upgrade_data[category][upgrade_name]
        effects = {}
        
        for effect_name, effect_value in upgrade_info['effects'].items():
            if effect_name in ['hook_count', 'diver_count']:
                # Discrete effects
                effects[effect_name] = effect_value * level
            else:
                # Multiplicative effects
                effects[effect_name] = effect_value * level
                
        return effects
        
    def get_all_effects(self):
        """Get all current upgrade effects."""
        all_effects = {}
        
        for category in self.upgrades:
            for upgrade_name in self.upgrades[category]:
                effects = self.get_upgrade_effect(category, upgrade_name)
                for effect_name, value in effects.items():
                    if effect_name in all_effects:
                        all_effects[effect_name] += value
                    else:
                        all_effects[effect_name] = value
                        
        return all_effects
        
    def get_upgrade_progress(self, category, upgrade_name):
        """Get upgrade progress as a percentage."""
        current = self.upgrades[category][upgrade_name]['level']
        maximum = self.upgrades[category][upgrade_name]['max_level']
        return current / maximum if maximum > 0 else 1.0
        
    def save_upgrades(self, filename):
        """Save upgrade progress to file."""
        try:
            with open(filename, 'w') as f:
                json.dump(self.upgrades, f, indent=2)
            return True
        except:
            return False
            
    def load_upgrades(self, filename):
        """Load upgrade progress from file."""
        try:
            with open(filename, 'r') as f:
                self.upgrades = json.load(f)
            return True
        except:
            return False
