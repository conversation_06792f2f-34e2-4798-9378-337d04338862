"""Modern UI system with improved visuals and user experience."""

import pygame
import math
from .constants import *

class ModernUI:
    """Modern UI system with gradients, shadows, and smooth animations."""
    
    def __init__(self, screen_width, screen_height):
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.is_mobile = screen_width <= 800 or screen_height <= 600
        
        # Scale factors
        self.scale_factor = min(screen_width / DEFAULT_SCREEN_WIDTH, 
                               screen_height / DEFAULT_SCREEN_HEIGHT)
        self.ui_scale = max(0.7, min(1.3, self.scale_factor))
        
        # Modern color palette
        self.colors = {
            'primary': (64, 128, 255),      # Blue
            'secondary': (128, 64, 255),    # Purple
            'success': (64, 255, 128),      # <PERSON>
            'warning': (255, 192, 64),      # Orange
            'danger': (255, 64, 64),        # Red
            'dark': (32, 32, 48),           # Dark blue-gray
            'darker': (16, 16, 24),         # Darker
            'light': (240, 240, 255),       # Light
            'accent': (255, 215, 0),        # Gold
            'background': (20, 25, 40),     # Deep blue background
            'panel': (40, 45, 65),          # Panel background
            'border': (80, 90, 120),        # Border color
            'text': (220, 225, 240),        # Text color
            'text_dim': (160, 170, 190)     # Dimmed text
        }
        
        # Fonts - Much larger for better readability
        pygame.font.init()
        base_font_scale = max(1.2, self.ui_scale)  # Ensure minimum readable size

        # Add accessibility scaling option
        accessibility_scale = 1.0  # Can be increased for even larger text
        if self.is_mobile:
            accessibility_scale = 1.2  # Slightly larger on mobile

        # Extra large mode for maximum readability
        self.extra_large_mode = False  # Can be toggled

        final_scale = base_font_scale * accessibility_scale

        self.fonts = {
            'title': pygame.font.Font(None, int(48 * final_scale)),
            'heading': pygame.font.Font(None, int(36 * final_scale)),
            'body': pygame.font.Font(None, int(28 * final_scale)),
            'small': pygame.font.Font(None, int(24 * final_scale)),
            'tiny': pygame.font.Font(None, int(20 * final_scale))
        }
        
        # Animation states
        self.animations = {}
        self.hover_states = {}
        
        # Layout calculations
        self._calculate_layout()
        
    def _calculate_layout(self):
        """Calculate modern responsive layout that fits on screen."""
        margin = int(15 * self.ui_scale)

        # Adaptive panel width based on screen size
        if self.screen_width < 800:
            # Small screens - use most of the width
            panel_width = int(self.screen_width * 0.45)
        elif self.screen_width < 1200:
            # Medium screens - balanced approach
            panel_width = int(min(350, self.screen_width * 0.32))
        else:
            # Large screens - can afford bigger panels
            panel_width = int(min(400, self.screen_width * 0.30))
        
        # Calculate panel heights that fit on screen
        available_height = self.screen_height - 2 * margin - 100  # Leave space for instructions

        # Adaptive panel heights
        if available_height < 600:
            # Small screens - compact panels
            resource_height = int(min(180, available_height * 0.3))
            crew_height = int(min(140, available_height * 0.25))
            grappling_height = int(min(120, available_height * 0.2))
        else:
            # Larger screens - more spacious panels
            resource_height = int(min(220, available_height * 0.32))
            crew_height = int(min(180, available_height * 0.28))
            grappling_height = int(min(160, available_height * 0.25))

        # Calculate Y positions to fit everything
        resources_y = margin
        crew_y = resources_y + resource_height + margin
        grappling_y = crew_y + crew_height + margin

        # Ensure grappling panel fits on screen
        if grappling_y + grappling_height > self.screen_height - 100:
            # Adjust heights to fit
            total_needed = resource_height + crew_height + grappling_height + 3 * margin + 100
            scale_factor = (self.screen_height - 100) / total_needed

            resource_height = int(resource_height * scale_factor)
            crew_height = int(crew_height * scale_factor)
            grappling_height = int(grappling_height * scale_factor)

            # Recalculate positions
            crew_y = resources_y + resource_height + margin
            grappling_y = crew_y + crew_height + margin

        # Main panels - Adaptive sizing
        self.panels = {
            'resources': {
                'x': self.screen_width - panel_width - margin,
                'y': resources_y,
                'width': panel_width,
                'height': resource_height
            },
            'crew': {
                'x': self.screen_width - panel_width - margin,
                'y': crew_y,
                'width': panel_width,
                'height': crew_height
            },
            'grappling': {
                'x': self.screen_width - panel_width - margin,
                'y': grappling_y,
                'width': panel_width,
                'height': grappling_height
            },
            'upgrades': {
                'x': margin,
                'y': max(margin, self.screen_height - int(200 * self.ui_scale) - margin),
                'width': int(min(400, self.screen_width * 0.4)),  # Adaptive width
                'height': int(min(200, self.screen_height * 0.3))  # Adaptive height
            }
        }
        
        # Automation buttons - Adaptive sizing
        button_size = int(min(70, max(50, 70 * self.ui_scale)))  # Adaptive button size
        button_spacing = int(8 * self.ui_scale)

        if self.is_mobile or self.screen_width < 1000:
            # Mobile or small screens: horizontal at top
            total_button_width = 4 * button_size + 3 * button_spacing
            if total_button_width > self.screen_width - 2 * margin:
                # Buttons too wide, make them smaller
                button_size = int((self.screen_width - 2 * margin - 3 * button_spacing) / 4)

            start_x = margin
            start_y = margin
            self.automation_buttons = []
            for i in range(4):
                x = start_x + i * (button_size + button_spacing)
                y = start_y
                self.automation_buttons.append({
                    'rect': pygame.Rect(x, y, button_size, int(button_size * 0.7)),
                    'index': i
                })
        else:
            # Desktop: try vertical on right, but check if it fits
            start_x = self.screen_width - button_size - margin
            start_y = self.panels['grappling']['y'] + self.panels['grappling']['height'] + margin

            # Check if vertical buttons fit
            total_button_height = 4 * int(button_size * 0.7) + 3 * button_spacing
            if start_y + total_button_height > self.screen_height - 50:
                # Doesn't fit vertically, use horizontal at bottom
                start_x = margin
                start_y = self.screen_height - int(button_size * 0.7) - margin

                # Check if horizontal fits
                total_button_width = 4 * button_size + 3 * button_spacing
                if total_button_width > self.screen_width - 2 * margin:
                    button_size = int((self.screen_width - 2 * margin - 3 * button_spacing) / 4)

                self.automation_buttons = []
                for i in range(4):
                    x = start_x + i * (button_size + button_spacing)
                    y = start_y
                    self.automation_buttons.append({
                        'rect': pygame.Rect(x, y, button_size, int(button_size * 0.7)),
                        'index': i
                    })
            else:
                # Fits vertically
                self.automation_buttons = []
                for i in range(4):
                    x = start_x
                    y = start_y + i * (int(button_size * 0.7) + button_spacing)
                    self.automation_buttons.append({
                        'rect': pygame.Rect(x, y, button_size, int(button_size * 0.7)),
                        'index': i
                    })
                
    def create_gradient_surface(self, width, height, color1, color2, vertical=True):
        """Create a gradient surface."""
        surface = pygame.Surface((width, height))
        
        if vertical:
            for y in range(height):
                ratio = y / height
                color = [
                    int(color1[i] * (1 - ratio) + color2[i] * ratio)
                    for i in range(3)
                ]
                pygame.draw.line(surface, color, (0, y), (width, y))
        else:
            for x in range(width):
                ratio = x / width
                color = [
                    int(color1[i] * (1 - ratio) + color2[i] * ratio)
                    for i in range(3)
                ]
                pygame.draw.line(surface, color, (x, 0), (x, height))
                
        return surface
        
    def draw_modern_panel(self, screen, panel_info, title, icon="", content_func=None):
        """Draw a modern panel with gradient background and shadow."""
        x, y, width, height = panel_info['x'], panel_info['y'], panel_info['width'], panel_info['height']
        
        # Shadow
        shadow_offset = int(4 * self.ui_scale)
        shadow_surface = pygame.Surface((width, height))
        shadow_surface.set_alpha(60)
        shadow_surface.fill((0, 0, 0))
        screen.blit(shadow_surface, (x + shadow_offset, y + shadow_offset))
        
        # Main panel background with gradient
        gradient = self.create_gradient_surface(width, height, 
                                              self.colors['panel'], 
                                              self.colors['darker'])
        gradient.set_alpha(240)
        screen.blit(gradient, (x, y))
        
        # Border with glow effect
        border_color = self.colors['border']
        pygame.draw.rect(screen, border_color, (x, y, width, height), 2)
        
        # Inner glow
        inner_rect = pygame.Rect(x + 1, y + 1, width - 2, height - 2)
        pygame.draw.rect(screen, (*self.colors['primary'], 30), inner_rect, 1)
        
        # Title bar - taller for larger text
        title_height = int(50 * self.ui_scale)  # Taller title bar
        title_gradient = self.create_gradient_surface(width, title_height,
                                                    self.colors['primary'],
                                                    self.colors['secondary'])
        title_gradient.set_alpha(180)
        screen.blit(title_gradient, (x, y))

        # Title text with more padding
        title_text = f"{icon} {title}" if icon else title
        title_surface = self.fonts['heading'].render(title_text, True, self.colors['light'])
        title_x = x + int(15 * self.ui_scale)  # More padding
        title_y = y + int(12 * self.ui_scale)  # More padding
        screen.blit(title_surface, (title_x, title_y))

        # Content area with more spacing
        if content_func:
            content_y = y + title_height + int(15 * self.ui_scale)  # More spacing
            content_func(screen, x + int(20 * self.ui_scale), content_y,   # More padding
                        width - int(40 * self.ui_scale))
                        
    def draw_modern_button(self, screen, rect, text, enabled=True, style='primary', icon=""):
        """Draw a modern button with hover effects."""
        button_id = f"{rect.x}_{rect.y}_{text}"
        
        # Hover animation
        if button_id not in self.hover_states:
            self.hover_states[button_id] = 0.0
            
        # Check if mouse is over button (simplified)
        mouse_pos = pygame.mouse.get_pos()
        is_hovered = rect.collidepoint(mouse_pos)
        
        # Animate hover state
        target_hover = 1.0 if is_hovered else 0.0
        self.hover_states[button_id] += (target_hover - self.hover_states[button_id]) * 0.1
        hover_amount = self.hover_states[button_id]
        
        # Button colors based on style and state
        if style == 'primary':
            base_color = self.colors['primary']
            hover_color = self.colors['secondary']
        elif style == 'success':
            base_color = self.colors['success']
            hover_color = (80, 255, 150)
        elif style == 'warning':
            base_color = self.colors['warning']
            hover_color = (255, 210, 80)
        elif style == 'danger':
            base_color = self.colors['danger']
            hover_color = (255, 80, 80)
        else:
            base_color = self.colors['dark']
            hover_color = self.colors['border']
            
        if not enabled:
            base_color = self.colors['dark']
            hover_color = base_color
            
        # Interpolate colors
        current_color = [
            int(base_color[i] * (1 - hover_amount) + hover_color[i] * hover_amount)
            for i in range(3)
        ]
        
        # Shadow
        shadow_offset = int(2 * self.ui_scale)
        shadow_rect = rect.copy()
        shadow_rect.x += shadow_offset
        shadow_rect.y += shadow_offset
        shadow_surface = pygame.Surface((rect.width, rect.height))
        shadow_surface.set_alpha(80)
        shadow_surface.fill((0, 0, 0))
        screen.blit(shadow_surface, shadow_rect)
        
        # Button background
        pygame.draw.rect(screen, current_color, rect)
        pygame.draw.rect(screen, self.colors['border'], rect, 2)
        
        # Button text
        button_text = f"{icon} {text}" if icon else text
        text_color = self.colors['light'] if enabled else self.colors['text_dim']
        
        # Multi-line text support with larger font
        button_font = self.fonts['body']  # Use larger body font instead of small
        words = button_text.split()
        lines = []
        current_line = ""

        for word in words:
            test_line = current_line + (" " if current_line else "") + word
            if button_font.size(test_line)[0] <= rect.width - int(16 * self.ui_scale):
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        if current_line:
            lines.append(current_line)

        # Draw text lines
        total_height = len(lines) * button_font.get_height()
        start_y = rect.centery - total_height // 2

        for i, line in enumerate(lines):
            text_surface = button_font.render(line, True, text_color)
            text_rect = text_surface.get_rect(centerx=rect.centerx,
                                            y=start_y + i * button_font.get_height())
            screen.blit(text_surface, text_rect)
            
    def draw_progress_bar(self, screen, x, y, width, height, progress, color=None, bg_color=None):
        """Draw a modern progress bar with glow effects."""
        if color is None:
            color = self.colors['success']
        if bg_color is None:
            bg_color = self.colors['dark']
            
        # Background
        bg_rect = pygame.Rect(x, y, width, height)
        pygame.draw.rect(screen, bg_color, bg_rect)
        pygame.draw.rect(screen, self.colors['border'], bg_rect, 1)
        
        # Progress fill
        if progress > 0:
            fill_width = int(width * min(1.0, max(0.0, progress)))
            fill_rect = pygame.Rect(x, y, fill_width, height)
            
            # Gradient fill
            if fill_width > 0:
                gradient = self.create_gradient_surface(fill_width, height, color, 
                                                      tuple(min(255, c + 40) for c in color))
                screen.blit(gradient, (x, y))
                
            # Glow effect
            glow_color = (*color, 100)
            pygame.draw.rect(screen, color, fill_rect, 1)
            
    def draw_stat_display(self, screen, x, y, label, value, icon="", color=None):
        """Draw a stat display with icon and value."""
        if color is None:
            color = self.colors['text']

        # Use larger fonts for better readability
        icon_font = self.fonts['heading']  # Larger icon
        text_font = self.fonts['body']     # Larger text

        # Icon
        if icon:
            icon_surface = icon_font.render(icon, True, color)
            screen.blit(icon_surface, (x, y))
            x += icon_surface.get_width() + int(12 * self.ui_scale)  # More spacing

        # Label
        label_surface = text_font.render(f"{label}:", True, self.colors['text_dim'])
        screen.blit(label_surface, (x, y))
        x += label_surface.get_width() + int(12 * self.ui_scale)  # More spacing

        # Value
        value_surface = text_font.render(str(value), True, color)
        screen.blit(value_surface, (x, y))

        return x + value_surface.get_width()
        
    def get_touch_zones(self):
        """Get touch zones for modern UI."""
        zones = {}

        # Automation buttons
        button_names = ['autopilot', 'crew_ai', 'auto_craft', 'mouse_follow']
        for i, button in enumerate(self.automation_buttons):
            zones[button_names[i]] = button['rect']

        return zones

    def check_layout_bounds(self):
        """Check if UI elements fit within screen bounds."""
        issues = []

        # Check panels
        for panel_name, panel in self.panels.items():
            if panel['x'] + panel['width'] > self.screen_width:
                issues.append(f"{panel_name} panel extends beyond right edge")
            if panel['y'] + panel['height'] > self.screen_height:
                issues.append(f"{panel_name} panel extends beyond bottom edge")

        # Check automation buttons
        for i, button in enumerate(self.automation_buttons):
            rect = button['rect']
            if rect.right > self.screen_width:
                issues.append(f"Automation button {i} extends beyond right edge")
            if rect.bottom > self.screen_height:
                issues.append(f"Automation button {i} extends beyond bottom edge")

        return issues
