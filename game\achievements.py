"""Achievement and progression system."""

import pygame
import time
from .constants import *

class Achievement:
    """Individual achievement."""
    
    def __init__(self, id, name, description, icon, condition_func, reward=None):
        self.id = id
        self.name = name
        self.description = description
        self.icon = icon
        self.condition_func = condition_func
        self.reward = reward or {}
        self.unlocked = False
        self.unlock_time = None
        
    def check_condition(self, game_state):
        """Check if achievement condition is met."""
        if not self.unlocked and self.condition_func(game_state):
            self.unlocked = True
            self.unlock_time = time.time()
            return True
        return False

class AchievementManager:
    """Manages achievements and progression."""
    
    def __init__(self):
        self.achievements = {}
        self.notifications = []
        self.notification_duration = 3.0  # seconds
        
        self._initialize_achievements()
        
    def _initialize_achievements(self):
        """Initialize all achievements."""
        achievements_data = [
            # Resource Collection
            {
                'id': 'first_kelp',
                'name': 'Seaweed Harvester',
                'description': 'Collect your first kelp',
                'icon': '🌿',
                'condition': lambda gs: gs['resources']['kelp'] >= 1,
                'reward': {'oxygen': 50}
            },
            {
                'id': 'kelp_collector',
                'name': 'Kelp Forest Explorer',
                'description': 'Collect 100 kelp',
                'icon': '🌿',
                'condition': lambda gs: gs['resources']['kelp'] >= 100,
                'reward': {'oxygen': 200}
            },
            {
                'id': 'first_treasure',
                'name': 'Treasure Hunter',
                'description': 'Find your first treasure',
                'icon': '💎',
                'condition': lambda gs: gs['resources']['treasure'] >= 1,
                'reward': {'kelp': 50, 'coral': 10}
            },
            
            # Depth Exploration
            {
                'id': 'deep_diver',
                'name': 'Deep Sea Explorer',
                'description': 'Reach 200m depth',
                'icon': '🌊',
                'condition': lambda gs: gs['submarine']['depth'] >= 200,
                'reward': {'coral': 5}
            },
            {
                'id': 'abyss_explorer',
                'name': 'Abyss Walker',
                'description': 'Reach 400m depth',
                'icon': '🕳️',
                'condition': lambda gs: gs['submarine']['depth'] >= 400,
                'reward': {'pearl': 3, 'treasure': 1}
            },
            
            # Upgrade Milestones
            {
                'id': 'first_upgrade',
                'name': 'Upgrader',
                'description': 'Purchase your first upgrade',
                'icon': '⬆️',
                'condition': lambda gs: gs['total_upgrades'] >= 1,
                'reward': {'kelp': 25}
            },
            {
                'id': 'upgrade_master',
                'name': 'Upgrade Master',
                'description': 'Purchase 10 upgrades',
                'icon': '🔧',
                'condition': lambda gs: gs['total_upgrades'] >= 10,
                'reward': {'coral': 20, 'pearl': 5}
            },
            
            # Crew Management
            {
                'id': 'crew_leader',
                'name': 'Crew Leader',
                'description': 'Have 5 divers working',
                'icon': '👥',
                'condition': lambda gs: len(gs['divers']) >= 5,
                'reward': {'kelp': 100}
            },
            
            # Automation
            {
                'id': 'automation_master',
                'name': 'Automation Expert',
                'description': 'Enable all automation systems',
                'icon': '🤖',
                'condition': lambda gs: all([gs['autopilot'], gs['crew_ai'], gs['auto_craft']]),
                'reward': {'coral': 15}
            },
            
            # Collection Efficiency
            {
                'id': 'efficient_collector',
                'name': 'Efficient Collector',
                'description': 'Collect 50 resources in one dive',
                'icon': '⚡',
                'condition': lambda gs: gs['resources_this_dive'] >= 50,
                'reward': {'pearl': 2}
            },
            
            # Time-based
            {
                'id': 'dedicated_explorer',
                'name': 'Dedicated Explorer',
                'description': 'Play for 10 minutes',
                'icon': '⏰',
                'condition': lambda gs: gs['play_time'] >= 600,  # 10 minutes
                'reward': {'treasure': 1}
            },

            # Dock-related achievements
            {
                'id': 'first_dock',
                'name': 'Harbor Master',
                'description': 'Use the dock for the first time',
                'icon': '🚢',
                'condition': lambda gs: gs.get('dock_uses', 0) >= 1,
                'reward': {'kelp': 25, 'coral': 5}
            },
            {
                'id': 'dock_regular',
                'name': 'Regular Customer',
                'description': 'Use the dock 10 times',
                'icon': '⚓',
                'condition': lambda gs: gs.get('dock_uses', 0) >= 10,
                'reward': {'coral': 50, 'pearl': 10}
            },
            {
                'id': 'dock_master',
                'name': 'Dock Master',
                'description': 'Use the dock 50 times',
                'icon': '🏗️',
                'condition': lambda gs: gs.get('dock_uses', 0) >= 50,
                'reward': {'pearl': 25, 'treasure': 5}
            }
        ]
        
        for data in achievements_data:
            achievement = Achievement(
                data['id'],
                data['name'],
                data['description'],
                data['icon'],
                data['condition'],
                data.get('reward', {})
            )
            self.achievements[data['id']] = achievement
            
    def update(self, game_state):
        """Update achievements and check for new unlocks."""
        newly_unlocked = []
        
        for achievement in self.achievements.values():
            if achievement.check_condition(game_state):
                newly_unlocked.append(achievement)
                self._add_notification(achievement)
                
        return newly_unlocked
        
    def _add_notification(self, achievement):
        """Add achievement notification."""
        notification = {
            'achievement': achievement,
            'start_time': time.time(),
            'animation_progress': 0.0
        }
        self.notifications.append(notification)
        
    def update_notifications(self, dt):
        """Update achievement notifications."""
        current_time = time.time()
        
        # Update existing notifications
        for notification in self.notifications[:]:
            elapsed = current_time - notification['start_time']
            
            if elapsed > self.notification_duration:
                self.notifications.remove(notification)
            else:
                # Update animation progress
                progress = elapsed / self.notification_duration
                if progress < 0.2:
                    # Slide in
                    notification['animation_progress'] = progress / 0.2
                elif progress > 0.8:
                    # Slide out
                    notification['animation_progress'] = 1.0 - ((progress - 0.8) / 0.2)
                else:
                    # Stay visible
                    notification['animation_progress'] = 1.0
                    
    def draw_notifications(self, screen, modern_ui):
        """Draw achievement notifications."""
        y_offset = 20
        
        for notification in self.notifications:
            achievement = notification['achievement']
            progress = notification['animation_progress']
            
            # Calculate position with slide animation
            notification_width = int(300 * modern_ui.ui_scale)
            notification_height = int(80 * modern_ui.ui_scale)
            
            x = int(screen.get_width() - notification_width * progress)
            y = y_offset
            
            # Background with gradient
            notification_rect = pygame.Rect(x, y, notification_width, notification_height)
            
            # Gradient background
            if hasattr(modern_ui, 'create_gradient_surface'):
                # Use modern UI gradient if available
                gradient = modern_ui.create_gradient_surface(
                    notification_width, notification_height,
                    modern_ui.colors['success'], modern_ui.colors['info']
                )
                gradient.set_alpha(int(220 * progress))
                screen.blit(gradient, (x, y))
            else:
                # Fallback to simple colored background
                bg_surface = pygame.Surface((notification_width, notification_height))
                bg_surface.set_alpha(int(220 * progress))
                bg_surface.fill(modern_ui.colors['success'])
                screen.blit(bg_surface, (x, y))
            
            # Border
            pygame.draw.rect(screen, modern_ui.colors['light'], notification_rect, 2)
            
            # Achievement icon
            icon_surface = modern_ui.fonts['title'].render(achievement.icon, True, modern_ui.colors['light'])
            screen.blit(icon_surface, (x + 15, y + 15))
            
            # Achievement text
            title_surface = modern_ui.fonts['heading'].render("Achievement Unlocked!", True, modern_ui.colors['light'])
            screen.blit(title_surface, (x + 60, y + 10))
            
            name_surface = modern_ui.fonts['body'].render(achievement.name, True, modern_ui.colors['accent'])
            screen.blit(name_surface, (x + 60, y + 30))
            
            desc_surface = modern_ui.fonts['small'].render(achievement.description, True, modern_ui.colors['text_dim'])
            screen.blit(desc_surface, (x + 60, y + 50))
            
            y_offset += notification_height + 10
            
    def get_progress_stats(self):
        """Get overall progress statistics."""
        total_achievements = len(self.achievements)
        unlocked_achievements = sum(1 for a in self.achievements.values() if a.unlocked)
        
        return {
            'total': total_achievements,
            'unlocked': unlocked_achievements,
            'percentage': (unlocked_achievements / total_achievements) * 100 if total_achievements > 0 else 0
        }
        
    def get_unlocked_achievements(self):
        """Get list of unlocked achievements."""
        return [a for a in self.achievements.values() if a.unlocked]
        
    def get_locked_achievements(self):
        """Get list of locked achievements."""
        return [a for a in self.achievements.values() if not a.unlocked]
