"""Game constants and configuration."""

import pygame

# Screen settings - will be dynamically set based on device
DEFAULT_SCREEN_WIDTH = 1200
DEFAULT_SCREEN_HEIGHT = 800
MIN_SCREEN_WIDTH = 480
MIN_SCREEN_HEIGHT = 320
FPS = 60

# Dynamic screen dimensions (set at runtime)
SCREEN_WIDTH = DEFAULT_SCREEN_WIDTH
SCREEN_HEIGHT = DEFAULT_SCREEN_HEIGHT

def get_screen_info():
    """Get available screen information."""
    pygame.init()
    info = pygame.display.Info()
    return info.current_w, info.current_h

def set_responsive_dimensions():
    """Set responsive screen dimensions based on device."""
    global SCREEN_WIDTH, SCREEN_HEIGHT

    try:
        available_width, available_height = get_screen_info()

        # For mobile/small screens, use most of available space
        if available_width <= 800 or available_height <= 600:
            SCREEN_WIDTH = max(MIN_SCREEN_WIDTH, min(available_width - 50, 800))
            SCREEN_HEIGHT = max(MIN_SCREEN_HEIGHT, min(available_height - 100, 600))
        else:
            # For desktop, use default or scale down if needed
            SCREEN_WIDTH = min(DEFAULT_SCREEN_WIDTH, available_width - 100)
            SCREEN_HEIGHT = min(DEFAULT_SCREEN_HEIGHT, available_height - 100)

    except:
        # Fallback to default if detection fails
        SCREEN_WIDTH = DEFAULT_SCREEN_WIDTH
        SCREEN_HEIGHT = DEFAULT_SCREEN_HEIGHT

    return SCREEN_WIDTH, SCREEN_HEIGHT

# Colors
SURFACE_BLUE = (135, 206, 235)  # Light blue for surface
DEEP_BLUE = (0, 0, 139)         # Dark blue for deep water
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
YELLOW = (255, 255, 0)
GREEN = (0, 255, 0)
RED = (255, 0, 0)
GOLD = (255, 215, 0)
SILVER = (192, 192, 192)
ORANGE = (255, 165, 0)

# Sky and atmosphere colors
SKY_BLUE = (135, 206, 250)      # Sky blue
SKY_LIGHT = (176, 224, 255)     # Light sky blue
CLOUD_WHITE = (255, 255, 255)   # Cloud white
CLOUD_GRAY = (220, 220, 220)    # Cloud gray
HORIZON_BLUE = (100, 149, 237)  # Horizon blue

# UI Layout settings - Optimized for larger playable area
UI_PANEL_WIDTH = 180  # Width of side UI panels (reduced from 300)
UI_TOP_HEIGHT = 40    # Height of top UI bar (reduced from 60)
UI_BOTTOM_HEIGHT = 50 # Height of bottom UI bar (reduced from 80)
UI_MARGIN = 8         # Margin between UI elements (reduced from 10)

# Game settings
SUBMARINE_SPEED = 3
DIVER_SPEED = 1.2  # Reduced from 2 to make divers slower initially
MAX_DEPTH = 500
SURFACE_LEVEL = 100

# World settings
WORLD_WIDTH = 4000   # Much larger than screen width
WORLD_HEIGHT = 2000  # Much larger than screen height

# Camera settings
DEFAULT_ZOOM = 1.0
MIN_ZOOM = 0.5  # Reasonable minimum zoom
MAX_ZOOM = 2.0  # Reasonable maximum zoom
ZOOM_SPEED = 0.1  # Good zoom speed for control
CAMERA_FOLLOW_SPEED = 0.1  # How quickly camera follows submarine
FREE_CAM_RANGE = 1000  # Maximum distance camera can move from submarine

# Resource types and their properties
RESOURCE_TYPES = {
    'kelp': {'color': GREEN, 'value': 1, 'depth_min': 0, 'base_size': 10},
    'coral': {'color': ORANGE, 'value': 2, 'depth_min': 20, 'base_size': 12},
    'pearl': {'color': WHITE, 'value': 5, 'depth_min': 50, 'base_size': 8},
    'treasure': {'color': GOLD, 'value': 10, 'depth_min': 100, 'base_size': 15}
}

# Crafting recipes
RECIPES = {
    'oxygen_tank': {'kelp': 5, 'output': {'oxygen': 100}},
    'depth_upgrade': {'coral': 3, 'pearl': 1, 'output': {'max_depth': 50}},
    'speed_upgrade': {'kelp': 10, 'treasure': 1, 'output': {'speed': 1}}
}
