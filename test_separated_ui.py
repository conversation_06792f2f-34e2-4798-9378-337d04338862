"""Test script to verify the separated UI system works correctly."""

import pygame
import sys
from game.separated_ui import SeparatedUI
from game.ui_containers import SeparatedUIManager

def test_ui_layout():
    """Test the UI layout at different screen sizes."""
    pygame.init()
    
    # Test different screen sizes
    test_sizes = [
        (800, 600),   # Small screen
        (1200, 800),  # Medium screen  
        (1920, 1080), # Large screen
        (480, 320)    # Very small screen
    ]
    
    for width, height in test_sizes:
        print(f"\nTesting screen size: {width}x{height}")
        
        # Create UI manager
        ui_manager = SeparatedUIManager(width, height)
        
        # Get regions
        playable = ui_manager.get_playable_area()
        
        print(f"  Playable area: {playable['width']}x{playable['height']} at ({playable['x']}, {playable['y']})")
        
        # Test some points
        test_points = [
            (10, 10),                    # Top-left (should be top UI)
            (width-10, 10),              # Top-right (should be top UI)
            (10, height//2),             # Left side (should be left UI)
            (width-10, height//2),       # Right side (should be right UI)
            (width//2, height-10),       # Bottom center (should be bottom UI)
            (width//2, height//2),       # Center (should be playable)
        ]
        
        for x, y in test_points:
            region = ui_manager.get_region_for_point(x, y)
            is_playable = ui_manager.is_point_in_playable_area(x, y)
            print(f"    Point ({x}, {y}): region='{region}', playable={is_playable}")
        
        # Verify playable area is reasonable
        min_playable_width = 400
        min_playable_height = 300
        
        if playable['width'] < min_playable_width:
            print(f"    WARNING: Playable width {playable['width']} is less than minimum {min_playable_width}")
        if playable['height'] < min_playable_height:
            print(f"    WARNING: Playable height {playable['height']} is less than minimum {min_playable_height}")
    
    pygame.quit()

def visual_test():
    """Visual test of the separated UI system."""
    pygame.init()
    
    # Create a test window
    screen = pygame.display.set_mode((1200, 800))
    pygame.display.set_caption("Separated UI Test")
    clock = pygame.time.Clock()
    
    # Create UI manager
    ui_manager = SeparatedUIManager(1200, 800)
    
    # Mock game state
    mock_game_state = {
        'submarine': type('MockSubmarine', (), {
            'depth': 150,
            'max_depth': 300,
            'speed': 5
        })(),
        'resources': {
            'kelp': 25,
            'coral': 12,
            'pearl': 5,
            'treasure': 2,
            'oxygen': 350
        },
        'divers': [
            type('MockDiver', (), {'state': 'collecting'})(),
            type('MockDiver', (), {'state': 'idle'})(),
            type('MockDiver', (), {'state': 'returning'})(),
        ],
        'paused': False,
        'automation': {
            'auto_craft': True,
            'mouse_follow': False
        }
    }
    
    running = True
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.MOUSEBUTTONDOWN:
                # Test click handling
                result = ui_manager.handle_click(event.pos[0], event.pos[1], mock_game_state)
                if result:
                    print(f"Click result: {result}")
        
        # Clear screen
        screen.fill((0, 0, 0))
        
        # Draw the separated UI
        ui_manager.draw(screen, mock_game_state)
        
        # Draw some test content in playable area
        playable = ui_manager.get_playable_area()
        pygame.draw.rect(screen, (0, 100, 0), 
                        (playable['x'], playable['y'], playable['width'], playable['height']), 2)
        
        # Draw center marker in playable area
        center_x = playable['x'] + playable['width'] // 2
        center_y = playable['y'] + playable['height'] // 2
        pygame.draw.circle(screen, (255, 255, 0), (center_x, center_y), 10)
        
        # Draw mouse position and region info
        mouse_pos = pygame.mouse.get_pos()
        region = ui_manager.get_region_for_point(mouse_pos[0], mouse_pos[1])
        is_playable = ui_manager.is_point_in_playable_area(mouse_pos[0], mouse_pos[1])
        
        font = pygame.font.Font(None, 24)
        info_text = f"Mouse: {mouse_pos}, Region: {region}, Playable: {is_playable}"
        text_surface = font.render(info_text, True, (255, 255, 255))
        screen.blit(text_surface, (10, 10))
        
        pygame.display.flip()
        clock.tick(60)
    
    pygame.quit()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "visual":
        visual_test()
    else:
        test_ui_layout()
