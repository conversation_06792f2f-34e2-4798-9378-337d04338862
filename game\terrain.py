"""Enhanced terrain system with procedural generation."""

import pygame
import random
import math
import noise
from .constants import *

class TerrainFeature:
    """Individual terrain feature like rocks, coral formations, etc."""
    
    def __init__(self, x, y, feature_type, size=1.0):
        self.x = x
        self.y = y
        self.type = feature_type
        self.size = size
        self.color = self._get_feature_color()
        self.points = self._generate_shape()
        
    def _get_feature_color(self):
        """Get color based on feature type."""
        colors = {
            'rock': (100, 100, 100),
            'coral': (255, 100, 150),
            'seaweed': (50, 150, 50),
            'sand': (194, 178, 128),
            'cave': (40, 40, 40)
        }
        return colors.get(self.type, (128, 128, 128))
        
    def _generate_shape(self):
        """Generate random shape points for the feature."""
        points = []
        num_points = random.randint(5, 8)
        base_radius = 20 * self.size
        
        for i in range(num_points):
            angle = (i / num_points) * 2 * math.pi
            radius = base_radius + random.randint(-5, 5)
            px = self.x + math.cos(angle) * radius
            py = self.y + math.sin(angle) * radius
            points.append((px, py))
            
        return points
        
    def draw(self, screen, camera_offset=0):
        """Draw the terrain feature."""
        adjusted_points = [(p[0], p[1] + camera_offset) for p in self.points]

        # Only draw if visible on screen
        if any(-50 <= p[1] <= SCREEN_HEIGHT + 50 for p in adjusted_points):
            pygame.draw.polygon(screen, self.color, adjusted_points)
            pygame.draw.polygon(screen, (255, 255, 255, 100), adjusted_points, 2)

    def draw_with_camera(self, screen, camera):
        """Draw the terrain feature with camera transformation."""
        # Transform all points to screen coordinates
        screen_points = []
        for point in self.points:
            screen_x, screen_y = camera.world_to_screen(point[0], point[1])
            screen_points.append((screen_x, screen_y))

        # Only draw if any point is visible
        if screen_points:
            try:
                pygame.draw.polygon(screen, self.color, screen_points)
                pygame.draw.polygon(screen, (255, 255, 255, 100), screen_points, 2)
            except ValueError:
                # Skip drawing if polygon is invalid
                pass

class TerrainLayer:
    """A layer of terrain at a specific depth."""
    
    def __init__(self, depth_start, depth_end, density=0.3):
        self.depth_start = depth_start
        self.depth_end = depth_end
        self.density = density
        self.features = []
        self.generated_chunks = set()
        
    def generate_chunk(self, chunk_x):
        """Generate terrain features for a chunk."""
        if chunk_x in self.generated_chunks:
            return
            
        chunk_width = 200
        start_x = chunk_x * chunk_width
        
        # Use noise for more natural distribution
        for x in range(start_x, start_x + chunk_width, 40):
            for y in range(self.depth_start, self.depth_end, 60):
                # Use Perlin noise for natural placement
                noise_val = noise.pnoise2(x * 0.01, y * 0.01, octaves=3)
                if noise_val > (1 - self.density):
                    feature_types = ['rock', 'coral', 'seaweed', 'sand']
                    if y > 300:  # Deeper features
                        feature_types.extend(['cave', 'coral'])
                        
                    feature_type = random.choice(feature_types)
                    size = 0.5 + random.random() * 1.5
                    
                    # Add some randomness to position
                    fx = x + random.randint(-20, 20)
                    fy = y + random.randint(-30, 30)
                    
                    self.features.append(TerrainFeature(fx, fy, feature_type, size))
                    
        self.generated_chunks.add(chunk_x)
        
    def draw(self, screen, camera_offset=0):
        """Draw all terrain features in this layer."""
        for feature in self.features:
            feature.draw(screen, camera_offset)

    def draw_with_camera(self, screen, camera, visible_bounds, layer_index):
        """Draw terrain features with camera transformation."""
        for feature in self.features:
            # Check if feature is visible
            if (visible_bounds['left'] <= feature.x <= visible_bounds['right'] and
                visible_bounds['top'] <= feature.y <= visible_bounds['bottom']):
                feature.draw_with_camera(screen, camera)

class ProceduralTerrain:
    """Manages procedural terrain generation."""
    
    def __init__(self):
        self.layers = [
            TerrainLayer(SURFACE_LEVEL + 50, SURFACE_LEVEL + 200, 0.2),   # Shallow
            TerrainLayer(SURFACE_LEVEL + 150, SURFACE_LEVEL + 350, 0.3),  # Medium
            TerrainLayer(SURFACE_LEVEL + 300, SURFACE_LEVEL + 500, 0.4),  # Deep
            TerrainLayer(SURFACE_LEVEL + 450, SURFACE_LEVEL + 700, 0.5),  # Abyss
        ]
        
        # Generate initial chunks
        for chunk in range(-2, 8):  # Generate some initial terrain
            for layer in self.layers:
                layer.generate_chunk(chunk)
                
    def update(self, submarine_x):
        """Update terrain generation based on submarine position."""
        current_chunk = int(submarine_x // 200)
        
        # Generate chunks around submarine
        for chunk_offset in range(-2, 3):
            chunk_x = current_chunk + chunk_offset
            for layer in self.layers:
                layer.generate_chunk(chunk_x)
                
    def draw(self, screen, submarine_depth):
        """Draw terrain with depth-based effects."""
        camera_offset = -submarine_depth * 0.1  # Parallax effect

        for i, layer in enumerate(self.layers):
            # Apply depth fade
            if submarine_depth >= layer.depth_start - 100:
                layer.draw(screen, camera_offset * (i + 1) * 0.3)

    def draw_with_camera(self, screen, submarine_depth, camera, visible_bounds):
        """Draw terrain with camera transformation."""
        # Only draw terrain features that are visible
        for i, layer in enumerate(self.layers):
            # Apply depth fade
            if submarine_depth >= layer.depth_start - 100:
                layer.draw_with_camera(screen, camera, visible_bounds, i)

class DepthZone:
    """Represents different depth zones with unique characteristics."""
    
    def __init__(self, name, depth_range, color_tint, ambient_effects):
        self.name = name
        self.depth_min, self.depth_max = depth_range
        self.color_tint = color_tint
        self.ambient_effects = ambient_effects
        self.particles = []
        
    def is_in_zone(self, depth):
        """Check if depth is within this zone."""
        return self.depth_min <= depth <= self.depth_max
        
    def get_ambient_color(self, base_color, depth):
        """Get ambient color for this zone."""
        zone_intensity = min(1.0, (depth - self.depth_min) / (self.depth_max - self.depth_min))
        
        # Blend base color with zone tint
        r = int(base_color[0] * (1 - zone_intensity * 0.3) + self.color_tint[0] * zone_intensity * 0.3)
        g = int(base_color[1] * (1 - zone_intensity * 0.3) + self.color_tint[1] * zone_intensity * 0.3)
        b = int(base_color[2] * (1 - zone_intensity * 0.3) + self.color_tint[2] * zone_intensity * 0.3)
        
        return (max(0, min(255, r)), max(0, min(255, g)), max(0, min(255, b)))
        
    def update_particles(self, dt):
        """Update ambient particles for this zone."""
        # Add floating particles based on zone type
        if 'particles' in self.ambient_effects and random.random() < 0.1:
            particle = {
                'x': random.randint(0, SCREEN_WIDTH),
                'y': random.randint(0, SCREEN_HEIGHT),
                'vx': random.uniform(-0.5, 0.5),
                'vy': random.uniform(-0.2, 0.2),
                'life': random.randint(180, 300),
                'size': random.randint(1, 3)
            }
            self.particles.append(particle)
            
        # Update existing particles
        for particle in self.particles[:]:
            particle['x'] += particle['vx']
            particle['y'] += particle['vy']
            particle['life'] -= 1
            
            if particle['life'] <= 0 or particle['x'] < 0 or particle['x'] > SCREEN_WIDTH:
                self.particles.remove(particle)
                
    def draw_particles(self, screen):
        """Draw ambient particles."""
        for particle in self.particles:
            alpha = min(255, particle['life'] * 2)
            color = (*self.color_tint, alpha)
            pygame.draw.circle(screen, color[:3], 
                             (int(particle['x']), int(particle['y'])), 
                             particle['size'])

class EnhancedTerrain:
    """Complete enhanced terrain system."""
    
    def __init__(self):
        self.procedural_terrain = ProceduralTerrain()
        
        # Define depth zones
        self.zones = [
            DepthZone("Sunlight Zone", (0, 100), (255, 255, 200), ['bubbles']),
            DepthZone("Twilight Zone", (100, 300), (100, 150, 200), ['particles']),
            DepthZone("Midnight Zone", (300, 500), (50, 50, 150), ['particles', 'darkness']),
            DepthZone("Abyssal Zone", (500, 1000), (20, 20, 80), ['particles', 'darkness', 'pressure'])
        ]
        
    def update(self, submarine_x, submarine_depth, dt):
        """Update terrain system."""
        self.procedural_terrain.update(submarine_x)
        
        # Update zone effects
        for zone in self.zones:
            if zone.is_in_zone(submarine_depth):
                zone.update_particles(dt)
                
    def get_ambient_color(self, base_color, depth):
        """Get ambient color based on current depth zone."""
        for zone in self.zones:
            if zone.is_in_zone(depth):
                return zone.get_ambient_color(base_color, depth)
        return base_color
        
    def draw(self, screen, submarine_depth):
        """Draw complete terrain system."""
        # Draw procedural terrain
        self.procedural_terrain.draw(screen, submarine_depth)

        # Draw zone effects
        for zone in self.zones:
            if zone.is_in_zone(submarine_depth):
                zone.draw_particles(screen)

    def draw_with_camera(self, screen, submarine_depth, camera, visible_bounds):
        """Draw terrain system with camera transformation."""
        # Draw procedural terrain with camera
        self.procedural_terrain.draw_with_camera(screen, submarine_depth, camera, visible_bounds)

        # Draw zone effects (these can remain screen-relative for now)
        for zone in self.zones:
            if zone.is_in_zone(submarine_depth):
                zone.draw_particles(screen)
