"""Main game class for Deep Sea Explorer."""

import pygame
import sys
import time
import math
from .constants import *
from .submarine import Submarine
from .diver import Diver
from .resource import Resource<PERSON>anager
from .terrain import Enhanced<PERSON><PERSON><PERSON>
from .automation import <PERSON>Pilot, CrewAI, AutoCrafter, MouseController
from .touch_input import ResponsiveInput
from .unified_ui import UnifiedUI
from .upgrade_system import UpgradeManager
from .upgrade_shop import UpgradeShop
from .achievements import AchievementManager
from .dock import Dock
from .camera import Camera
from .sky_system import SkySystem

class SubmarineGame:
    """Main game class that manages all game systems."""
    
    def __init__(self):
        # Set responsive dimensions
        screen_width, screen_height = set_responsive_dimensions()

        # Initialize Pygame with responsive screen
        self.screen = pygame.display.set_mode((screen_width, screen_height), pygame.RESIZABLE)
        pygame.display.set_caption("Deep Sea Explorer - Cross-Platform Edition")
        self.clock = pygame.time.Clock()

        # Initialize unified UI system
        self.ui = UnifiedUI(screen_width, screen_height)
        # Create a simple input handler that doesn't depend on old UI systems
        self.input_handler = None  # We'll handle input directly through unified UI

        # Performance optimization: Cache surfaces and calculations
        self._game_surface = None
        self._last_playable_area = None
        self._cached_water_color = None
        self._last_depth = -1

        # Initialize upgrade and achievement systems
        self.upgrade_manager = UpgradeManager()
        self.upgrade_shop = UpgradeShop(screen_width, screen_height, self.upgrade_manager)
        self.achievement_manager = AchievementManager()



        # Game objects (scaled for responsive design)
        # Initialize dock first to get its position
        self.dock = Dock(WORLD_WIDTH // 2 - 100, SURFACE_LEVEL - 20)  # Position dock in world coordinates

        # Start submarine docked at the dock
        submarine_start_x = self.dock.x + self.dock.width // 2 - 40  # Center submarine on dock (40 is half submarine width)
        submarine_start_y = self.dock.y - 30 + 5  # Position submarine on dock (30 is submarine height)

        self.submarine = Submarine(submarine_start_x, submarine_start_y)

        # Set submarine's initial docked state
        self.submarine.set_docked_state(True, self.dock)
        self.submarine.depth = 0  # Ensure submarine starts at surface

        # Set dock's initial state to show it's occupied
        self.dock.is_occupied = True
        self.dock.docked_submarine = self.submarine

        # Initialize camera system
        playable_area = self.ui.get_playable_area()
        self.camera = Camera(playable_area['width'], playable_area['height'])
        self.camera.submarine = self.submarine  # Give camera reference to submarine
        self.resource_manager = ResourceManager()
        self.terrain = EnhancedTerrain()
        self.sky_system = SkySystem()

        # Automation systems
        self.autopilot = AutoPilot()
        self.crew_ai = CrewAI()
        self.auto_crafter = AutoCrafter()
        self.mouse_controller = MouseController()

        # Crew management (automated divers)
        self.divers = []
        self.max_divers = 1  # Start with only 1 diver instead of 2
        self._spawn_initial_divers()

        # Game state
        self.resources = {
            'oxygen': 1000,
            'kelp': 0,
            'coral': 0,
            'pearl': 0,
            'treasure': 0
        }

        self.running = True
        self.paused = False
        self.show_welcome = True
        self.welcome_timer = 300  # Show for 5 seconds

        # Game statistics for achievements
        self.start_time = time.time()
        self.resources_this_dive = 0
        self.total_upgrades_purchased = 0

        # Performance monitoring
        self.frame_count = 0
        self.fps_timer = 0
        self.current_fps = 60
        self.show_performance = False  # Toggle with F1
        self.show_debug = False  # Toggle with D key
        
    def _spawn_initial_divers(self):
        """Spawn initial crew of divers."""
        spawn_pos = self.submarine.get_center_pos()
        for i in range(self.max_divers):
            diver = Diver(spawn_pos[0], spawn_pos[1], i)
            # Properly dock the diver to ensure correct positioning
            self.submarine.dock_diver(diver)
            self.divers.append(diver)

        # Set divers reference on submarine for grappling hook coordination
        self.submarine.set_divers_reference(self.divers)

    def handle_events(self):
        """Handle all game events with responsive input."""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.VIDEORESIZE:
                self._handle_resize(event.w, event.h)
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False
                elif event.key == pygame.K_SPACE:
                    self.paused = not self.paused
                elif event.key == pygame.K_F1:
                    self.show_performance = not self.show_performance
                elif event.key == pygame.K_d:
                    self.show_debug = not self.show_debug
            elif event.type == pygame.MOUSEWHEEL:
                # Handle zoom with mouse wheel
                if event.y > 0:
                    self.camera.zoom_in()
                elif event.y < 0:
                    self.camera.zoom_out()
            elif event.type == pygame.MOUSEBUTTONDOWN:
                # Handle middle mouse button for camera dragging
                if event.button == 2:  # Middle mouse button
                    self.camera.start_mouse_drag(event.pos)
                # Handle left click for focus button (check this first before game clicks)
                elif event.button == 1:  # Left mouse button
                    # Check focus button first
                    if hasattr(self, 'focus_button_rect') and self.focus_button_rect.collidepoint(event.pos):
                        self.camera.focus_on_submarine()
                    else:
                        # Process normal game clicks if not clicking the focus button
                        # Convert to input result format for existing game logic
                        input_result = {'type': 'tap', 'pos': event.pos, 'button': 'left'}
                        self._handle_input_result(input_result)
                elif event.button == 3:  # Right mouse button
                    # Handle right click for submarine movement
                    input_result = {'type': 'tap', 'pos': event.pos, 'button': 'right'}
                    self._handle_input_result(input_result)
            elif event.type == pygame.MOUSEBUTTONUP:
                # Stop camera dragging
                if event.button == 2:  # Middle mouse button
                    self.camera.stop_mouse_drag()
            elif event.type == pygame.MOUSEMOTION:
                # Handle camera dragging
                self.camera.update_mouse_drag(event.pos)
            # Note: Other input handling is now done directly through unified UI system
                    
    def _handle_resize(self, width, height):
        """Handle window resize events."""
        global SCREEN_WIDTH, SCREEN_HEIGHT
        SCREEN_WIDTH = max(MIN_SCREEN_WIDTH, width)
        SCREEN_HEIGHT = max(MIN_SCREEN_HEIGHT, height)

        # Update screen
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.RESIZABLE)

        # Update unified UI system
        self.ui.resize(SCREEN_WIDTH, SCREEN_HEIGHT)
        self.upgrade_shop = UpgradeShop(SCREEN_WIDTH, SCREEN_HEIGHT, self.upgrade_manager)

        # Update camera viewport
        playable_area = self.ui.get_playable_area()
        self.camera.resize_viewport(playable_area['width'], playable_area['height'])

        # Performance optimization: Invalidate cached surfaces on resize
        self._game_surface = None
        self._last_playable_area = None

    def _handle_input_result(self, input_result):
        """Handle processed input results."""
        if input_result['type'] in ['tap', 'touch_start']:
            # Check if this is a right-click for submarine movement
            button = input_result.get('button', 'left')  # Default to left for touch
            if button == 'right':
                self._handle_submarine_movement(input_result['pos'])
            else:
                self._handle_tap(input_result['pos'])
        elif input_result['type'] == 'double_tap':
            self._handle_double_tap(input_result['pos'])
        # Note: Keyboard events are handled directly in handle_events(), not through input system

    def _handle_tap(self, pos):
        """Handle tap/click events."""
        # Dismiss welcome message if showing
        if self.show_welcome:
            self.show_welcome = False
            return

        # Note: Bottom buttons are now handled by unified UI

        # Check upgrade shop first - always check if open
        if self.upgrade_shop.is_open:
            shop_result = self.upgrade_shop.handle_click(pos)
            # If shop_result is a string, it's an upgrade name to purchase
            if isinstance(shop_result, str):
                self._try_purchase_upgrade(shop_result)
                return  # Don't close shop after purchase
            # If shop_result is True, the click was handled (shop stays open)
            elif shop_result:
                return  # Click was handled, don't process further
            # If shop_result is False, shop was closed, continue processing

        # Use unified UI system for input handling
        game_state = self._get_game_state()
        ui_result = self.ui.handle_click(pos[0], pos[1], game_state)

        if ui_result:
            if ui_result['action'] == 'game_click':
                # Click was in playable area - handle as grappling hook deployment (left-click only)
                self._handle_grappling_click((ui_result['x'], ui_result['y']))
            elif ui_result['action'] == 'toggle_auto_craft':
                self.auto_crafter.enabled = not self.auto_crafter.enabled
            elif ui_result['action'] == 'toggle_mouse_follow':
                # Toggle between manual control and autonomous mode
                if self.mouse_controller.submarine_follow_mouse or self.mouse_controller.was_user_controlled:
                    # Resume autonomous movement
                    self.mouse_controller._resume_autonomous_movement(self.autopilot)
                else:
                    # Enable manual control mode (user can click to move submarine)
                    self.mouse_controller.was_user_controlled = True
            elif ui_result['action'] == 'toggle_upgrade_shop':
                self.upgrade_shop.toggle_shop()
            elif ui_result['action'] == 'focus_submarine':
                self.camera.focus_on_submarine()
            elif ui_result['action'] == 'focus_dock':
                # Focus camera on dock
                dock_center_x = self.dock.x + self.dock.width // 2
                dock_center_y = self.dock.y + self.dock.height // 2
                self.camera.set_target(dock_center_x, dock_center_y)
            return

        # Fallback to old input handling for shops and other UI elements
        self._handle_legacy_input(pos)

    def _get_game_state(self):
        """Get current game state for UI system."""
        return {
            'submarine': self.submarine,
            'resources': self.resources,
            'dock_resources': self.dock.stored_resources,  # Add dock storage for UI
            'submarine_storage': self.submarine.storage,   # Add submarine storage for UI
            'divers': self.divers,
            'paused': self.paused,
            'automation': {
                'autopilot': self.autopilot.enabled,
                'crew_ai': self.crew_ai.enabled,
                'auto_craft': self.auto_crafter.enabled,
                'mouse_follow': self.mouse_controller.submarine_follow_mouse or self.mouse_controller.was_user_controlled,
                'mouse_status': self.mouse_controller.get_status(),
                'autopilot_status': self.autopilot.get_status(self.submarine, self.divers)
            },
            'dock': self.dock
        }

    # Note: Bottom buttons are now handled by unified UI system

    def _handle_submarine_movement(self, pos):
        """Handle right-click submarine movement."""
        # Convert screen coordinates to playable area coordinates
        playable_area = self.ui.get_playable_area()
        game_x = pos[0] - playable_area['x']
        game_y = pos[1] - playable_area['y']

        # Convert to world coordinates using camera
        world_pos = self.camera.screen_to_world(game_x, game_y)

        # Set submarine target for autonomous movement (temporarily disable autopilot)
        self.mouse_controller.set_submarine_target(world_pos, self.autopilot)

    def _handle_grappling_click(self, pos):
        """Handle left-click grappling hook deployment."""
        # Convert screen coordinates to playable area coordinates
        playable_area = self.ui.get_playable_area()
        game_x = pos[0] - playable_area['x']
        game_y = pos[1] - playable_area['y']

        # Convert to world coordinates using camera
        world_pos = self.camera.screen_to_world(game_x, game_y)

        # Check for resource collection
        clicked_resource = self.resource_manager.get_nearest_resource(world_pos)
        if clicked_resource:
            # Check if the resource is close enough to the click
            import math
            distance = math.sqrt((clicked_resource.x - world_pos[0])**2 + (clicked_resource.y - world_pos[1])**2)
            if distance <= 50:  # Click tolerance
                # Deploy player grappling hook to resource
                if hasattr(self.submarine, 'player_hook'):
                    self.submarine.player_hook.deploy_to_resource(clicked_resource)
                return

        # Deploy player grappling hook to position
        if hasattr(self.submarine, 'player_hook'):
            self.submarine.player_hook.deploy_to_position(world_pos[0], world_pos[1])

    def _handle_legacy_input(self, pos):
        """Handle legacy input for shops and other UI elements."""
        # Check if click is in playable area - if so, handle as grappling click (legacy mode defaults to left-click behavior)
        if self.ui.is_point_in_playable_area(pos[0], pos[1]):
            self._handle_grappling_click(pos)

    def _handle_double_tap(self, pos):
        """Handle double tap events."""
        # Double tap could be used for special actions
        pass

    # Note: Text size adjustment is now handled automatically by responsive design

    def _try_purchase_upgrade(self, upgrade_name):
        """Try to purchase a specific upgrade."""
        # Get upgrade cost
        cost = self.upgrade_manager.get_upgrade_cost(self.upgrade_shop.selected_category, upgrade_name)
        if not cost:
            return False

        # Check if we can afford it with combined dock and warehouse resources
        if self.dock.can_afford_combined(cost):
            # Deduct resources from combined storage
            self.dock.deduct_combined_resources(cost)

            # Increase upgrade level
            self.upgrade_manager.upgrades[self.upgrade_shop.selected_category][upgrade_name]['level'] += 1

            # Apply upgrade effects
            self._apply_upgrade_effects()
            self.total_upgrades_purchased += 1

            # If it's a dock upgrade, also update the dock's upgrade levels
            if self.upgrade_shop.selected_category == 'dock':
                self.dock.upgrades[upgrade_name] = self.upgrade_manager.upgrades['dock'][upgrade_name]['level']

            # Show upgrade notification
            self.ui.show_notification(f"+ Upgraded {upgrade_name.replace('_', ' ').title()}!", 'info', 2000)
            return True

        return False

    def _apply_upgrade_effects(self):
        """Apply all current upgrade effects to game systems."""
        effects = self.upgrade_manager.get_all_effects()

        # Apply submarine upgrades
        if 'max_depth_bonus' in effects:
            self.submarine.max_depth = MAX_DEPTH + effects['max_depth_bonus']
        if 'speed_multiplier' in effects:
            self.submarine.speed = SUBMARINE_SPEED * (1 + effects['speed_multiplier'])
        if 'oxygen_capacity' in effects:
            # Could increase max oxygen here
            pass

        # Apply submarine storage upgrades
        submarine_upgrades = self.upgrade_manager.upgrades.get('submarine', {})
        if 'storage' in submarine_upgrades:
            self.submarine.storage_upgrades = submarine_upgrades['storage']['level']

        # Apply grappling hook upgrades
        if 'hook_count' in effects:
            # Add more hooks to the grappling system
            target_hooks = 2 + int(effects['hook_count'])
            while len(self.submarine.grappling_system.hooks) < target_hooks:
                self.submarine.grappling_system._spawn_hooks()

        # Apply dock upgrades
        dock_upgrades = self.upgrade_manager.upgrades.get('dock', {})
        for upgrade_name, upgrade_data in dock_upgrades.items():
            if upgrade_data['level'] > 0:
                self.dock.upgrades[upgrade_name] = upgrade_data['level']

        # Apply warehouse upgrades
        warehouse_upgrades = self.upgrade_manager.upgrades.get('warehouse', {})
        if hasattr(self.dock, 'warehouse'):
            for upgrade_name, upgrade_data in warehouse_upgrades.items():
                if upgrade_data['level'] > 0:
                    self.dock.warehouse.upgrades[upgrade_name] = upgrade_data['level']

        # Update dock storage capacity if it was upgraded
        if 'dock' in self.upgrade_manager.upgrades and self.upgrade_manager.upgrades['dock']['storage_capacity']['level'] > 0:
            self.dock.update_storage_capacity()

        # Apply crew upgrades
        if 'diver_count' in effects:
            target_divers = 1 + int(effects['diver_count'])  # Start from 1 instead of 2
            while len(self.divers) < target_divers:
                spawn_pos = self.submarine.get_center_pos()
                new_diver = Diver(spawn_pos[0], spawn_pos[1], len(self.divers))
                # Properly dock the new diver
                self.submarine.dock_diver(new_diver)
                self.divers.append(new_diver)

            # Update divers reference on submarine for grappling hook coordination
            self.submarine.set_divers_reference(self.divers)

        # Apply unified range upgrade to both divers and grappling hooks
        if 'unified_range' in effects:
            unified_range_bonus = effects['unified_range']

            # Apply to all divers
            for diver in self.divers:
                diver.tether_range_bonus = unified_range_bonus
                diver.tether_range = diver.base_tether_range + unified_range_bonus

            # Apply to grappling system
            if hasattr(self.submarine, 'grappling_system'):
                for hook in self.submarine.grappling_system.hooks:
                    # Reset to base range and apply unified bonus
                    hook.max_range = hook.base_range + unified_range_bonus

            # Apply to player hook
            if hasattr(self.submarine, 'player_hook'):
                self.submarine.player_hook.max_range = self.submarine.player_hook.base_range + unified_range_bonus

        # Apply upgrade effects to all existing divers
        for diver in self.divers:
            diver.apply_upgrade_effects(effects)

    def _handle_automation_toggle(self, zone_name):
        """Handle automation toggle button presses."""
        if zone_name == 'autopilot':
            self.autopilot.toggle()
        elif zone_name == 'crew_ai':
            self.crew_ai.toggle()
        elif zone_name == 'auto_craft':
            self.auto_crafter.toggle()
        elif zone_name == 'mouse_follow':
            # Toggle between manual control and autonomous mode
            if self.mouse_controller.submarine_follow_mouse or self.mouse_controller.was_user_controlled:
                # Resume autonomous movement
                self.mouse_controller._resume_autonomous_movement(self.autopilot)
            else:
                # Enable manual control mode (user can click to move submarine)
                self.mouse_controller.was_user_controlled = True

    def _handle_dock_unloading(self):
        """Handle dock unloading completion - dock workers handle the actual resource transfer."""
        # Dock workers have handled the resource transfer, just handle the completion effects

        # Refill oxygen when docking
        self.resources['oxygen'] = min(1000, self.resources['oxygen'] + 500)

        # Reset dive counter
        self.resources_this_dive = 0

        # Track dock usage
        self.dock_uses = getattr(self, 'dock_uses', 0) + 1

        # Show completion message
        remaining_resources = self.submarine.get_storage_used()
        if remaining_resources == 0:
            self.ui.show_notification("⚓ Docking complete - All cargo unloaded by workers", 'success', 3000)
        else:
            self.ui.show_notification(f"⚓ Docking complete - {remaining_resources} items remaining", 'info', 2000)

        # Show oxygen refill notification
        self.ui.show_notification("💨 Oxygen refilled!", 'info', 1500)

    def _get_game_state_for_achievements(self):
        """Get current game state for achievement checking."""
        return {
            'resources': self.resources.copy(),
            'submarine': {
                'depth': self.submarine.depth,
                'max_depth': self.submarine.max_depth,
                'speed': self.submarine.speed
            },
            'divers': self.divers,
            'total_upgrades': self.total_upgrades_purchased,
            'autopilot': self.autopilot.enabled,
            'crew_ai': self.crew_ai.enabled,
            'auto_craft': self.auto_crafter.enabled,
            'resources_this_dive': self.resources_this_dive,
            'play_time': time.time() - self.start_time,
            'dock_uses': getattr(self, 'dock_uses', 0)
        }
            
    def update(self, dt):
        """Update all game systems."""
        # Convert delta time from milliseconds to seconds for consistent animation timing
        dt_seconds = dt / 1000.0

        # Performance monitoring
        self.frame_count += 1
        self.fps_timer += dt
        if self.fps_timer >= 1000:  # Update FPS every second
            self.current_fps = self.frame_count
            self.frame_count = 0
            self.fps_timer = 0

        if self.paused:
            return

        # Update UI system
        self.ui.update(dt)

        # Update automation systems - pass mouse controller and dock to prevent conflicts
        old_mode = getattr(self.autopilot, 'patrol_mode', None)
        self.autopilot.update(self.submarine, self.resource_manager.resources, dt,
                             self.resources['oxygen'], self.divers, self.mouse_controller, self.dock)

        # Show notification when entering dock priority mode
        if (old_mode != 'dock_priority' and self.autopilot.patrol_mode == 'dock_priority' and
            not hasattr(self, '_dock_priority_notified')):
            self.ui.show_notification("🚢 DOCK PRIORITY: All resources blocked by storage!", 'info', 4000)
            self._dock_priority_notified = True
        elif self.autopilot.patrol_mode != 'dock_priority':
            self._dock_priority_notified = False

        # Show notification when entering docking mode
        if (old_mode != 'docking' and self.autopilot.patrol_mode == 'docking' and
            not hasattr(self, '_docking_notified')):
            self.ui.show_notification("⚓ DOCKING: Staying at surface near dock", 'info', 3000)
            self._docking_notified = True
        elif self.autopilot.patrol_mode != 'docking':
            self._docking_notified = False
        self.crew_ai.update(self.divers, self.resource_manager.resources,
                           self.submarine.get_center_pos(), dt, self.submarine)
        self.auto_crafter.update(self.resources, self.submarine, dt)

        # Update mouse controller for submarine movement
        self.mouse_controller.update_submarine(self.submarine, dt,
                                              self.resource_manager.resources, self.autopilot, self.divers, self.dock)

        # Update upgrade shop
        self.upgrade_shop.update(dt)

        # Update achievements
        game_state = self._get_game_state_for_achievements()
        self.achievement_manager.update(game_state)
        self.achievement_manager.update_notifications(dt)

        # Update submarine and grappling systems
        grappling_collected = self.submarine.update(dt_seconds, self.resource_manager.resources)

        # Check for and resolve stuck states
        if self.submarine.is_potentially_stuck():
            self.submarine.resolve_stuck_state(self.divers)
            self.ui.show_notification("🔧 Resolving stuck submarine...", 'warning', 2000)

        # Keep submarine bounds updated (world boundaries) - but avoid tiny movements
        # Don't apply boundary corrections if submarine is being undocked
        if not (self.dock.undocking_submarine == self.submarine):
            new_x = max(0, min(WORLD_WIDTH - self.submarine.width, self.submarine.x))
            new_y = max(SURFACE_LEVEL, min(WORLD_HEIGHT - self.submarine.height, self.submarine.y))

            # Only apply boundary corrections if they're significant (avoid micro-movements)
            # Use smoother boundary correction to prevent jerky movement
            if abs(new_x - self.submarine.x) > 1.0:
                # Smooth boundary correction instead of instant snap
                correction_speed = 0.1
                self.submarine.x += (new_x - self.submarine.x) * correction_speed
            if abs(new_y - self.submarine.y) > 1.0:
                # Smooth boundary correction instead of instant snap
                correction_speed = 0.1
                self.submarine.y += (new_y - self.submarine.y) * correction_speed

        self.submarine.depth = max(0, self.submarine.y - SURFACE_LEVEL)

        # Process grappling hook collections
        for collected_item in grappling_collected:
            if isinstance(collected_item, tuple) and len(collected_item) == 2:
                resource_type, value = collected_item
            else:
                # Fallback for old format
                resource_type = collected_item
                value = RESOURCE_TYPES.get(resource_type, {}).get('value', 1)

            if resource_type in RESOURCE_TYPES:
                # Try to store in submarine first
                stored = self.submarine.store_resource(resource_type, value)
                if stored > 0:
                    self.resources_this_dive += 1  # Track for achievements

                    # Enhanced grappling hook collection notification
                    resource_icons = {
                        'kelp': '🌿',
                        'coral': '🪸',
                        'pearl': '🔮',
                        'treasure': '💰'
                    }
                    icon = resource_icons.get(resource_type, '+')

                    # Different notification based on storage status
                    storage_percentage = self.submarine.get_storage_percentage()
                    if storage_percentage >= 0.9:
                        self.ui.show_notification(f"🪝 {icon} +{stored} (Storage Critical!)", 'warning', 2500)
                    elif storage_percentage >= 0.7:
                        self.ui.show_notification(f"🪝 {icon} +{stored} (Getting Full)", 'warning', 2000)
                    else:
                        self.ui.show_notification(f"🪝 {icon} Grappled {resource_type.title()} (+{stored})", 'success', 1500)

                    # Enhanced storage full warning for grappling hooks
                    if stored < value:
                        lost_amount = value - stored
                        self.ui.show_notification(f"⚠️ Grappling Hook: Storage Full! Lost {lost_amount}!", 'error', 3000)
                else:
                    # No storage space at all
                    self.ui.show_notification(f"❌ Grappling Hook Blocked - Storage Full!", 'error', 2500)

        # Update camera to follow submarine center
        submarine_center_x = self.submarine.x + self.submarine.width // 2
        submarine_center_y = self.submarine.y + self.submarine.height // 2
        self.camera.update(submarine_center_x, submarine_center_y, dt)

        # Update terrain
        self.terrain.update(self.submarine.x, self.submarine.depth, dt)

        # Update sky system
        self.sky_system.update(dt_seconds)

        # Update resource manager
        submarine_pos = self.submarine.get_center_pos()
        # Collect all grappling hooks for visual collection effects
        all_grappling_hooks = []
        if hasattr(self.submarine, 'grappling_system') and self.submarine.grappling_system.hooks:
            all_grappling_hooks.extend(self.submarine.grappling_system.hooks)
        if hasattr(self.submarine, 'player_hook') and self.submarine.player_hook.hook:
            all_grappling_hooks.append(self.submarine.player_hook.hook)

        self.resource_manager.update(dt_seconds, self.submarine.depth, self.divers, submarine_pos, all_grappling_hooks)

        # Update dock system with resources collected this dive
        unloading_complete = self.dock.update(dt, self.submarine, self.resources_this_dive)

        # Handle dock unloading completion
        if unloading_complete:
            self._handle_dock_unloading()

        # Update divers
        submarine_pos = self.submarine.get_center_pos()
        for diver in self.divers:
            collected = diver.update(dt_seconds, submarine_pos, self.submarine)
            if collected:
                # Add collected resource to inventory - collected is now (type, value) tuple
                if isinstance(collected, tuple) and len(collected) == 2:
                    resource_type, value = collected
                else:
                    # Fallback for old format
                    resource_type = collected
                    value = RESOURCE_TYPES[collected]['value']

                # Try to store in submarine first
                stored = self.submarine.store_resource(resource_type, value)
                if stored > 0:
                    # Show enhanced notification for resource collection
                    resource_icons = {
                        'kelp': '🌿',
                        'coral': '🪸',
                        'pearl': '🔮',
                        'treasure': '💰'
                    }
                    icon = resource_icons.get(resource_type, '+')

                    # Different notification based on storage status
                    storage_percentage = self.submarine.get_storage_percentage()
                    if storage_percentage >= 0.9:
                        self.ui.show_notification(f"{icon} +{stored} (Storage Critical!)", 'warning', 2500)
                    elif storage_percentage >= 0.7:
                        self.ui.show_notification(f"{icon} +{stored} (Storage Getting Full)", 'warning', 2000)
                    else:
                        self.ui.show_notification(f"{icon} +{stored} {resource_type.title()}", 'success', 1500)

                    # Enhanced storage full warning
                    if stored < value:
                        lost_amount = value - stored
                        self.ui.show_notification(f"⚠️ Storage Full! Lost {lost_amount} {resource_type}!", 'error', 3000)
                else:
                    # No storage space at all
                    self.ui.show_notification(f"❌ Cannot collect {resource_type} - Storage Full!", 'error', 2500)

        # Consume oxygen over time when diving
        if self.submarine.depth > 0:
            old_oxygen = self.resources['oxygen']
            self.resources['oxygen'] = max(0, self.resources['oxygen'] - 0.1)

            # Show oxygen warnings
            if old_oxygen > 200 and self.resources['oxygen'] <= 200:
                self.ui.show_notification("! Oxygen getting low!", 'warning', 2500)
            elif old_oxygen > 100 and self.resources['oxygen'] <= 100:
                self.ui.show_notification("!! Critical oxygen level!", 'error', 3000)

        # Auto-dock when submarine storage prevents resource collection
        storage_percentage = self.submarine.get_storage_percentage()
        uncollected_resources = [r for r in self.resource_manager.resources if not r.collected]
        collectible_resources = [r for r in uncollected_resources
                               if self.submarine.can_store_resource(r.type, 1)]

        # Check if all spawned resources are blocked by storage limits
        all_resources_blocked = (len(uncollected_resources) > 0 and len(collectible_resources) == 0)

        # Check if there are no resources available at all
        no_resources_available = (len(uncollected_resources) == 0)

        # More aggressive docking when all resources are blocked by storage
        should_dock = False
        dock_message = ""

        if no_resources_available:
            # No resources available to collect - return to dock
            should_dock = True
            dock_message = "🏁 No resources available - returning to dock!"
        elif all_resources_blocked:
            # All spawned resources cannot be collected due to storage - prioritize docking
            should_dock = True
            dock_message = "📦 All resources blocked by storage - returning to dock!"
        elif storage_percentage >= 0.9:
            # Storage nearly full - normal docking behavior
            should_dock = True
            dock_message = "📦 Storage nearly full - returning to dock!"

        if (should_dock and self.submarine.depth <= 10 and  # Near surface
            self.submarine.has_grappling_hooks_aboard()):  # Safe to move
            dock_center_x = self.dock.x + self.dock.width // 2
            if abs(self.submarine.x - dock_center_x) > 15:
                # Move towards dock
                direction = 1 if dock_center_x > self.submarine.x else -1
                self.submarine.x += direction * 2.0

                # Show storage full warning
                if not hasattr(self, '_storage_warning_shown') or not self._storage_warning_shown:
                    self.ui.show_notification(dock_message, 'warning', 3000)
                    self._storage_warning_shown = True
            else:
                self._storage_warning_shown = False
        else:
            self._storage_warning_shown = False

        # Enhanced emergency surfacing when oxygen is critically low
        if self.resources['oxygen'] <= 50 and self.submarine.depth > 0:
            # EMERGENCY OVERRIDE - This takes priority over all other systems

            # Force recall all divers immediately
            for diver in self.divers:
                if diver.state != 'idle':
                    diver.state = 'idle'
                    diver.x, diver.y = self.submarine.get_center_pos()
                    diver.oxygen = max(10, diver.oxygen)  # Give them minimal oxygen

            # Force retract all grappling hooks immediately
            if hasattr(self.submarine, 'grappling_system'):
                for hook in self.submarine.grappling_system.hooks:
                    if hook.state != 'idle':
                        hook.state = 'idle'
                        hook.x = self.submarine.x + self.submarine.width // 2
                        hook.y = self.submarine.y + self.submarine.height // 2
                        hook.collected_resource = None
                        hook.target_resource = None

            # Force retract player grappling hook
            if hasattr(self.submarine, 'player_hook') and self.submarine.player_hook.hook:
                self.submarine.player_hook.hook = None

            # Disable autopilot during emergency
            self.autopilot.enabled = False

            # Fast emergency surfacing - much faster than normal movement
            emergency_speed = max(5, self.submarine.speed * 2)  # At least 5 pixels per frame
            self.submarine.y = max(SURFACE_LEVEL, self.submarine.y - emergency_speed)
            self.submarine.depth = max(0, self.submarine.y - SURFACE_LEVEL)

            # Force submarine to surface level if very close
            if self.submarine.depth <= 5:
                self.submarine.y = SURFACE_LEVEL
                self.submarine.depth = 0

            # Show emergency notification
            if not hasattr(self, '_emergency_notified') or not self._emergency_notified:
                self.ui.show_notification("🚨 EMERGENCY SURFACING! 🚨", 'error', 4000)
                self._emergency_notified = True

            # Additional warning if oxygen is extremely low
            if self.resources['oxygen'] <= 20:
                self.ui.show_notification("💀 CRITICAL OXYGEN! SURFACING NOW! 💀", 'error', 2000)

        else:
            self._emergency_notified = False
            # Re-enable autopilot when emergency is over
            if not self.autopilot.enabled and self.resources['oxygen'] > 100:
                self.autopilot.enabled = True

        # Auto-move towards dock when at surface and oxygen is low
        if (self.resources['oxygen'] <= 100 and self.submarine.depth <= 5 and
            self.submarine.has_grappling_hooks_aboard()):
            dock_center_x = self.dock.x + self.dock.width // 2
            if abs(self.submarine.x - dock_center_x) > 10:
                # Move towards dock only if grappling hooks are aboard
                direction = 1 if dock_center_x > self.submarine.x else -1
                self.submarine.x += direction * 1.5

        # Update welcome message timer
        if self.show_welcome:
            self.welcome_timer -= 1
            if self.welcome_timer <= 0:
                self.show_welcome = False
            
    def draw(self):
        """Draw all game elements with unified UI."""
        # First, draw the unified UI system (backgrounds and content)
        game_state = self._get_game_state()
        self.ui.draw(self.screen, game_state)

        # Get playable area for game rendering
        playable_area = self.ui.get_playable_area()

        # Performance optimization: Reuse game surface if size hasn't changed
        if (self._game_surface is None or
            self._last_playable_area != playable_area):
            self._game_surface = pygame.Surface((playable_area['width'], playable_area['height']))
            self._last_playable_area = playable_area.copy()

        # Performance optimization: Cache water color calculation
        current_depth = int(self.submarine.depth)
        if self._cached_water_color is None or self._last_depth != current_depth:
            base_color = self._interpolate_color(SURFACE_BLUE, DEEP_BLUE,
                                               min(self.submarine.depth / MAX_DEPTH, 1.0))
            self._cached_water_color = self.terrain.get_ambient_color(base_color, self.submarine.depth)
            self._last_depth = current_depth

        self._game_surface.fill(self._cached_water_color)

        # Draw sky system and surface effects with proper masking
        # Sky should maintain its own colors, not be affected by water color
        self._draw_sky_and_surface_with_masking(self._game_surface, playable_area)

        # Draw enhanced terrain with camera transformation
        self._draw_terrain_with_camera(self._game_surface)

        # Draw game objects with camera transformations
        self._draw_game_objects_with_camera(self._game_surface)

        # Blit the game surface to the playable area of the main screen
        self.screen.blit(self._game_surface, (playable_area['x'], playable_area['y']))

        # Draw unified collection range indicator (overlays on main screen)
        self._draw_grappling_range_indicator()

        # Draw upgrade shop (overlays on top of everything)
        self.upgrade_shop.draw(self.screen, self.dock.get_combined_resources())

        # Draw achievement notifications
        self.achievement_manager.draw_notifications(self.screen, self.ui)

        # Draw storage warning overlays
        self._draw_storage_warnings()

        # Draw welcome message
        if self.show_welcome:
            self._draw_welcome_message()

        # Note: Camera info and focus button are now handled by unified UI

        # Draw camera drag indicator
        if self.camera.dragging:
            self._draw_camera_drag_indicator()

        # Draw performance info if enabled
        if self.show_performance:
            self._draw_performance_info()

        # Draw debug info if enabled (toggle with 'D' key)
        if hasattr(self, 'show_debug') and self.show_debug:
            self._draw_debug_info()
            self._draw_surface_debug_info()  # Include surface debug in regular debug mode

    def _draw_terrain_with_camera(self, game_surface):
        """Draw terrain with camera transformation."""
        # Only draw terrain if camera is underwater (below surface level)
        if self.camera.y > SURFACE_LEVEL + 20:  # Only show terrain when clearly underwater
            visible_bounds = self.camera.get_visible_world_bounds()
            self.terrain.draw_with_camera(game_surface, self.submarine.depth, self.camera, visible_bounds)

    def _draw_game_objects_with_camera(self, game_surface):
        """Draw all game objects using camera transformations."""
        # Draw resources with camera transformation
        self._draw_resources_with_camera(game_surface)

        # Draw divers with camera transformation (only if underwater)
        if self.camera.y > SURFACE_LEVEL + 20:  # Only show divers when clearly underwater
            submarine_pos = self.submarine.get_center_pos()
            show_tether_range = len([d for d in self.divers if d.state == 'idle']) > 0  # Show range if any diver is idle
            submarine_stationary = self.submarine.is_stationary()

            # Performance optimization: Cache submarine screen position for all divers
            submarine_screen_pos = self.camera.world_to_screen(submarine_pos[0], submarine_pos[1])

            for diver in self.divers:
                self._draw_diver_with_camera(game_surface, diver, submarine_pos, show_tether_range, submarine_stationary, submarine_screen_pos)

        # Draw unified collection range indicator with camera transformation (only if underwater)
        if self.camera.y > SURFACE_LEVEL + 20:  # Only show range when clearly underwater
            self._draw_unified_collection_range_with_camera(game_surface)

        # Draw submarine movement target indicator with camera transformation
        self._draw_submarine_target_indicator_with_camera(game_surface)

        # Draw dock with camera transformation
        self._draw_dock_with_camera(game_surface)

        # Additional safeguard: Always draw dock if submarine is docked or very close to surface
        if ((hasattr(self.submarine, 'is_docked') and self.submarine.is_docked) or
            self.submarine.depth <= 30):
            dock_screen_x, dock_screen_y = self.camera.world_to_screen(self.dock.x, self.dock.y)
            # Force redraw without caching to ensure visibility during zoom operations
            self._draw_detailed_dock_with_camera(game_surface, dock_screen_x, dock_screen_y)

        # Draw submarine with camera transformation
        self._draw_submarine_with_camera(game_surface)

        # Draw autonomous/manual control indicator with camera transformation
        self._draw_control_mode_indicator_with_camera(game_surface)

    def _draw_resources_with_camera(self, surface):
        """Draw resources using camera transformation with optimized culling."""
        # Only draw resources if camera is underwater (below surface level)
        if self.camera.y <= SURFACE_LEVEL + 20:  # Don't show resources when at/above surface
            return

        # Performance optimization: Get visible bounds once
        visible_bounds = self.camera.get_visible_world_bounds()
        surface_width = surface.get_width()
        surface_height = surface.get_height()

        # Performance optimization: Batch process visible resources
        visible_resources = []
        for resource in self.resource_manager.resources:
            if not resource.collected:
                # Quick world-space culling before expensive screen transformation
                if (visible_bounds['left'] - 100 <= resource.x <= visible_bounds['right'] + 100 and
                    visible_bounds['top'] - 100 <= resource.y <= visible_bounds['bottom'] + 100):

                    screen_x, screen_y = self.camera.world_to_screen(resource.x, resource.y)

                    # Screen-space culling
                    if (-50 <= screen_x <= surface_width + 50 and
                        -50 <= screen_y <= surface_height + 50):
                        visible_resources.append((resource, screen_x, screen_y))

        # Draw all visible resources
        for resource, screen_x, screen_y in visible_resources:
            self._draw_resource_at_position(surface, resource, screen_x, screen_y)

            # Draw collection blocked indicator if storage is full
            if not self.submarine.can_store_resource(resource.type, 1):
                self._draw_collection_blocked_indicator(surface, screen_x, screen_y)

    def _draw_resource_at_position(self, surface, resource, screen_x, screen_y):
        """Draw a resource at the specified screen position."""
        # Skip drawing if resource is collected or has no size
        if resource.collected or resource.current_size <= 0:
            return

        # Calculate floating position
        float_y = screen_y + math.sin(resource.float_offset) * 3

        # Scale size with zoom using current_size
        scaled_size = max(2, int(resource.current_size * self.camera.zoom))

        # Draw glow effect based on current size
        glow_radius = scaled_size + int(resource.glow_intensity * 5)
        glow_color = tuple(min(255, c + int(resource.glow_intensity * 50)) for c in resource.color)

        # Add enhanced collection effect - make glow more intense when being collected
        if resource.being_collected:
            # Pulsing effect during collection
            collection_pulse = math.sin(resource.float_offset * 8) * 0.5 + 0.5
            collection_glow = int(100 * collection_pulse)
            glow_color = tuple(min(255, c + collection_glow) for c in glow_color)
            glow_radius += int(collection_pulse * 3 * self.camera.zoom)

        # Draw multiple circles for glow effect
        for i in range(3):
            alpha = 50 - i * 15
            glow_size = glow_radius - i * 2
            if glow_size > 0:
                try:
                    pygame.draw.circle(surface, glow_color,
                                     (int(screen_x), int(float_y)), glow_size)
                except (ValueError, OverflowError):
                    pass

        # Draw main resource using current size
        try:
            pygame.draw.circle(surface, resource.color,
                             (int(screen_x), int(float_y)), scaled_size)

            # Draw border - make it thicker and animated when being collected
            if resource.being_collected:
                border_width = max(1, int((3 + math.sin(resource.float_offset * 6)) * self.camera.zoom))
                border_color = tuple(min(255, c + 50) for c in WHITE)
            else:
                border_width = max(1, int(2 * self.camera.zoom))
                border_color = WHITE

            pygame.draw.circle(surface, border_color,
                             (int(screen_x), int(float_y)), scaled_size, border_width)

            # Draw collection progress indicator
            if resource.being_collected and resource.collection_progress > 0:
                # Draw a small progress bar above the resource
                bar_width = int(scaled_size * 2)
                bar_height = max(2, int(3 * self.camera.zoom))
                bar_x = int(screen_x - bar_width // 2)
                bar_y = int(float_y - scaled_size - 8 * self.camera.zoom)

                # Background bar
                pygame.draw.rect(surface, (50, 50, 50),
                               (bar_x, bar_y, bar_width, bar_height))

                # Progress bar
                progress_width = int(bar_width * resource.collection_progress)
                if progress_width > 0:
                    pygame.draw.rect(surface, (255, 255, 0),
                                   (bar_x, bar_y, progress_width, bar_height))

        except (ValueError, OverflowError):
            pass

    def _draw_collection_blocked_indicator(self, surface, screen_x, screen_y):
        """Draw an indicator showing that resource collection is blocked due to full storage."""
        try:
            # Pulsing blocked indicator
            pulse = (math.sin(pygame.time.get_ticks() * 0.01) + 1) * 0.5
            alpha = int(100 + pulse * 155)

            # Create indicator surface
            indicator_size = 16
            indicator_surface = pygame.Surface((indicator_size, indicator_size))
            indicator_surface.set_alpha(alpha)

            # Red X indicator
            red_intensity = int(200 + pulse * 55)
            x_color = (red_intensity, 0, 0)

            # Draw X lines
            pygame.draw.line(indicator_surface, x_color, (2, 2), (indicator_size - 2, indicator_size - 2), 2)
            pygame.draw.line(indicator_surface, x_color, (indicator_size - 2, 2), (2, indicator_size - 2), 2)

            # Draw circle background
            pygame.draw.circle(indicator_surface, (100, 0, 0), (indicator_size // 2, indicator_size // 2), indicator_size // 2 - 1)
            pygame.draw.circle(indicator_surface, x_color, (indicator_size // 2, indicator_size // 2), indicator_size // 2 - 1, 2)

            # Position indicator above resource
            indicator_x = int(screen_x - indicator_size // 2)
            indicator_y = int(screen_y - indicator_size - 10)

            surface.blit(indicator_surface, (indicator_x, indicator_y))

        except (ValueError, OverflowError, pygame.error):
            pass

    def _draw_diver_with_camera(self, surface, diver, submarine_pos, show_tether_range, submarine_stationary, submarine_screen_pos=None):
        """Draw a diver using camera transformation."""
        screen_x, screen_y = self.camera.world_to_screen(diver.x, diver.y)

        # Use cached submarine screen position if provided
        if submarine_screen_pos is None:
            submarine_screen_pos = self.camera.world_to_screen(submarine_pos[0], submarine_pos[1])

        # Check if diver is visible on screen
        if (-50 <= screen_x <= surface.get_width() + 50 and
            -50 <= screen_y <= surface.get_height() + 50):

            # Draw diver directly at screen position
            self._draw_diver_at_position(surface, diver, screen_x, screen_y, submarine_screen_pos, show_tether_range, submarine_stationary)

    def _draw_diver_at_position(self, surface, diver, screen_x, screen_y, submarine_screen_pos, show_tether_range, submarine_stationary):
        """Draw a diver at the specified screen position."""
        # Don't draw idle divers (they're inside the submarine)
        if diver.state == 'idle':
            # Don't draw individual diver tether ranges anymore - unified range is drawn elsewhere
            return  # Don't draw the diver itself when idle

        # Scale size with zoom (balanced size for visibility without being too large)
        diver_size = max(4, int(8 * self.camera.zoom))

        # Determine diver color based on state
        if diver.state == 'swimming':
            color = (0, 150, 255)  # Blue when swimming
        elif diver.state == 'collecting':
            color = (255, 255, 0)  # Yellow when collecting
        elif diver.state == 'returning':
            color = (255, 150, 0)  # Orange when returning
        else:
            color = (128, 128, 128)  # Gray default

        # Draw tether line if diver is deployed
        if diver.state != 'idle':
            try:
                # Different line colors based on diver state
                if diver.oxygen < 20:
                    line_color = (255, 0, 0)  # Red when oxygen is critical
                elif diver.oxygen < 40:
                    line_color = (255, 255, 0)  # Yellow when oxygen is low
                else:
                    line_color = (100, 100, 100)  # Gray normal

                pygame.draw.line(surface, line_color,
                               (int(submarine_screen_pos[0]), int(submarine_screen_pos[1])),
                               (int(screen_x), int(screen_y)), 2)
            except (ValueError, OverflowError):
                pass

        # Draw diver body with animation
        try:
            # Add animation effects based on diver state
            animated_x = screen_x
            animated_y = screen_y

            if diver.state == 'swimming':
                # Swimming animation - slight bobbing and movement
                bob_offset = math.sin(diver.swim_animation * 2) * 2 * self.camera.zoom
                animated_y += bob_offset
                # Add slight horizontal sway
                sway_offset = math.cos(diver.swim_animation * 1.5) * 1 * self.camera.zoom
                animated_x += sway_offset
            elif diver.state == 'collecting':
                # Collecting animation - rapid small movements
                collect_offset_x = math.sin(diver.swim_animation * 8) * 1 * self.camera.zoom
                collect_offset_y = math.cos(diver.swim_animation * 6) * 1 * self.camera.zoom
                animated_x += collect_offset_x
                animated_y += collect_offset_y
            elif diver.state == 'idle':
                # Idle animation - gentle floating
                float_offset = math.sin(diver.swim_animation * 0.5) * 1 * self.camera.zoom
                animated_y += float_offset

            pygame.draw.circle(surface, color, (int(animated_x), int(animated_y)), diver_size)
            pygame.draw.circle(surface, WHITE, (int(animated_x), int(animated_y)), diver_size, 1)

            # Draw oxygen tank (using animated position)
            tank_size = max(1, int(3 * self.camera.zoom))
            tank_x = int(animated_x - diver_size // 2)
            tank_y = int(animated_y)
            pygame.draw.circle(surface, (150, 150, 150), (tank_x, tank_y), tank_size)

            # Draw oxygen indicator if low (using animated position)
            if diver.oxygen < 30:
                warning_size = max(1, int(3 * self.camera.zoom))
                warning_y = int(animated_y - diver_size - 5 * self.camera.zoom)
                pygame.draw.circle(surface, RED, (int(animated_x), warning_y), warning_size)

            # Draw collected resource indicator (using animated position)
            if hasattr(diver, 'collected_resource') and diver.collected_resource:
                resource_size = max(1, int(2 * self.camera.zoom))
                resource_x = int(animated_x + diver_size // 2)
                resource_y = int(animated_y - diver_size // 2)
                pygame.draw.circle(surface, GOLD, (resource_x, resource_y), resource_size)

        except (ValueError, OverflowError):
            pass

    def _draw_submarine_with_camera(self, surface):
        """Draw submarine using camera transformation."""
        screen_x, screen_y = self.camera.world_to_screen(self.submarine.x, self.submarine.y)

        # Draw submarine directly at screen position
        self._draw_submarine_at_position(surface, screen_x, screen_y)

    def _draw_submarine_at_position(self, surface, screen_x, screen_y):
        """Draw submarine at the specified screen position."""
        # Scale dimensions with zoom
        scaled_width = max(10, int(self.submarine.width * self.camera.zoom))
        scaled_height = max(6, int(self.submarine.height * self.camera.zoom))

        # Get visual y with bobbing effect
        visual_y = screen_y
        if self.submarine.depth == 0:  # Only bob at surface
            visual_y = screen_y + math.sin(self.submarine.bob_offset) * 2 * self.camera.zoom

        # Main submarine body (oval shape)
        hull_color = (70, 130, 180)
        if self.submarine.hull_upgrades > 0:
            hull_color = (90, 150, 200)  # Upgraded hull color

        # Change color based on oxygen and crew status
        if self.resources['oxygen'] <= 100:
            hull_color = (180, 70, 70)  # Red when oxygen is critically low
        elif self.resources['oxygen'] <= 200:
            hull_color = (180, 130, 70)  # Orange when oxygen is low
        elif self.divers and not self.submarine.has_crew_aboard(self.divers):
            hull_color = (150, 100, 180)  # Purple when waiting for crew

        try:
            pygame.draw.ellipse(surface, hull_color,
                              (int(screen_x), int(visual_y), scaled_width, scaled_height))

            # Draw submarine outline
            pygame.draw.ellipse(surface, WHITE,
                              (int(screen_x), int(visual_y), scaled_width, scaled_height), 2)

            # Draw periscope/conning tower if upgraded - positioned within submarine bounds
            if self.submarine.depth_upgrades > 0:
                tower_width = max(2, int(8 * self.camera.zoom))
                tower_height = max(3, min(int(12 * self.camera.zoom), scaled_height - 4))  # Ensure tower fits within submarine
                tower_x = int(screen_x + scaled_width * 0.3)
                tower_y = int(visual_y + 2 * self.camera.zoom)  # Position inside submarine, not above it
                pygame.draw.rect(surface, hull_color,
                               (tower_x, tower_y, tower_width, tower_height))

            # Draw grappling hooks
            self._draw_grappling_hooks_at_position(surface, screen_x, visual_y, scaled_width, scaled_height)

            # Draw storage indicators
            self._draw_submarine_storage_indicators(surface, screen_x, visual_y, scaled_width, scaled_height)

        except (ValueError, OverflowError):
            pass

    def _draw_submarine_storage_indicators(self, surface, screen_x, screen_y, sub_width, sub_height):
        """Draw storage capacity indicators on the submarine."""
        try:
            # Only draw storage indicators if zoom level is sufficient
            if self.camera.zoom < 0.5:
                return

            storage_percentage = self.submarine.get_storage_percentage()

            # Storage capacity bar above submarine
            bar_width = max(20, int(sub_width * 0.8))
            bar_height = max(3, int(4 * self.camera.zoom))
            bar_x = int(screen_x + (sub_width - bar_width) // 2)
            bar_y = int(screen_y - 15 * self.camera.zoom)

            # Background bar
            pygame.draw.rect(surface, (40, 40, 40), (bar_x, bar_y, bar_width, bar_height))
            pygame.draw.rect(surface, (80, 80, 80), (bar_x, bar_y, bar_width, bar_height), 1)

            # Storage fill bar with color based on capacity
            if storage_percentage > 0:
                fill_width = int(bar_width * storage_percentage)
                if storage_percentage >= 0.9:
                    fill_color = (220, 50, 50)  # Red when nearly full
                elif storage_percentage >= 0.7:
                    fill_color = (220, 150, 50)  # Orange when getting full
                else:
                    fill_color = (50, 150, 220)  # Blue when normal

                pygame.draw.rect(surface, fill_color, (bar_x, bar_y, fill_width, bar_height))

            # Storage type indicators (small dots showing what's stored)
            if self.camera.zoom >= 0.8:  # Only show detailed indicators at higher zoom
                dot_size = max(2, int(3 * self.camera.zoom))
                dot_spacing = max(6, int(8 * self.camera.zoom))
                start_x = int(screen_x + sub_width * 0.1)
                dot_y = int(screen_y + sub_height + 8 * self.camera.zoom)

                resource_colors = {
                    'kelp': (50, 150, 50),
                    'coral': (255, 100, 100),
                    'pearl': (200, 200, 255),
                    'treasure': (255, 215, 0)
                }

                dot_x = start_x
                for resource_type, color in resource_colors.items():
                    stored_amount = self.submarine.get_storage_used(resource_type)
                    if stored_amount > 0:
                        # Draw dot with size based on amount stored
                        max_capacity = self.submarine.get_storage_capacity(resource_type)
                        size_multiplier = min(2.0, stored_amount / max(1, max_capacity * 0.5))
                        actual_size = max(dot_size, int(dot_size * size_multiplier))

                        pygame.draw.circle(surface, color, (dot_x, dot_y), actual_size)
                        pygame.draw.circle(surface, (255, 255, 255), (dot_x, dot_y), actual_size, 1)

                        # Draw amount text if zoom is high enough
                        if self.camera.zoom >= 1.2 and stored_amount > 0:
                            font = pygame.font.Font(None, max(12, int(14 * self.camera.zoom)))
                            text = font.render(str(stored_amount), True, (255, 255, 255))
                            text_rect = text.get_rect(center=(dot_x, dot_y + actual_size + 8))
                            surface.blit(text, text_rect)

                        dot_x += dot_spacing

            # Storage full warning indicator
            if storage_percentage >= 0.95:
                warning_size = max(8, int(12 * self.camera.zoom))
                warning_x = int(screen_x + sub_width + 5)
                warning_y = int(screen_y + sub_height // 2)

                # Pulsing warning indicator
                pulse = (math.sin(pygame.time.get_ticks() * 0.01) + 1) * 0.5
                warning_alpha = int(150 + pulse * 105)
                warning_color = (255, 50, 50)

                # Create a surface for the warning with alpha
                warning_surface = pygame.Surface((warning_size * 2, warning_size * 2))
                warning_surface.set_alpha(warning_alpha)
                warning_surface.fill(warning_color)

                pygame.draw.circle(warning_surface, warning_color, (warning_size, warning_size), warning_size)
                surface.blit(warning_surface, (warning_x - warning_size, warning_y - warning_size))

                # Warning symbol (!)
                if self.camera.zoom >= 0.8:
                    font = pygame.font.Font(None, max(12, int(16 * self.camera.zoom)))
                    warning_text = font.render("!", True, (255, 255, 255))
                    text_rect = warning_text.get_rect(center=(warning_x, warning_y))
                    surface.blit(warning_text, text_rect)

        except (ValueError, OverflowError, pygame.error):
            pass

    def _draw_storage_warnings(self):
        """Draw storage warning overlays and indicators."""
        try:
            storage_percentage = self.submarine.get_storage_percentage()

            # Critical storage warning overlay
            if storage_percentage >= 0.95:
                # Full screen warning overlay
                warning_surface = pygame.Surface((self.screen.get_width(), self.screen.get_height()))
                warning_surface.set_alpha(30)

                # Pulsing red overlay
                pulse = (math.sin(pygame.time.get_ticks() * 0.008) + 1) * 0.5
                red_intensity = int(100 + pulse * 155)
                warning_surface.fill((red_intensity, 0, 0))
                self.screen.blit(warning_surface, (0, 0))

                # Storage full message
                font = pygame.font.Font(None, 48)
                warning_text = "STORAGE FULL!"
                text_surface = font.render(warning_text, True, (255, 255, 255))
                text_rect = text_surface.get_rect(center=(self.screen.get_width() // 2, 100))

                # Text shadow
                shadow_surface = font.render(warning_text, True, (0, 0, 0))
                shadow_rect = text_rect.copy()
                shadow_rect.x += 3
                shadow_rect.y += 3
                self.screen.blit(shadow_surface, shadow_rect)
                self.screen.blit(text_surface, text_rect)

                # Instructions
                instruction_font = pygame.font.Font(None, 24)
                instruction_text = "Return to dock to unload resources"
                instruction_surface = instruction_font.render(instruction_text, True, (255, 255, 255))
                instruction_rect = instruction_surface.get_rect(center=(self.screen.get_width() // 2, 140))

                instruction_shadow = instruction_font.render(instruction_text, True, (0, 0, 0))
                instruction_shadow_rect = instruction_rect.copy()
                instruction_shadow_rect.x += 2
                instruction_shadow_rect.y += 2
                self.screen.blit(instruction_shadow, instruction_shadow_rect)
                self.screen.blit(instruction_surface, instruction_rect)

            elif storage_percentage >= 0.8:
                # Warning border around screen
                border_width = 5
                warning_color = (255, 200, 0) if storage_percentage < 0.9 else (255, 100, 0)

                # Pulsing border
                pulse = (math.sin(pygame.time.get_ticks() * 0.005) + 1) * 0.5
                alpha = int(100 + pulse * 155)

                border_surface = pygame.Surface((self.screen.get_width(), border_width))
                border_surface.set_alpha(alpha)
                border_surface.fill(warning_color)

                # Top and bottom borders
                self.screen.blit(border_surface, (0, 0))
                self.screen.blit(border_surface, (0, self.screen.get_height() - border_width))

                # Side borders
                side_surface = pygame.Surface((border_width, self.screen.get_height()))
                side_surface.set_alpha(alpha)
                side_surface.fill(warning_color)
                self.screen.blit(side_surface, (0, 0))
                self.screen.blit(side_surface, (self.screen.get_width() - border_width, 0))

            # Collection blocked indicators
            if storage_percentage >= 0.99:
                # Show blocked collection indicators near resources
                playable_area = self.ui.get_playable_area()
                game_surface_rect = pygame.Rect(playable_area['x'], playable_area['y'],
                                              playable_area['width'], playable_area['height'])

                for resource in self.resource_manager.resources:
                    if not resource.collected:
                        # Check if resource is visible on screen
                        screen_pos = self.camera.world_to_screen(resource.x, resource.y)
                        screen_x = playable_area['x'] + screen_pos[0]
                        screen_y = playable_area['y'] + screen_pos[1]

                        if game_surface_rect.collidepoint(screen_x, screen_y):
                            # Draw blocked indicator
                            blocked_size = 20
                            blocked_surface = pygame.Surface((blocked_size * 2, blocked_size * 2))
                            blocked_surface.set_alpha(150)

                            # Pulsing X indicator
                            pulse = (math.sin(pygame.time.get_ticks() * 0.01) + 1) * 0.5
                            red_intensity = int(200 + pulse * 55)

                            # Draw X
                            pygame.draw.line(blocked_surface, (red_intensity, 0, 0),
                                           (5, 5), (blocked_size * 2 - 5, blocked_size * 2 - 5), 3)
                            pygame.draw.line(blocked_surface, (red_intensity, 0, 0),
                                           (blocked_size * 2 - 5, 5), (5, blocked_size * 2 - 5), 3)

                            self.screen.blit(blocked_surface, (screen_x - blocked_size, screen_y - blocked_size))

            # Dock direction indicator when storage is getting full
            if storage_percentage >= 0.8 and self.submarine.depth <= 20:
                dock_center_x = self.dock.x + self.dock.width // 2
                submarine_center_x = self.submarine.x + self.submarine.width // 2

                if abs(submarine_center_x - dock_center_x) > 50:  # Not at dock
                    # Draw arrow pointing to dock
                    arrow_size = 30
                    arrow_x = self.screen.get_width() // 2
                    arrow_y = 50

                    # Determine arrow direction
                    if dock_center_x > submarine_center_x:
                        # Point right
                        arrow_points = [(arrow_x - arrow_size//2, arrow_y),
                                      (arrow_x + arrow_size//2, arrow_y),
                                      (arrow_x + arrow_size//2 + 10, arrow_y + arrow_size//2),
                                      (arrow_x + arrow_size//2, arrow_y + arrow_size),
                                      (arrow_x - arrow_size//2, arrow_y + arrow_size)]
                    else:
                        # Point left
                        arrow_points = [(arrow_x + arrow_size//2, arrow_y),
                                      (arrow_x - arrow_size//2, arrow_y),
                                      (arrow_x - arrow_size//2 - 10, arrow_y + arrow_size//2),
                                      (arrow_x - arrow_size//2, arrow_y + arrow_size),
                                      (arrow_x + arrow_size//2, arrow_y + arrow_size)]

                    # Pulsing arrow
                    pulse = (math.sin(pygame.time.get_ticks() * 0.006) + 1) * 0.5
                    arrow_color = (int(100 + pulse * 155), int(200 + pulse * 55), 0)

                    pygame.draw.polygon(self.screen, arrow_color, arrow_points)
                    pygame.draw.polygon(self.screen, (255, 255, 255), arrow_points, 2)

                    # Arrow label
                    font = pygame.font.Font(None, 20)
                    label_text = "TO DOCK"
                    label_surface = font.render(label_text, True, (255, 255, 255))
                    label_rect = label_surface.get_rect(center=(arrow_x, arrow_y + arrow_size + 15))

                    label_shadow = font.render(label_text, True, (0, 0, 0))
                    label_shadow_rect = label_rect.copy()
                    label_shadow_rect.x += 1
                    label_shadow_rect.y += 1
                    self.screen.blit(label_shadow, label_shadow_rect)
                    self.screen.blit(label_surface, label_rect)

        except (ValueError, OverflowError, pygame.error):
            pass

    def _draw_grappling_hooks_at_position(self, surface, screen_x, screen_y, sub_width, sub_height):
        """Draw grappling hooks for the submarine."""
        if hasattr(self.submarine, 'grappling_system'):
            # Draw automated grappling hooks
            for i, hook in enumerate(self.submarine.grappling_system.hooks):
                if hook.state != 'idle':  # Hook is active (extending, collecting, or retracting)
                    hook_start_x = screen_x + sub_width // 2
                    hook_start_y = screen_y + sub_height // 2

                    # Convert hook position to screen coordinates
                    hook_screen_pos = self.camera.world_to_screen(hook.x, hook.y)

                    try:
                        # Draw hook line
                        pygame.draw.line(surface, (200, 200, 200),
                                       (int(hook_start_x), int(hook_start_y)),
                                       (int(hook_screen_pos[0]), int(hook_screen_pos[1])), 2)

                        # Draw hook end
                        hook_size = max(2, int(6 * self.camera.zoom))
                        hook_color = (200, 200, 200)
                        if hook.state == 'collecting':
                            hook_color = (255, 255, 0)  # Yellow when collecting
                        elif hook.collected_resource:
                            hook_color = (0, 255, 0)  # Green when carrying resource

                        pygame.draw.circle(surface, hook_color,
                                         (int(hook_screen_pos[0]), int(hook_screen_pos[1])), hook_size)
                        pygame.draw.circle(surface, WHITE,
                                         (int(hook_screen_pos[0]), int(hook_screen_pos[1])), hook_size, 1)
                    except (ValueError, OverflowError):
                        pass

        # Draw player grappling hook
        if (hasattr(self.submarine, 'player_hook') and
            self.submarine.player_hook.hook and
            self.submarine.player_hook.hook.state != 'idle'):

            hook = self.submarine.player_hook.hook
            hook_start_x = screen_x + sub_width // 2
            hook_start_y = screen_y + sub_height // 2

            # Convert hook position to screen coordinates
            hook_screen_pos = self.camera.world_to_screen(hook.x, hook.y)

            try:
                # Draw hook line (cyan color for player hook)
                pygame.draw.line(surface, (0, 255, 255),
                               (int(hook_start_x), int(hook_start_y)),
                               (int(hook_screen_pos[0]), int(hook_screen_pos[1])), 3)

                # Draw hook end
                hook_size = max(3, int(7 * self.camera.zoom))
                hook_color = (0, 255, 255)
                if hook.state == 'collecting':
                    hook_color = (255, 255, 0)  # Yellow when collecting
                elif hook.collected_resource:
                    hook_color = (0, 255, 0)  # Green when carrying resource

                pygame.draw.circle(surface, hook_color,
                                 (int(hook_screen_pos[0]), int(hook_screen_pos[1])), hook_size)
                pygame.draw.circle(surface, WHITE,
                                 (int(hook_screen_pos[0]), int(hook_screen_pos[1])), hook_size, 1)
            except (ValueError, OverflowError):
                pass

    def _draw_dock_with_camera(self, surface):
        """Draw dock using camera transformation with detailed graphics."""
        screen_x, screen_y = self.camera.world_to_screen(self.dock.x, self.dock.y)

        # More generous visibility check to ensure dock is always visible when submarine is near surface
        # Expand the bounds significantly to handle docking scenarios
        margin = 500  # Large margin to ensure dock is always visible
        if (-margin <= screen_x <= surface.get_width() + margin and
            -margin <= screen_y <= surface.get_height() + margin):

            # Always use detailed rendering for best visual quality
            self._draw_detailed_dock_with_camera(surface, screen_x, screen_y)
        elif self.submarine.depth <= 50:
            # Force dock rendering if submarine is near surface, regardless of screen position
            # This ensures dock is always visible when submarine could potentially dock
            self._draw_detailed_dock_with_camera(surface, screen_x, screen_y)



    def _draw_detailed_dock_with_camera(self, surface, screen_x, screen_y):
        """Draw simple dock graphics with proper scaling."""
        # Just use the simplified dock drawing - clean and reliable
        zoom = self.camera.zoom
        scaled_width = max(20, int(self.dock.width * zoom))
        scaled_height = max(10, int(self.dock.platform_height * zoom))

        self._draw_simplified_dock(surface, screen_x, screen_y, scaled_width, scaled_height)


    def _draw_dock_at_position(self, surface, screen_x, screen_y):
        """Draw dock at the specified screen position with camera scaling."""
        # Scale dimensions with zoom
        scaled_width = max(20, int(self.dock.width * self.camera.zoom))
        scaled_height = max(10, int(self.dock.platform_height * self.camera.zoom))

        try:
            # Always use simplified rendering for camera system
            # The detailed dock rendering is handled separately when not using camera
            self._draw_simplified_dock(surface, screen_x, screen_y, scaled_width, scaled_height)
        except (ValueError, OverflowError):
            pass

    def _draw_simplified_dock(self, surface, screen_x, screen_y, scaled_width, scaled_height):
        """Draw simplified dock for distant camera views."""
        dock_color = (139, 69, 19)  # Brown color
        outline_color = (101, 67, 33)

        # Ensure minimum visibility
        min_width = max(scaled_width, 30)
        min_height = max(scaled_height, 8)

        # Main platform with planks indication
        pygame.draw.rect(surface, dock_color,
                       (int(screen_x), int(screen_y), min_width, min_height))
        pygame.draw.rect(surface, outline_color,
                       (int(screen_x), int(screen_y), min_width, min_height), 2)

        # Simplified plank lines if zoom allows
        if self.camera.zoom >= 0.3:
            plank_spacing = max(6, int(20 * self.camera.zoom))
            for i in range(0, min_width, plank_spacing):
                plank_x = int(screen_x + i)
                pygame.draw.line(surface, outline_color,
                               (plank_x, int(screen_y)),
                               (plank_x, int(screen_y + min_height)), 1)

        # Simplified supports
        post_width = max(3, int(8 * self.camera.zoom))
        post_height = max(8, int(15 * self.camera.zoom))
        support_spacing = max(15, int(30 * self.camera.zoom))

        for i in range(0, min_width, support_spacing):
            post_x = int(screen_x + i)
            post_y = int(screen_y + min_height)
            pygame.draw.rect(surface, outline_color,
                           (post_x, post_y, post_width, post_height))

        # Always show warehouse indicator
        warehouse_width = max(20, int(self.dock.warehouse.width * self.camera.zoom * 0.8))
        warehouse_height = max(15, int(self.dock.warehouse.height * self.camera.zoom * 0.8))
        warehouse_x = int(screen_x + min_width - warehouse_width - 5)
        warehouse_y = int(screen_y - warehouse_height)

        # Simplified warehouse
        pygame.draw.rect(surface, (140, 100, 70),
                       (warehouse_x, warehouse_y, warehouse_width, warehouse_height))
        pygame.draw.rect(surface, (0, 0, 0),
                       (warehouse_x, warehouse_y, warehouse_width, warehouse_height), 2)

        # Warehouse roof
        roof_points = [
            (warehouse_x, warehouse_y),
            (warehouse_x + warehouse_width // 2, warehouse_y - 8),
            (warehouse_x + warehouse_width, warehouse_y)
        ]
        pygame.draw.polygon(surface, (100, 70, 50), roof_points)
        pygame.draw.polygon(surface, (0, 0, 0), roof_points, 1)

    def _draw_warehouse_with_camera(self, surface):
        """Draw warehouse using camera transformation."""
        if not hasattr(self.dock, 'warehouse'):
            return

        warehouse = self.dock.warehouse
        screen_x, screen_y = self.camera.world_to_screen(warehouse.x, warehouse.y)

        # Check if warehouse is visible on screen
        if (-200 <= screen_x <= surface.get_width() + 200 and
            -200 <= screen_y <= surface.get_height() + 200):

            # Draw warehouse at screen position with camera scaling
            self._draw_warehouse_at_position(surface, warehouse, screen_x, screen_y)

    def _draw_warehouse_at_position(self, surface, warehouse, screen_x, screen_y):
        """Draw warehouse at the specified screen position with camera scaling."""
        try:
            # Scale dimensions with zoom
            scaled_width = max(20, int(warehouse.width * self.camera.zoom))
            scaled_height = max(15, int(warehouse.height * self.camera.zoom))

            # Main warehouse building - larger and more detailed
            building_color = (140, 100, 70)  # Lighter brown warehouse color
            roof_color = (100, 70, 50)       # Darker brown for roof
            foundation_color = (80, 60, 40)  # Foundation color

            # Foundation/base
            foundation_height = max(2, int(8 * self.camera.zoom))
            pygame.draw.rect(surface, foundation_color,
                            (int(screen_x - 5 * self.camera.zoom),
                             int(screen_y + scaled_height - foundation_height),
                             int(scaled_width + 10 * self.camera.zoom),
                             int(foundation_height + 5 * self.camera.zoom)))

            # Main building base
            pygame.draw.rect(surface, building_color,
                           (int(screen_x), int(screen_y), scaled_width, scaled_height))
            pygame.draw.rect(surface, (0, 0, 0),
                           (int(screen_x), int(screen_y), scaled_width, scaled_height),
                           max(1, int(3 * self.camera.zoom)))

            # Building details - vertical support beams (only if zoom is sufficient)
            if self.camera.zoom > 0.5:
                beam_color = (100, 70, 50)
                beam_width = max(1, int(6 * self.camera.zoom))
                for i in range(3):
                    beam_x = int(screen_x + (i + 1) * (scaled_width // 4))
                    pygame.draw.rect(surface, beam_color,
                                   (beam_x, int(screen_y), beam_width, scaled_height))

            # Roof - more detailed with multiple sections
            roof_height = max(5, int(25 * self.camera.zoom))
            roof_points = [
                (int(screen_x - 8 * self.camera.zoom), int(screen_y)),
                (int(screen_x + scaled_width // 2), int(screen_y - roof_height)),
                (int(screen_x + scaled_width + 8 * self.camera.zoom), int(screen_y)),
            ]
            pygame.draw.polygon(surface, roof_color, roof_points)
            pygame.draw.polygon(surface, (0, 0, 0), roof_points, max(1, int(3 * self.camera.zoom)))

            # Large warehouse doors - double doors for industrial look (only if zoom is sufficient)
            if self.camera.zoom > 0.3:
                door_width = max(10, int(50 * self.camera.zoom))
                door_height = max(8, int(65 * self.camera.zoom))
                door_x = int(screen_x + (scaled_width - door_width) // 2)
                door_y = int(screen_y + scaled_height - door_height)

                # Left door
                left_door_width = door_width // 2 - 1
                pygame.draw.rect(surface, (80, 50, 30),
                               (door_x, door_y, left_door_width, door_height))
                pygame.draw.rect(surface, (0, 0, 0),
                               (door_x, door_y, left_door_width, door_height),
                               max(1, int(2 * self.camera.zoom)))

                # Right door
                right_door_x = door_x + door_width // 2 + 1
                pygame.draw.rect(surface, (80, 50, 30),
                               (right_door_x, door_y, left_door_width, door_height))
                pygame.draw.rect(surface, (0, 0, 0),
                               (right_door_x, door_y, left_door_width, door_height),
                               max(1, int(2 * self.camera.zoom)))

            # Windows (only if zoom is sufficient)
            if self.camera.zoom > 0.4:
                window_width = max(3, int(18 * self.camera.zoom))
                window_height = max(2, int(15 * self.camera.zoom))
                window_y1 = int(screen_y + 25 * self.camera.zoom)  # Upper row

                # Upper row of windows
                for i in range(min(4, max(1, int(4 * self.camera.zoom)))):
                    window_x = int(screen_x + 20 * self.camera.zoom + i * 30 * self.camera.zoom)
                    if window_x + window_width < screen_x + scaled_width:
                        # Window glass
                        pygame.draw.rect(surface, (150, 200, 255),
                                       (window_x, window_y1, window_width, window_height))
                        pygame.draw.rect(surface, (0, 0, 0),
                                       (window_x, window_y1, window_width, window_height),
                                       max(1, int(2 * self.camera.zoom)))

            # Storage tanks/silos (only if zoom is sufficient)
            if self.camera.zoom > 0.3 and warehouse.storage_indicators:
                self._draw_warehouse_storage_tanks_with_camera(surface, warehouse, screen_x, screen_y, scaled_width, scaled_height)

            # Warehouse sign (only if zoom is sufficient)
            if self.camera.zoom > 0.5:
                self._draw_warehouse_sign_with_camera(surface, screen_x, screen_y, scaled_width)

        except (ValueError, OverflowError, TypeError):
            pass

    def _draw_warehouse_storage_tanks_with_camera(self, surface, warehouse, screen_x, screen_y, scaled_width, scaled_height):
        """Draw warehouse storage tanks with camera scaling."""
        try:
            # Large storage tanks/silos on the side of the warehouse
            tank_width = max(5, int(22 * self.camera.zoom))
            tank_height = max(10, int(60 * self.camera.zoom))
            tank_spacing = max(8, int(28 * self.camera.zoom))
            start_x = int(screen_x + scaled_width + 15 * self.camera.zoom)
            start_y = int(screen_y + scaled_height - tank_height - 5 * self.camera.zoom)

            resource_colors = {
                'kelp': (50, 150, 50),
                'coral': (255, 100, 100),
                'pearl': (200, 200, 255),
                'treasure': (255, 215, 0)
            }

            for i, indicator in enumerate(warehouse.storage_indicators):
                if i >= 4:  # Limit to 4 tanks for screen space
                    break

                tank_x = start_x + i * tank_spacing
                tank_y = start_y

                # Tank base/foundation
                base_height = max(2, int(8 * self.camera.zoom))
                pygame.draw.rect(surface, (80, 80, 80),
                               (int(tank_x - 2 * self.camera.zoom),
                                tank_y + tank_height,
                                int(tank_width + 4 * self.camera.zoom),
                                base_height))

                # Tank outline with metallic appearance
                pygame.draw.rect(surface, (140, 140, 140), (tank_x, tank_y, tank_width, tank_height))
                pygame.draw.rect(surface, (0, 0, 0), (tank_x, tank_y, tank_width, tank_height),
                               max(1, int(3 * self.camera.zoom)))

                # Fill level with gradient effect
                fill_height = int(tank_height * indicator['fill_percentage'])
                if fill_height > 0:
                    fill_y = tank_y + tank_height - fill_height
                    color = resource_colors.get(indicator['resource_type'], (255, 255, 255))

                    # Main fill
                    pygame.draw.rect(surface, color,
                                   (int(tank_x + 2 * self.camera.zoom), fill_y,
                                    int(tank_width - 4 * self.camera.zoom), fill_height))

                # Tank top cap
                cap_height = max(1, int(6 * self.camera.zoom))
                pygame.draw.rect(surface, (120, 120, 120),
                               (int(tank_x - 1 * self.camera.zoom),
                                int(tank_y - cap_height),
                                int(tank_width + 2 * self.camera.zoom),
                                cap_height))

                # Resource type label (only if zoom is sufficient)
                if self.camera.zoom > 0.6:
                    font_size = max(8, int(14 * self.camera.zoom))
                    font = pygame.font.Font(None, font_size)
                    label = indicator['resource_type'][0].upper()  # First letter
                    text = font.render(label, True, (255, 255, 255))
                    text_rect = text.get_rect(center=(tank_x + tank_width // 2,
                                                    int(tank_y - 10 * self.camera.zoom)))
                    surface.blit(text, text_rect)

        except (ValueError, OverflowError, TypeError):
            pass

    def _draw_warehouse_sign_with_camera(self, surface, screen_x, screen_y, scaled_width):
        """Draw warehouse sign with camera scaling."""
        try:
            # Large prominent sign
            sign_width = max(20, int(100 * self.camera.zoom))
            sign_height = max(5, int(25 * self.camera.zoom))
            sign_x = int(screen_x + (scaled_width - sign_width) // 2)
            sign_y = int(screen_y - 40 * self.camera.zoom)

            # Sign background with border
            pygame.draw.rect(surface, (240, 240, 240), (sign_x, sign_y, sign_width, sign_height))
            pygame.draw.rect(surface, (0, 0, 0), (sign_x, sign_y, sign_width, sign_height),
                           max(1, int(3 * self.camera.zoom)))

            # Sign text - larger and more prominent (only if zoom is sufficient)
            if self.camera.zoom > 0.4:
                font_size = max(8, int(20 * self.camera.zoom))
                font = pygame.font.Font(None, font_size)
                text = font.render("WAREHOUSE", True, (0, 0, 0))
                text_rect = text.get_rect(center=(sign_x + sign_width // 2, sign_y + sign_height // 2))
                surface.blit(text, text_rect)

        except (ValueError, OverflowError, TypeError):
            pass

    def _draw_unified_collection_range_with_camera(self, surface):
        """Draw unified collection range indicator using camera transformation."""
        if hasattr(self.submarine, 'player_hook') and self.submarine.player_hook:
            player_hook = self.submarine.player_hook
            if hasattr(player_hook, 'max_range') and player_hook.is_busy():
                # Get the actual unified collection range
                if self.divers:
                    unified_range = self.divers[0].tether_range  # All divers have same range
                else:
                    unified_range = player_hook.max_range  # Fallback to player hook

                submarine_screen_pos = self.camera.world_to_screen(self.submarine.x + self.submarine.width//2,
                                                                 self.submarine.y + self.submarine.height//2)
                range_radius = max(1, min(1000, int(unified_range * self.camera.zoom)))

                # Safety check for drawing position
                if (0 <= submarine_screen_pos[0] <= surface.get_width() and
                    0 <= submarine_screen_pos[1] <= surface.get_height() and
                    range_radius > 0):
                    try:
                        center_pos = (int(submarine_screen_pos[0]), int(submarine_screen_pos[1]))
                        pygame.draw.circle(surface, (255, 215, 0), center_pos, range_radius, 1)
                    except (ValueError, OverflowError, TypeError):
                        pass



    def _highlight_resources_in_range(self, surface, submarine_center_world, grappling_range):
        """Highlight resources that are within grappling hook range."""
        for resource in self.resource_manager.resources:
            if not resource.collected:
                # Calculate distance from submarine to resource in world coordinates
                dx = resource.x - submarine_center_world[0]
                dy = resource.y - submarine_center_world[1]
                distance = math.sqrt(dx*dx + dy*dy)

                if distance <= grappling_range:
                    # Convert resource position to screen coordinates
                    resource_screen_pos = self.camera.world_to_screen(resource.x, resource.y)

                    # Check if resource is visible on screen
                    if (-50 <= resource_screen_pos[0] <= surface.get_width() + 50 and
                        -50 <= resource_screen_pos[1] <= surface.get_height() + 50):

                        # Draw highlight circle around resource
                        highlight_radius = max(10, int(20 * self.camera.zoom))
                        try:
                            pygame.draw.circle(surface, (255, 255, 255),
                                             (int(resource_screen_pos[0]), int(resource_screen_pos[1])),
                                             highlight_radius, 1)
                        except (ValueError, OverflowError):
                            pass

    def _draw_submarine_target_indicator_with_camera(self, surface):
        """Draw submarine movement target indicator using camera transformation."""
        submarine_center = self.submarine.get_center_pos()
        submarine_screen = self.camera.world_to_screen(submarine_center[0], submarine_center[1])

        # Draw mouse controller target (manual control)
        if (hasattr(self, 'mouse_controller') and
            self.mouse_controller.submarine_follow_mouse and
            hasattr(self.mouse_controller, 'mouse_target') and
            self.mouse_controller.mouse_target):

            target_x, target_y = self.mouse_controller.mouse_target

            # Convert to screen coordinates
            target_screen = self.camera.world_to_screen(target_x, target_y)

            # Draw line from submarine to target (cyan for manual control)
            pygame.draw.line(surface, (0, 255, 255), submarine_screen, target_screen, 2)

            # Draw target indicator (cyan for manual control)
            pygame.draw.circle(surface, (0, 255, 255), target_screen, int(8 * self.camera.zoom), 2)
            pygame.draw.circle(surface, (0, 255, 255), target_screen, int(4 * self.camera.zoom), 2)

        # Draw autopilot target (automatic control)
        elif (hasattr(self, 'autopilot') and
              hasattr(self.autopilot, 'movement_target') and
              self.autopilot.movement_target):

            target_x, target_y = self.autopilot.movement_target

            # Convert to screen coordinates
            target_screen = self.camera.world_to_screen(target_x, target_y)

            # Draw line from submarine to target (orange for autopilot)
            pygame.draw.line(surface, (255, 165, 0), submarine_screen, target_screen, 2)

            # Draw target indicator (orange for autopilot)
            pygame.draw.circle(surface, (255, 165, 0), target_screen, int(10 * self.camera.zoom), 2)
            pygame.draw.circle(surface, (255, 165, 0), target_screen, int(6 * self.camera.zoom), 2)

            # Add autopilot mode text near the target
            font = pygame.font.Font(None, int(24 * self.camera.zoom))
            mode_text = f"AUTO: {self.autopilot.patrol_mode}"
            text_surface = font.render(mode_text, True, (255, 165, 0))
            text_pos = (target_screen[0] + int(15 * self.camera.zoom),
                       target_screen[1] - int(15 * self.camera.zoom))
            surface.blit(text_surface, text_pos)

    def _draw_control_mode_indicator_with_camera(self, surface):
        """Draw control mode indicator using camera transformation."""
        submarine_center = self.submarine.get_center_pos()
        submarine_screen = self.camera.world_to_screen(submarine_center[0], submarine_center[1])
        indicator_x = submarine_screen[0] - int(15 * self.camera.zoom)
        indicator_y = submarine_screen[1] - int(25 * self.camera.zoom)
        indicator_size = int(8 * self.camera.zoom)

        if hasattr(self, 'mouse_controller') and self.mouse_controller.submarine_follow_mouse:
            # Manual control - red indicator
            pygame.draw.circle(surface, (255, 0, 0), (indicator_x, indicator_y), indicator_size)
        else:
            # Autonomous control - green indicator
            pygame.draw.circle(surface, (0, 255, 0), (indicator_x, indicator_y), indicator_size)

    # Note: Camera info is now shown in the unified UI system

    # Note: Focus submarine button is now handled by unified UI system

    def _draw_camera_drag_indicator(self):
        """Draw visual indicator when camera is being dragged."""
        # Draw a subtle overlay to indicate drag mode
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(30)
        overlay.fill((100, 150, 255))  # Light blue tint
        self.screen.blit(overlay, (0, 0))

        # Draw drag icon at mouse position
        mouse_pos = pygame.mouse.get_pos()
        if mouse_pos:
            # Draw crosshair cursor
            cursor_size = 20
            cursor_color = (255, 255, 255)

            # Horizontal line
            pygame.draw.line(self.screen, cursor_color,
                           (mouse_pos[0] - cursor_size//2, mouse_pos[1]),
                           (mouse_pos[0] + cursor_size//2, mouse_pos[1]), 2)
            # Vertical line
            pygame.draw.line(self.screen, cursor_color,
                           (mouse_pos[0], mouse_pos[1] - cursor_size//2),
                           (mouse_pos[0], mouse_pos[1] + cursor_size//2), 2)

            # Draw "DRAG" text near cursor
            font = self.ui.fonts.get('small', pygame.font.Font(None, 16))
            text_surface = font.render("DRAGGING", True, (255, 255, 255))
            text_x = mouse_pos[0] + 15
            text_y = mouse_pos[1] - 15
            self.screen.blit(text_surface, (text_x, text_y))

    def _draw_submarine_target_indicator(self, surface):
        """Draw indicator showing submarine's movement target."""
        if (hasattr(self.mouse_controller, 'submarine_follow_mouse') and
            self.mouse_controller.submarine_follow_mouse and
            hasattr(self.mouse_controller, 'mouse_target') and
            self.mouse_controller.mouse_target):

            target_x, target_y = self.mouse_controller.mouse_target
            submarine_center = self.submarine.get_center_pos()

            # Draw line from submarine to target
            pygame.draw.line(surface, (0, 255, 255), submarine_center, (target_x, target_y), 2)

            # Draw target indicator
            pygame.draw.circle(surface, (0, 255, 255), (int(target_x), int(target_y)), 8, 2)
            pygame.draw.circle(surface, (0, 255, 255), (int(target_x), int(target_y)), 4, 2)

    def _draw_control_mode_indicator(self, surface):
        """Draw indicator showing current control mode."""
        submarine_center = self.submarine.get_center_pos()
        indicator_x = submarine_center[0] - 15
        indicator_y = submarine_center[1] - 25

        if self.mouse_controller.submarine_follow_mouse:
            # Manual control - red indicator
            pygame.draw.circle(surface, (255, 100, 100), (indicator_x, indicator_y), 6)
            pygame.draw.circle(surface, (255, 255, 255), (indicator_x, indicator_y), 6, 1)
        elif self.autopilot.enabled:
            # Autonomous mode - green indicator
            pygame.draw.circle(surface, (100, 255, 100), (indicator_x, indicator_y), 6)
            pygame.draw.circle(surface, (255, 255, 255), (indicator_x, indicator_y), 6, 1)
        else:
            # Idle mode - yellow indicator
            pygame.draw.circle(surface, (255, 255, 100), (indicator_x, indicator_y), 6)
            pygame.draw.circle(surface, (255, 255, 255), (indicator_x, indicator_y), 6, 1)

        # Add grappling availability indicator
        grappling_indicator_x = submarine_center[0] + 20
        grappling_indicator_y = submarine_center[1] - 25

        if not self.submarine.has_grappling_hooks_aboard():
            # Grappling hooks deployed - orange waiting indicator
            pygame.draw.circle(surface, (255, 165, 0), (grappling_indicator_x, grappling_indicator_y), 4)
            pygame.draw.circle(surface, (255, 255, 255), (grappling_indicator_x, grappling_indicator_y), 4, 1)
        elif self.submarine.is_stationary():
            # Grappling available - green hook symbol
            pygame.draw.circle(surface, (100, 255, 100), (grappling_indicator_x, grappling_indicator_y), 4)
            pygame.draw.circle(surface, (255, 255, 255), (grappling_indicator_x, grappling_indicator_y), 4, 1)
        else:
            # Grappling disabled - red X
            pygame.draw.circle(surface, (255, 100, 100), (grappling_indicator_x, grappling_indicator_y), 4)
            pygame.draw.line(surface, (255, 255, 255),
                           (grappling_indicator_x - 2, grappling_indicator_y - 2),
                           (grappling_indicator_x + 2, grappling_indicator_y + 2), 1)
            pygame.draw.line(surface, (255, 255, 255),
                           (grappling_indicator_x - 2, grappling_indicator_y + 2),
                           (grappling_indicator_x + 2, grappling_indicator_y - 2), 1)

    def _draw_grappling_range_indicator_on_surface(self, surface):
        """Draw simple grappling range indicator on the game surface."""
        if hasattr(self.submarine, 'player_hook') and self.submarine.player_hook:
            player_hook = self.submarine.player_hook
            if hasattr(player_hook, 'max_range') and player_hook.is_busy():
                # Only draw a simple range circle when hook is deployed
                submarine_center = self.submarine.get_center_pos()
                pygame.draw.circle(surface, (255, 215, 0), submarine_center, player_hook.max_range, 1)

    def _interpolate_color(self, color1, color2, ratio):
        """Interpolate between two colors."""
        return tuple(int(color1[i] * (1 - ratio) + color2[i] * ratio) for i in range(3))

    def _draw_performance_info(self):
        """Draw performance information overlay."""
        font = self.ui.fonts.get('small', pygame.font.Font(None, 16))

        # Performance stats
        stats = [
            f"FPS: {self.current_fps}",
            f"Resources: {len(self.resource_manager.resources)}",
            f"Divers: {len(self.divers)}",
            f"Zoom: {self.camera.zoom:.2f}",
            f"Depth: {int(self.submarine.depth)}m"
        ]

        # Draw background
        bg_width = 150
        bg_height = len(stats) * 20 + 10
        bg_surface = pygame.Surface((bg_width, bg_height))
        bg_surface.set_alpha(180)
        bg_surface.fill((0, 0, 0))
        self.screen.blit(bg_surface, (10, 10))

        # Draw stats
        for i, stat in enumerate(stats):
            color = (255, 255, 255)
            if "FPS" in stat and self.current_fps < 30:
                color = (255, 100, 100)  # Red for low FPS
            elif "FPS" in stat and self.current_fps > 50:
                color = (100, 255, 100)  # Green for good FPS

            text_surface = font.render(stat, True, color)
            self.screen.blit(text_surface, (15, 15 + i * 20))

    def _draw_debug_info(self):
        """Draw debug information overlay for submarine automation."""
        font = self.ui.fonts.get('small', pygame.font.Font(None, 16))

        # Autopilot debug info
        autopilot_status = self.autopilot.get_status(self.submarine, self.divers)
        mouse_status = self.mouse_controller.get_status()

        # Count resources in range
        resources_in_range = self.autopilot._count_resources_in_diver_range(
            self.submarine, self.resource_manager.resources, self.divers)
        collectible_in_range = self.autopilot._count_collectible_resources_in_range(
            self.submarine, self.resource_manager.resources, self.divers)

        # Storage info
        storage_used = self.submarine.get_storage_used()
        storage_capacity = self.submarine.get_storage_capacity()
        storage_percent = (storage_used / storage_capacity * 100) if storage_capacity > 0 else 0

        # Calculate resource blocking info for debug
        uncollected_resources = [r for r in self.resource_manager.resources if not r.collected]
        collectible_resources = [r for r in uncollected_resources
                               if self.submarine.can_store_resource(r.type, 1)]
        all_blocked = len(uncollected_resources) > 0 and len(collectible_resources) == 0

        # Count uncollectable resources in range
        if hasattr(self.autopilot, '_count_uncollectable_resources_in_range'):
            uncollectable_in_range = self.autopilot._count_uncollectable_resources_in_range(
                self.submarine, self.resource_manager.resources, self.divers)
        else:
            uncollectable_in_range = 0

        # Dock status for debug
        dock_occupied = self.dock.is_occupied
        dock_progress = self.dock.unloading_progress if dock_occupied else 0
        workers_busy = sum(1 for w in self.dock.dock_workers if w.state != 'idle') if hasattr(self.dock, 'dock_workers') else 0

        # Debug stats
        debug_stats = [
            f"=== SUBMARINE DEBUG ===",
            f"Autopilot: {autopilot_status}",
            f"Control: {mouse_status}",
            f"Mode: {self.autopilot.patrol_mode}",
            f"Target: {'Yes' if self.autopilot.movement_target else 'No'}",
            f"Resources in range: {resources_in_range}",
            f"Collectible in range: {collectible_in_range}",
            f"Uncollectable in range: {uncollectable_in_range}",
            f"Total spawned: {len(uncollected_resources)}",
            f"Can collect: {len(collectible_resources)}",
            f"All blocked: {'YES' if all_blocked else 'No'}",
            f"Storage: {storage_used}/{storage_capacity} ({storage_percent:.1f}%)",
            f"Docked: {'YES' if dock_occupied else 'No'}",
            f"Dock progress: {dock_progress:.1f}" if dock_occupied else "Dock progress: N/A",
            f"Workers busy: {workers_busy}",
            f"Stationary: {'Yes' if self.submarine.is_stationary() else 'No'}",
            f"Crew aboard: {'Yes' if self.submarine.has_crew_aboard(self.divers) else 'No'}",
            f"Hooks aboard: {'Yes' if self.submarine.has_grappling_hooks_aboard() else 'No'}"
        ]

        # Draw background
        bg_width = 250
        bg_height = len(debug_stats) * 18 + 10
        bg_surface = pygame.Surface((bg_width, bg_height))
        bg_surface.set_alpha(200)
        bg_surface.fill((0, 0, 50))  # Dark blue background
        self.screen.blit(bg_surface, (SCREEN_WIDTH - bg_width - 10, 10))

        # Draw debug stats
        for i, stat in enumerate(debug_stats):
            color = (255, 255, 255)
            if "===" in stat:
                color = (255, 255, 0)  # Yellow for headers
            elif "Target: Yes" in stat:
                color = (0, 255, 0)  # Green for active target
            elif "Collectible in range: 0" in stat:
                color = (255, 100, 100)  # Red for no collectible resources
            elif "Storage:" in stat and storage_percent > 90:
                color = (255, 165, 0)  # Orange for nearly full storage

            text_surface = font.render(stat, True, color)
            self.screen.blit(text_surface, (SCREEN_WIDTH - bg_width - 5, 15 + i * 18))

    # Note: Modern UI drawing is now handled by the unified UI system





    def _draw_grappling_range_indicator(self):
        """Draw a unified collection range indicator for both divers and grappling hooks."""
        # Get playable area to convert coordinates properly
        playable_area = self.ui.get_playable_area()

        # Get submarine center in world coordinates
        submarine_center_world = self.submarine.get_center_pos()

        # Convert to screen coordinates using camera
        submarine_center = self.camera.world_to_screen(submarine_center_world[0], submarine_center_world[1])
        submarine_center = (
            submarine_center[0] + playable_area['x'],
            submarine_center[1] + playable_area['y']
        )

        # Get the actual unified collection range (same for both divers and grappling hooks)
        if self.divers:
            unified_range = self.divers[0].tether_range  # All divers have same range
        else:
            unified_range = self.submarine.player_hook.max_range  # Fallback to player hook

        player_hook_range = unified_range * self.camera.zoom

        # Get mouse position for dynamic range display
        mouse_pos = pygame.mouse.get_pos()

        # Only show range indicator if mouse is in playable area
        if not self.ui.is_point_in_playable_area(mouse_pos[0], mouse_pos[1]):
            return

        mouse_distance = math.sqrt((mouse_pos[0] - submarine_center[0])**2 +
                                 (mouse_pos[1] - submarine_center[1])**2)

        # Show range indicator when mouse is near, hook is deployed, or hovering over resources
        hook_is_busy = self.submarine.player_hook.is_busy()
        mouse_near_sub = mouse_distance <= player_hook_range * 1.3

        # Check if mouse is near any resources (convert resource positions to screen coordinates)
        mouse_near_resource = False
        for resource in self.resource_manager.resources:
            if not resource.collected and self.camera.is_visible(resource.x, resource.y):
                resource_screen_pos = self.camera.world_to_screen(resource.x, resource.y)
                resource_screen_pos = (
                    resource_screen_pos[0] + playable_area['x'],
                    resource_screen_pos[1] + playable_area['y']
                )
                res_distance = math.sqrt((resource_screen_pos[0] - mouse_pos[0])**2 + (resource_screen_pos[1] - mouse_pos[1])**2)
                if res_distance <= 40:  # Mouse near resource
                    mouse_near_resource = True
                    break

        show_range = hook_is_busy or mouse_near_sub or mouse_near_resource

        if show_range:
            # Check if submarine is stationary for grappling deployment
            submarine_stationary = self.submarine.is_stationary()

            # Determine unified collection range circle color based on state
            if hook_is_busy:
                # Collection system active - show active range in gold
                circle_color = (255, 215, 0)
                alpha = 80
            elif not submarine_stationary:
                # Submarine moving - collection disabled, show in red
                circle_color = (255, 100, 100)
                alpha = 40
            elif mouse_distance <= player_hook_range:
                # Mouse in collection range and submarine stationary - show available range in green
                circle_color = (100, 255, 150)
                alpha = 60
            else:
                # Mouse out of collection range - show range limit in blue
                circle_color = (100, 150, 255)
                alpha = 40

            # Clamp range to reasonable bounds to prevent crashes
            safe_range = max(10, min(500, int(player_hook_range)))

            # Create a surface for the transparent circle
            surface_size = safe_range * 2 + 4
            if surface_size > 0 and surface_size < 2000:  # Safety check
                range_surface = pygame.Surface((surface_size, surface_size))
                range_surface.set_alpha(alpha)
                range_surface.fill((0, 0, 0))
                range_surface.set_colorkey((0, 0, 0))

                # Draw the range circle with subtle gradient effect
                center = (int(safe_range + 2), int(safe_range + 2))

                # Draw multiple circles for gradient effect
                for i in range(3):
                    radius = safe_range - i
                    if radius > 0:
                        try:
                            pygame.draw.circle(range_surface, circle_color, center, radius, 1)
                        except (ValueError, OverflowError):
                            # Skip drawing if values are invalid
                            pass

                # Position the surface
                safe_submarine_center = (int(submarine_center[0]), int(submarine_center[1]))
                range_rect = range_surface.get_rect(center=safe_submarine_center)
                self.screen.blit(range_surface, range_rect)

            # Draw targeting line when mouse is in range
            if mouse_distance <= player_hook_range and not hook_is_busy:
                # Safety check for drawing positions
                if (0 <= submarine_center[0] <= SCREEN_WIDTH and
                    0 <= submarine_center[1] <= SCREEN_HEIGHT and
                    0 <= mouse_pos[0] <= SCREEN_WIDTH and
                    0 <= mouse_pos[1] <= SCREEN_HEIGHT):

                    # Draw subtle line from submarine to mouse
                    line_color = (255, 255, 255, 100)
                    try:
                        self._draw_dashed_line(self.screen, submarine_center, mouse_pos,
                                             line_color, 1, 8)
                        # Draw small circle at mouse position
                        safe_mouse_pos = (int(mouse_pos[0]), int(mouse_pos[1]))
                        pygame.draw.circle(self.screen, (255, 255, 255), safe_mouse_pos, 3, 1)
                    except (ValueError, OverflowError):
                        # Skip drawing if values are invalid
                        pass

            # Draw range limit indicator when mouse is outside range
            elif mouse_distance > player_hook_range and not hook_is_busy:
                # Draw line to max range in mouse direction
                if mouse_distance > 0:
                    direction_x = (mouse_pos[0] - submarine_center[0]) / mouse_distance
                    direction_y = (mouse_pos[1] - submarine_center[1]) / mouse_distance

                    end_x = submarine_center[0] + direction_x * safe_range
                    end_y = submarine_center[1] + direction_y * safe_range

                    # Safety check for drawing positions
                    if (0 <= end_x <= SCREEN_WIDTH and 0 <= end_y <= SCREEN_HEIGHT):
                        try:
                            # Draw dashed line to show max range
                            self._draw_dashed_line(self.screen, submarine_center, (end_x, end_y),
                                                 (255, 150, 150), 2, 6)

                            # Draw small circle at max range point
                            safe_end_pos = (int(end_x), int(end_y))
                            pygame.draw.circle(self.screen, (255, 150, 150), safe_end_pos, 4, 2)
                        except (ValueError, OverflowError):
                            # Skip drawing if values are invalid
                            pass

        # Highlight resources within range when showing range indicator
        if show_range and not hook_is_busy:
            for resource in self.resource_manager.resources:
                if not resource.collected and self.camera.is_visible(resource.x, resource.y):
                    # Use world coordinates for distance calculation
                    res_distance = math.sqrt((resource.x - submarine_center_world[0])**2 +
                                           (resource.y - submarine_center_world[1])**2)
                    if res_distance <= unified_range:  # Use unified collection range
                        # Draw highlight using screen coordinates
                        resource_screen_pos = self.camera.world_to_screen(resource.x, resource.y)
                        resource_screen_pos = (
                            int(resource_screen_pos[0] + playable_area['x']),
                            int(resource_screen_pos[1] + playable_area['y'])
                        )
                        highlight_radius = max(1, min(100, int(25 * self.camera.zoom)))
                        safe_resource_pos = (int(resource_screen_pos[0]), int(resource_screen_pos[1]))
                        pygame.draw.circle(self.screen, (255, 255, 255),
                                         safe_resource_pos, highlight_radius, 1)

    def _draw_dashed_line(self, surface, start_pos, end_pos, color, width, dash_length):
        """Draw a dashed line between two points with safety checks."""
        try:
            x1, y1 = float(start_pos[0]), float(start_pos[1])
            x2, y2 = float(end_pos[0]), float(end_pos[1])

            # Safety bounds check
            if (not (-10000 <= x1 <= 10000) or not (-10000 <= y1 <= 10000) or
                not (-10000 <= x2 <= 10000) or not (-10000 <= y2 <= 10000)):
                return

            # Calculate line length and direction
            dx = x2 - x1
            dy = y2 - y1
            distance = math.sqrt(dx**2 + dy**2)

            if distance == 0 or distance > 5000:  # Skip very long lines
                return

            # Normalize direction
            dx /= distance
            dy /= distance

            # Draw dashes
            current_distance = 0
            draw_dash = True
            max_iterations = 100  # Prevent infinite loops

            while current_distance < distance and max_iterations > 0:
                # Calculate dash end position
                dash_end = min(current_distance + dash_length, distance)

                if draw_dash:
                    start_x = x1 + dx * current_distance
                    start_y = y1 + dy * current_distance
                    end_x = x1 + dx * dash_end
                    end_y = y1 + dy * dash_end

                    # Safety check for line coordinates
                    if (abs(start_x) < 10000 and abs(start_y) < 10000 and
                        abs(end_x) < 10000 and abs(end_y) < 10000):
                        pygame.draw.line(surface, color[:3],
                                       (int(start_x), int(start_y)),
                                       (int(end_x), int(end_y)),
                                       max(1, min(10, width)))

                current_distance = dash_end
                draw_dash = not draw_dash
                max_iterations -= 1

        except (ValueError, OverflowError, TypeError):
            # Skip drawing if any error occurs
            pass

    def _draw_welcome_message(self):
        """Draw welcome message for new players."""
        # Semi-transparent overlay
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(180)
        overlay.fill((0, 0, 0))
        self.screen.blit(overlay, (0, 0))

        # Welcome panel
        panel_width = 600
        panel_height = 300
        panel_x = (SCREEN_WIDTH - panel_width) // 2
        panel_y = (SCREEN_HEIGHT - panel_height) // 2

        pygame.draw.rect(self.screen, (20, 50, 100), (panel_x, panel_y, panel_width, panel_height))
        pygame.draw.rect(self.screen, WHITE, (panel_x, panel_y, panel_width, panel_height), 3)

        # Title
        font_large = pygame.font.Font(None, 48)
        font_medium = pygame.font.Font(None, 32)
        font_small = pygame.font.Font(None, 24)

        title = font_large.render("🌊 Deep Sea Explorer 🌊", True, (100, 200, 255))
        title_rect = title.get_rect(center=(SCREEN_WIDTH // 2, panel_y + 50))
        self.screen.blit(title, title_rect)

        # Subtitle
        subtitle = font_medium.render("Mouse-Only Idle Submarine Game", True, YELLOW)
        subtitle_rect = subtitle.get_rect(center=(SCREEN_WIDTH // 2, panel_y + 90))
        self.screen.blit(subtitle, subtitle_rect)

        # Instructions
        instructions = [
            "🪝 Click on glowing resources to deploy your grappling hook",
            "🤖 Automated divers work alongside your hook",
            "🎯 Click empty areas to deploy hook to specific positions",
            "⚙️ Watch your submarine collect resources automatically!",
            "",
            "Click anywhere to dismiss this message"
        ]

        y_offset = 130
        for instruction in instructions:
            if instruction:  # Skip empty lines
                color = WHITE if not instruction.startswith("🖱️") else GREEN
                text = font_small.render(instruction, True, color)
                text_rect = text.get_rect(center=(SCREEN_WIDTH // 2, panel_y + y_offset))
                self.screen.blit(text, text_rect)
            y_offset += 25

    def _draw_enhanced_surface_effects(self, surface, playable_area):
        """Draw enhanced surface water effects with waves and visual barriers."""
        # Calculate fixed surface position in screen coordinates
        surface_screen_y = self.camera.world_to_screen(0, SURFACE_LEVEL)[1]

        # Only draw surface effects if surface is visible on screen
        if surface_screen_y < -100 or surface_screen_y > surface.get_height() + 100:
            return

        # Get current underwater water color for better blending
        current_water_color = self._cached_water_color if self._cached_water_color else SURFACE_BLUE

        # Create animated wave effect
        wave_time = time.time() * 2  # Wave animation speed
        wave_height = 8
        wave_frequency = 0.02

        # Draw multiple layers for depth effect
        for layer in range(3):
            layer_alpha = 200 - (layer * 50)  # Decreasing opacity for depth
            layer_offset = layer * 3

            # Create surface for this layer
            layer_surface = pygame.Surface((playable_area['width'], 60 + layer_offset), pygame.SRCALPHA)

            # Draw wave pattern
            wave_points = []
            for x in range(0, playable_area['width'] + 10, 5):
                # Create wave pattern with multiple frequencies
                wave_y = (math.sin((x + wave_time * 50) * wave_frequency) * wave_height +
                         math.sin((x + wave_time * 30) * wave_frequency * 1.5) * wave_height * 0.5)
                wave_points.append((x, 20 + wave_y + layer_offset))

            # Complete the polygon for filling
            wave_points.append((playable_area['width'], 60 + layer_offset))
            wave_points.append((0, 60 + layer_offset))

            # Draw wave layer with colors that blend with underwater
            if layer == 0:
                # Top layer - blend surface blue with current water color
                blended_color = tuple(int(SURFACE_BLUE[i] * 0.7 + current_water_color[i] * 0.3) for i in range(3))
                color = (*blended_color, layer_alpha)
            elif layer == 1:
                # Middle layer - more underwater color influence
                blended_color = tuple(int(SURFACE_BLUE[i] * 0.4 + current_water_color[i] * 0.6) for i in range(3))
                color = (*blended_color, layer_alpha)
            else:
                # Bottom layer - mostly underwater color
                blended_color = tuple(int(SURFACE_BLUE[i] * 0.2 + current_water_color[i] * 0.8) for i in range(3))
                color = (*blended_color, layer_alpha)

            # Fill the wave area
            if len(wave_points) >= 3:
                pygame.draw.polygon(layer_surface, color, wave_points)

                # Add wave highlights on top layer
                if layer == 0:
                    highlight_points = wave_points[:-2]  # Remove bottom points
                    if len(highlight_points) >= 2:
                        pygame.draw.lines(layer_surface, (*WHITE, 80), False, highlight_points, 2)

            # Blit layer to main surface
            surface.blit(layer_surface, (0, max(0, surface_screen_y - 30)))

        # Add surface reflection effect when camera is near surface
        if abs(self.camera.y - SURFACE_LEVEL) < 100:
            reflection_alpha = max(0, 100 - abs(self.camera.y - SURFACE_LEVEL))
            reflection_surface = pygame.Surface((playable_area['width'], 40), pygame.SRCALPHA)

            # Create subtle reflection gradient
            for y in range(40):
                alpha = int(reflection_alpha * (1 - y / 40))
                if alpha > 0:
                    reflection_color = (*SKY_LIGHT, alpha)
                    pygame.draw.line(reflection_surface, reflection_color,
                                   (0, y), (playable_area['width'], y))

            surface.blit(reflection_surface, (0, max(0, surface_screen_y + 20)))

        # Draw surface level indicator line
        self._draw_surface_level_indicator(surface, playable_area, surface_screen_y)

    def _draw_sky_and_surface_with_masking(self, surface, playable_area):
        """Draw sky and surface effects with proper masking so sky only appears above waves."""
        # Calculate surface position in screen coordinates (fixed world position)
        surface_screen_y = self.camera.world_to_screen(0, SURFACE_LEVEL)[1]

        # Only proceed if surface is visible on screen
        if surface_screen_y < -100 or surface_screen_y > surface.get_height() + 100:
            return

        # Only draw sky if camera can see above surface level
        if not self.sky_system.is_sky_visible(self.camera):
            # If no sky, just draw the enhanced surface effects
            self._draw_enhanced_surface_effects(surface, playable_area)
            return

        # We'll create the wave mask surface when applying it

        # Create sky surface with opaque background to prevent water color bleeding
        sky_surface = pygame.Surface((playable_area['width'], playable_area['height']))

        # Fill sky surface with proper sky color (not water color)
        sky_surface.fill(SKY_BLUE)  # Use proper sky color as base

        # Draw sky background on sky surface
        self.sky_system.draw_sky_background(sky_surface, self.camera)

        # Draw clouds on sky surface
        self.sky_system.draw_clouds(sky_surface, self.camera)

        # Create wave mask surface for efficient masking
        wave_mask_surface = self._create_wave_mask_surface(playable_area, surface_screen_y)

        # Create a clean masked sky surface
        masked_sky = pygame.Surface((playable_area['width'], playable_area['height']), pygame.SRCALPHA)

        # First, blit the sky to the masked surface
        masked_sky.blit(sky_surface, (0, 0))

        # Then use the wave mask to cut out the underwater areas
        # Convert mask to alpha mask (white = keep, black = remove)
        alpha_mask = wave_mask_surface.copy()
        alpha_mask.set_colorkey((0, 0, 0))  # Make black transparent

        # Apply the mask by blitting with alpha
        temp_surface = pygame.Surface((playable_area['width'], playable_area['height']), pygame.SRCALPHA)
        temp_surface.blit(masked_sky, (0, 0))
        temp_surface.blit(alpha_mask, (0, 0), special_flags=pygame.BLEND_RGBA_MULT)

        # Blit the final masked sky to the main surface
        surface.blit(temp_surface, (0, 0))

        # Draw the surface water effects on top
        self._draw_enhanced_surface_effects(surface, playable_area)

    def _create_wave_mask_surface(self, playable_area, surface_screen_y):
        """Create a mask surface that defines where sky should be visible (above waves)."""
        mask_surface = pygame.Surface((playable_area['width'], playable_area['height']), pygame.SRCALPHA)

        # Create animated wave effect for masking at the fixed surface position
        wave_time = time.time() * 2
        wave_height = 8
        wave_frequency = 0.02

        # Create wave points for the mask at the actual surface screen position
        wave_points = []
        for x in range(0, playable_area['width'] + 10, 2):  # More detailed wave
            # Calculate wave offset from the fixed surface position
            wave_y = (math.sin((x + wave_time * 50) * wave_frequency) * wave_height +
                     math.sin((x + wave_time * 30) * wave_frequency * 1.5) * wave_height * 0.5)
            # Wave position is relative to the fixed surface screen position
            final_wave_y = surface_screen_y + wave_y
            wave_points.append((x, final_wave_y))

        # Fill everything above waves with white (sky visible)
        if surface_screen_y > 0 and len(wave_points) > 0:
            # Create polygon for sky area (above waves)
            sky_points = [(0, 0), (playable_area['width'], 0)]
            # Add wave points in reverse order to complete the polygon
            sky_points.extend(reversed(wave_points))
            sky_points.append((0, wave_points[0][1]))

            if len(sky_points) >= 3:
                pygame.draw.polygon(mask_surface, (255, 255, 255, 255), sky_points)

        return mask_surface

    def _draw_surface_level_indicator(self, surface, playable_area, surface_screen_y):
        """Draw a clear visual indicator for the surface level boundary."""
        # Calculate the actual fixed surface position
        actual_surface_screen_y = self.camera.world_to_screen(0, SURFACE_LEVEL)[1]

        # Only draw indicator if surface is visible and camera is near surface
        if (actual_surface_screen_y < -50 or actual_surface_screen_y > surface.get_height() + 50 or
            abs(self.camera.y - SURFACE_LEVEL) > 200):
            return

        # Calculate indicator visibility based on camera distance from surface
        distance_from_surface = abs(self.camera.y - SURFACE_LEVEL)
        indicator_alpha = max(50, 255 - int(distance_from_surface * 2))

        # Draw main surface line at the fixed position
        line_y = int(actual_surface_screen_y)
        if 0 <= line_y <= surface.get_height():
            # Main surface line - bright and clear
            pygame.draw.line(surface, (*WHITE, indicator_alpha),
                           (0, line_y), (playable_area['width'], line_y), 3)

            # Add subtle glow effect
            for offset in range(1, 4):
                glow_alpha = max(10, indicator_alpha // (offset * 2))
                pygame.draw.line(surface, (*SKY_LIGHT, glow_alpha),
                               (0, line_y - offset), (playable_area['width'], line_y - offset), 1)
                pygame.draw.line(surface, (*SKY_LIGHT, glow_alpha),
                               (0, line_y + offset), (playable_area['width'], line_y + offset), 1)

        # Add depth markers every 50 units when close to surface
        if distance_from_surface < 100:
            self._draw_depth_markers(surface, playable_area, actual_surface_screen_y, indicator_alpha)

    def _draw_depth_markers(self, surface, playable_area, surface_screen_y, base_alpha):
        """Draw depth markers to help with depth perception."""
        # Draw markers above and below surface at fixed world positions
        for depth_offset in range(-200, 300, 50):  # From 200 above to 300 below surface
            if depth_offset == 0:  # Skip surface level (already drawn)
                continue

            # Calculate fixed world position for this depth marker
            marker_world_y = SURFACE_LEVEL + depth_offset
            marker_screen_x, marker_screen_y = self.camera.world_to_screen(0, marker_world_y)

            # Only draw if visible on screen
            if 0 <= marker_screen_y <= surface.get_height():
                # Calculate marker alpha based on distance from surface
                marker_alpha = max(20, base_alpha // (abs(depth_offset) // 25 + 1))

                # Different colors for above/below surface
                if depth_offset < 0:  # Above surface (sky)
                    marker_color = (*SKY_BLUE, marker_alpha)
                    marker_length = 20
                else:  # Below surface (underwater)
                    marker_color = (*DEEP_BLUE, marker_alpha)
                    marker_length = 15

                # Draw depth marker line at fixed world position
                start_x = playable_area['width'] - 60
                end_x = playable_area['width'] - 10
                pygame.draw.line(surface, marker_color,
                               (start_x, marker_screen_y), (end_x, marker_screen_y), 2)

                # Add depth label for major markers
                if abs(depth_offset) % 100 == 0 and marker_alpha > 50:
                    depth_text = f"{abs(depth_offset)}m"
                    if hasattr(self, 'ui') and hasattr(self.ui, 'font_small'):
                        text_surface = self.ui.font_small.render(depth_text, True, marker_color[:3])
                        text_rect = text_surface.get_rect()
                        text_rect.right = start_x - 5
                        text_rect.centery = marker_screen_y

                        # Add text background for readability
                        bg_rect = text_rect.inflate(4, 2)
                        bg_surface = pygame.Surface(bg_rect.size, pygame.SRCALPHA)
                        bg_surface.fill((0, 0, 0, 100))
                        surface.blit(bg_surface, bg_rect)
                        surface.blit(text_surface, text_rect)

    def _draw_surface_debug_info(self):
        """Draw debug information about surface visibility."""
        if not hasattr(self, 'ui') or not hasattr(self.ui, 'font_small'):
            return

        debug_lines = [
            f"Camera Y: {self.camera.y:.1f}",
            f"Surface Level: {SURFACE_LEVEL}",
            f"Camera vs Surface: {self.camera.y - SURFACE_LEVEL:.1f}",
            f"Submarine Depth: {self.submarine.depth:.1f}",
            f"Sky Visible: {self.sky_system.is_sky_visible(self.camera)}",
            f"Submarine Docked: {getattr(self.submarine, 'is_docked', False)}"
        ]

        y_offset = 10
        for line in debug_lines:
            text_surface = self.ui.font_small.render(line, True, WHITE)
            # Add background for readability
            bg_rect = text_surface.get_rect()
            bg_rect.x = 10
            bg_rect.y = y_offset
            bg_surface = pygame.Surface(bg_rect.size, pygame.SRCALPHA)
            bg_surface.fill((0, 0, 0, 150))
            self.screen.blit(bg_surface, bg_rect)
            self.screen.blit(text_surface, (10, y_offset))
            y_offset += 20

    def run(self):
        """Main game loop."""
        while self.running:
            dt = self.clock.tick(FPS)
            
            self.handle_events()
            self.update(dt)
            self.draw()
            
            pygame.display.flip()
            
        pygame.quit()
        sys.exit()
