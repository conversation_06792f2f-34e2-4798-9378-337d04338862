"""UI container system for managing different UI panels in separated regions."""

import pygame
import math
from .constants import *
from .separated_ui import SeparatedUI

class UIContainer:
    """Base class for UI containers that manage specific UI regions."""
    
    def __init__(self, separated_ui, region_name):
        self.separated_ui = separated_ui
        self.region_name = region_name
        self.region = separated_ui.get_region(region_name)
        self.colors = separated_ui.colors
        self.fonts = {
            'small': separated_ui.font_small,
            'medium': separated_ui.font_medium,
            'large': separated_ui.font_large,
            'title': separated_ui.font_title
        }
        self.margin = separated_ui.ui_margin
        
    def draw(self, screen, game_state):
        """Override in subclasses to draw container content."""
        pass
    
    def handle_click(self, x, y, game_state):
        """Override in subclasses to handle clicks within the container."""
        # Convert global coordinates to local container coordinates
        local_x = x - self.region['x']
        local_y = y - self.region['y']
        return self._handle_local_click(local_x, local_y, game_state)
    
    def _handle_local_click(self, local_x, local_y, game_state):
        """Handle clicks in local container coordinates."""
        return None
    
    def draw_text(self, surface, text, x, y, font_size='medium', color=None):
        """Helper method to draw text."""
        if color is None:
            color = self.colors['text']
        font = self.fonts[font_size]
        text_surface = font.render(str(text), True, color)
        surface.blit(text_surface, (x, y))
        return text_surface.get_height()

class TopUIContainer(UIContainer):
    """Container for top UI bar - depth, status indicators."""
    
    def __init__(self, separated_ui):
        super().__init__(separated_ui, 'top_ui')
        
    def draw(self, screen, game_state):
        # Create surface for this region
        surface = pygame.Surface((self.region['width'], self.region['height']))
        surface.fill(self.colors['ui_bg'])
        
        submarine = game_state['submarine']
        
        # Draw depth indicator
        depth_text = f"Depth: {int(submarine.depth)}m"
        self.draw_text(surface, depth_text, self.margin, self.margin, 'large')
        
        # Draw max depth
        max_depth_text = f"Max: {submarine.max_depth}m"
        self.draw_text(surface, max_depth_text, 200, self.margin, 'medium')
        
        # Draw speed
        speed_text = f"Speed: {submarine.speed}"
        self.draw_text(surface, speed_text, 350, self.margin, 'medium')
        
        # Draw oxygen level (right side)
        resources = game_state['resources']
        oxygen_text = f"Oxygen: {resources.get('oxygen', 0)}"
        oxygen_color = self.colors['success'] if resources.get('oxygen', 0) > 50 else self.colors['warning']
        text_width = self.fonts['medium'].size(oxygen_text)[0]
        self.draw_text(surface, oxygen_text, self.region['width'] - text_width - self.margin, 
                      self.margin, 'medium', oxygen_color)
        
        # Draw pause indicator if paused
        if game_state.get('paused', False):
            pause_text = "⏸️ PAUSED"
            pause_width = self.fonts['large'].size(pause_text)[0]
            self.draw_text(surface, pause_text, 
                          (self.region['width'] - pause_width) // 2, 
                          self.margin, 'large', self.colors['warning'])
        
        screen.blit(surface, (self.region['x'], self.region['y']))

class LeftUIContainer(UIContainer):
    """Container for left UI panel - controls and automation."""
    
    def __init__(self, separated_ui):
        super().__init__(separated_ui, 'left_ui')
        
    def draw(self, screen, game_state):
        surface = pygame.Surface((self.region['width'], self.region['height']))
        surface.fill(self.colors['ui_bg'])
        
        y_offset = self.margin
        
        # Title
        y_offset += self.draw_text(surface, "🎮 CONTROLS", self.margin, y_offset, 'medium', self.colors['accent'])
        y_offset += 10
        
        # Compact control instructions
        controls = [
            "🖱️ Mouse Controls",
            "🪝 Click Resource",
            "🎯 Click Empty Area",
            "🤖 Auto Divers",
            "⚙️ Toggle Below:"
        ]

        for control in controls:
            color = self.colors['success'] if control.startswith("🖱️") else self.colors['text']
            y_offset += self.draw_text(surface, control, self.margin, y_offset, 'small', color)
            y_offset += 3  # Tighter spacing
        
        # Compact automation status
        y_offset += 15
        automation = game_state.get('automation', {})

        # Auto craft button
        auto_craft_color = self.colors['success'] if automation.get('auto_craft', False) else self.colors['text_dim']
        y_offset += self.draw_text(surface, "⚙️ AutoCraft", self.margin, y_offset, 'small', auto_craft_color)
        y_offset += 18

        # Mouse follow button
        mouse_follow_color = self.colors['success'] if automation.get('mouse_follow', False) else self.colors['text_dim']
        y_offset += self.draw_text(surface, "🖱️ Follow", self.margin, y_offset, 'small', mouse_follow_color)
        
        screen.blit(surface, (self.region['x'], self.region['y']))
    
    def _handle_local_click(self, local_x, local_y, game_state):
        # Check for automation toggle clicks
        # Auto craft button area (approximate)
        if 200 <= local_y <= 220:
            return {'action': 'toggle_auto_craft'}
        # Mouse follow button area (approximate)
        elif 225 <= local_y <= 245:
            return {'action': 'toggle_mouse_follow'}
        return None

class RightUIContainer(UIContainer):
    """Container for right UI panel - resources, crew, upgrades."""
    
    def __init__(self, separated_ui):
        super().__init__(separated_ui, 'right_ui')
        
    def draw(self, screen, game_state):
        surface = pygame.Surface((self.region['width'], self.region['height']))
        surface.fill(self.colors['ui_bg'])
        
        y_offset = self.margin
        
        # Resources section
        y_offset += self.draw_text(surface, "📦 RESOURCES", self.margin, y_offset, 'medium', self.colors['accent'])
        y_offset += 10
        
        resources = game_state['resources']
        for resource_name, amount in resources.items():
            if resource_name != 'oxygen':  # Oxygen shown in top bar
                resource_info = RESOURCE_TYPES.get(resource_name, {'color': WHITE})
                color = resource_info['color']
                # More compact format
                short_name = resource_name[:4].title()  # Truncate long names
                y_offset += self.draw_text(surface, f"{short_name}: {amount}",
                                         self.margin, y_offset, 'small', color)
                y_offset += 3  # Tighter spacing

        y_offset += 10
        
        # Crew section
        y_offset += self.draw_text(surface, "👥 CREW STATUS", self.margin, y_offset, 'medium', self.colors['accent'])
        y_offset += 10
        
        divers = game_state['divers']
        active_divers = sum(1 for diver in divers if diver.state != 'idle')
        total_divers = len(divers)
        
        y_offset += self.draw_text(surface, f"Active: {active_divers}/{total_divers}", 
                                 self.margin, y_offset, 'small')
        y_offset += 5
        
        # Show individual diver status (more compact)
        for i, diver in enumerate(divers[:3]):  # Show first 3 divers only
            status_color = self.colors['success'] if diver.state == 'collecting' else self.colors['text_dim']
            status_text = f"D{i+1}: {diver.state[:4]}"  # Abbreviated
            y_offset += self.draw_text(surface, status_text, self.margin, y_offset, 'small', status_color)
            y_offset += 2

        y_offset += 10
        
        # Grappling hook status
        y_offset += self.draw_text(surface, "🪝 GRAPPLING", self.margin, y_offset, 'medium', self.colors['accent'])
        y_offset += 10
        
        submarine = game_state['submarine']
        if hasattr(submarine, 'grappling_hook') and submarine.grappling_hook:
            hook = submarine.grappling_hook
            if hook.deployed:
                hook_status = "Deployed"
                hook_color = self.colors['warning']
            else:
                hook_status = "Ready"
                hook_color = self.colors['success']
        else:
            hook_status = "Not Available"
            hook_color = self.colors['text_dim']
            
        y_offset += self.draw_text(surface, f"Status: {hook_status}", 
                                 self.margin, y_offset, 'small', hook_color)
        
        screen.blit(surface, (self.region['x'], self.region['y']))

class BottomUIContainer(UIContainer):
    """Container for bottom UI bar - quick actions and notifications."""
    
    def __init__(self, separated_ui):
        super().__init__(separated_ui, 'bottom_ui')
        
    def draw(self, screen, game_state):
        surface = pygame.Surface((self.region['width'], self.region['height']))
        surface.fill(self.colors['ui_bg'])

        # Compact quick actions
        actions = [
            "⏸️ SPACE",
            "❌ ESC",
            "⚙️ Upgrades",
            "🏠 Dock"
        ]

        x_offset = self.margin
        for i, action in enumerate(actions):
            if i < 2:  # First 2 are controls
                self.draw_text(surface, action, x_offset, self.margin, 'small')
                x_offset += 80
            else:  # Last 2 are shop buttons
                button_color = self.colors['primary'] if i == 2 else self.colors['secondary']
                self.draw_text(surface, action, x_offset, self.margin, 'small', button_color)
                x_offset += 80

        # Show notifications or status messages
        if game_state.get('notification'):
            notification = game_state['notification']
            notif_width = self.fonts['medium'].size(notification)[0]
            self.draw_text(surface, notification,
                          self.region['width'] - notif_width - self.margin,
                          self.margin, 'medium', self.colors['warning'])

        screen.blit(surface, (self.region['x'], self.region['y']))

    def _handle_local_click(self, local_x, local_y, game_state):
        # Check for shop button clicks (updated for compact layout)
        if self.margin <= local_y <= self.margin + 20:  # Button row
            if 160 <= local_x <= 240:  # Upgrades button area
                return {'action': 'toggle_upgrade_shop'}
        return None

class SeparatedUIManager:
    """Manager for all separated UI containers."""
    
    def __init__(self, screen_width, screen_height):
        self.separated_ui = SeparatedUI(screen_width, screen_height)
        
        # Initialize containers
        self.containers = {
            'top': TopUIContainer(self.separated_ui),
            'left': LeftUIContainer(self.separated_ui),
            'right': RightUIContainer(self.separated_ui),
            'bottom': BottomUIContainer(self.separated_ui)
        }
    
    def get_playable_area(self):
        """Get the playable area region."""
        return self.separated_ui.get_playable_area()
    
    def is_point_in_playable_area(self, x, y):
        """Check if a point is in the playable area."""
        return self.separated_ui.is_point_in_playable_area(x, y)
    
    def get_region_for_point(self, x, y):
        """Get which region a point belongs to."""
        return self.separated_ui.get_region_for_point(x, y)
    
    def handle_click(self, x, y, game_state):
        """Handle clicks and route to appropriate container."""
        region = self.get_region_for_point(x, y)
        
        if region == 'playable':
            return {'action': 'game_click', 'x': x, 'y': y}
        elif region in ['top', 'left', 'right', 'bottom']:
            container = self.containers[region]
            return container.handle_click(x, y, game_state)
        
        return None
    
    def draw(self, screen, game_state):
        """Draw all UI containers."""
        # Draw UI backgrounds first
        self.separated_ui.draw_ui_backgrounds(screen)
        
        # Draw each container
        for container in self.containers.values():
            container.draw(screen, game_state)
    
    def resize(self, new_width, new_height):
        """Handle screen resize."""
        self.separated_ui = SeparatedUI(new_width, new_height)
        
        # Recreate containers with new layout
        self.containers = {
            'top': TopUIContainer(self.separated_ui),
            'left': LeftUIContainer(self.separated_ui),
            'right': RightUIContainer(self.separated_ui),
            'bottom': BottomUIContainer(self.separated_ui)
        }
