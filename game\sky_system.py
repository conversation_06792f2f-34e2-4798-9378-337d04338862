"""Sky and cloud system for the submarine game."""

import pygame
import math
import random
from .constants import *

class Cloud:
    """Individual cloud with position, size, and animation."""
    
    def __init__(self, x, y, size=1.0, speed=0.5):
        self.x = x
        self.y = y
        self.size = size
        self.speed = speed
        self.opacity = random.randint(180, 255)
        self.drift_offset = random.random() * math.pi * 2
        self.drift_speed = random.uniform(0.1, 0.3)
        
        # Cloud shape - multiple circles for fluffy appearance
        self.circles = []
        num_circles = random.randint(3, 6)
        for i in range(num_circles):
            circle_x = random.uniform(-30, 30) * size
            circle_y = random.uniform(-15, 15) * size
            circle_radius = random.uniform(15, 35) * size
            self.circles.append((circle_x, circle_y, circle_radius))
    
    def update(self, dt):
        """Update cloud position and animation."""
        # Horizontal movement
        self.x += self.speed * dt * 60
        
        # Gentle vertical drift
        self.drift_offset += self.drift_speed * dt
        
        # Wrap around world width
        if self.x > WORLD_WIDTH + 200:
            self.x = -200
    
    def draw_with_camera(self, surface, camera):
        """Draw cloud with camera transformation."""
        # Add gentle vertical drift
        drift_y = math.sin(self.drift_offset) * 5 * self.size
        cloud_y = self.y + drift_y
        
        screen_x, screen_y = camera.world_to_screen(self.x, cloud_y)
        
        # Only draw if visible on screen
        if (-100 <= screen_x <= surface.get_width() + 100 and
            -100 <= screen_y <= surface.get_height() + 100):
            
            # Create cloud surface with alpha
            cloud_surface = pygame.Surface((200 * self.size, 100 * self.size), pygame.SRCALPHA)
            
            # Draw cloud circles
            for circle_x, circle_y, radius in self.circles:
                scaled_radius = max(1, int(radius * camera.zoom))
                circle_screen_x = int((200 * self.size) / 2 + circle_x * camera.zoom)
                circle_screen_y = int((100 * self.size) / 2 + circle_y * camera.zoom)
                
                # Draw cloud with varying opacity
                cloud_color = (*CLOUD_WHITE, self.opacity)
                pygame.draw.circle(cloud_surface, cloud_color, 
                                 (circle_screen_x, circle_screen_y), scaled_radius)
                
                # Add subtle gray shading for depth
                if scaled_radius > 5:
                    shadow_color = (*CLOUD_GRAY, self.opacity // 2)
                    shadow_offset = max(1, int(2 * camera.zoom))
                    pygame.draw.circle(cloud_surface, shadow_color,
                                     (circle_screen_x + shadow_offset, 
                                      circle_screen_y + shadow_offset), 
                                     scaled_radius - shadow_offset)
            
            # Blit cloud to main surface
            surface.blit(cloud_surface, 
                        (screen_x - (200 * self.size) / 2, 
                         screen_y - (100 * self.size) / 2))

class SkySystem:
    """Manages sky rendering and cloud animations."""
    
    def __init__(self):
        self.clouds = []
        self.generate_clouds()
        
        # Sky gradient colors
        self.sky_top = SKY_BLUE
        self.sky_bottom = SKY_LIGHT
        self.horizon_color = HORIZON_BLUE
        
    def generate_clouds(self):
        """Generate initial cloud field."""
        self.clouds = []
        
        # Generate clouds across the world width
        for i in range(15):  # Number of clouds
            x = random.uniform(-200, WORLD_WIDTH + 200)
            y = random.uniform(SURFACE_LEVEL - 150, SURFACE_LEVEL - 50)  # Above surface
            size = random.uniform(0.5, 1.5)
            speed = random.uniform(0.3, 0.8)
            
            cloud = Cloud(x, y, size, speed)
            self.clouds.append(cloud)
    
    def update(self, dt):
        """Update all clouds."""
        for cloud in self.clouds:
            cloud.update(dt)
    
    def is_sky_visible(self, camera):
        """Check if sky should be visible based on camera position."""
        # Sky is visible when camera is at or above surface level
        return camera.y <= SURFACE_LEVEL + 20
    
    def draw_sky_background(self, surface, camera):
        """Draw sky gradient background."""
        if not self.is_sky_visible(camera):
            return

        # Calculate surface position on screen
        surface_screen_y = camera.world_to_screen(0, SURFACE_LEVEL)[1]

        # Draw sky above the surface line
        if surface_screen_y > 0:
            sky_height = min(surface_screen_y, surface.get_height())

            # Draw gradient from top to surface
            for y in range(int(sky_height)):
                # Interpolate between sky colors
                ratio = y / sky_height if sky_height > 0 else 0

                # Blend sky colors
                r = int(self.sky_top[0] * (1 - ratio) + self.horizon_color[0] * ratio)
                g = int(self.sky_top[1] * (1 - ratio) + self.horizon_color[1] * ratio)
                b = int(self.sky_top[2] * (1 - ratio) + self.horizon_color[2] * ratio)

                color = (r, g, b)
                pygame.draw.line(surface, color, (0, y), (surface.get_width(), y))
    
    def draw_clouds(self, surface, camera):
        """Draw all clouds."""
        if not self.is_sky_visible(camera):
            return
        
        # Sort clouds by y position for proper layering
        sorted_clouds = sorted(self.clouds, key=lambda c: c.y, reverse=True)
        
        for cloud in sorted_clouds:
            cloud.draw_with_camera(surface, camera)
    
    def draw_with_camera(self, surface, camera):
        """Draw complete sky system with camera transformation."""
        # Draw sky background first
        self.draw_sky_background(surface, camera)
        
        # Then draw clouds
        self.draw_clouds(surface, camera)
