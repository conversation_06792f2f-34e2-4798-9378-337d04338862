"""Submarine class for the Deep Sea Explorer game."""

import pygame
import math
from .constants import *
from .grappling_hook import GrapplingSystem, PlayerGrapplingHook

class Submarine:
    """Main submarine that the player controls."""
    
    def __init__(self, x, y):
        self.x = x
        self.y = y

        # Responsive sizing
        scale_factor = min(SCREEN_WIDTH / DEFAULT_SCREEN_WIDTH, SCREEN_HEIGHT / DEFAULT_SCREEN_HEIGHT)
        self.width = int(80 * max(0.6, scale_factor))
        self.height = int(30 * max(0.6, scale_factor))
        self.speed = SUBMARINE_SPEED
        self.depth = 0
        self.max_depth = MAX_DEPTH

        # Upgrades
        self.speed_upgrades = 0
        self.depth_upgrades = 0
        self.hull_upgrades = 0
        self.storage_upgrades = 0

        # Storage system
        self.storage = {
            'kelp': 0,
            'coral': 0,
            'pearl': 0,
            'treasure': 0
        }
        self.base_storage_capacity = 20  # Base storage per resource type
        self.storage_upgrade_bonus = 10  # Additional storage per upgrade level

        # Visual effects
        self.bob_offset = 0
        self.bob_speed = 0.05

        # Movement tracking
        self.prev_x = x
        self.prev_y = y
        self.is_moving = False
        self.movement_threshold = 1.5  # Increased from 0.5 to 1.5 to ignore tiny movements
        self.stationary_timer = 0
        self.stationary_delay = 10  # Balanced delay for responsive diver deployment

        # Grappling systems
        self.grappling_system = GrapplingSystem(self)  # Automated hooks
        self.player_hook = PlayerGrapplingHook(self)    # Player-controlled hook

        # Docking state tracking
        self.is_docked = False
        self.dock_reference = None
        
    def update(self, dt, resources):
        """Update submarine and grappling systems."""
        # Update movement tracking
        self._update_movement_tracking()

        # Help submarine become stationary when appropriate
        self.force_stationary_if_idle()

        # Update visual effects - dt is now in seconds
        self.bob_offset += self.bob_speed * dt * 60  # Scale for visible animation speed

        # Only update grappling systems if not docked
        all_collected = []
        if not self.is_docked:
            # Update automated grappling system
            auto_collected = self.grappling_system.update(resources, dt)

            # Update player grappling hook
            player_collected = self.player_hook.update(resources, dt)

            # Combine collected resources
            all_collected = auto_collected[:]
            if player_collected:
                all_collected.append(player_collected)

        return all_collected

    def get_storage_capacity(self, resource_type=None):
        """Get current storage capacity for a resource type or total."""
        capacity_per_type = self.base_storage_capacity + (self.storage_upgrades * self.storage_upgrade_bonus)
        if resource_type:
            return capacity_per_type
        return capacity_per_type * len(self.storage)

    def get_storage_used(self, resource_type=None):
        """Get current storage used for a resource type or total."""
        if resource_type:
            return self.storage.get(resource_type, 0)
        return sum(self.storage.values())

    def get_storage_free(self, resource_type=None):
        """Get free storage space for a resource type or total."""
        if resource_type:
            return self.get_storage_capacity(resource_type) - self.get_storage_used(resource_type)
        return self.get_storage_capacity() - self.get_storage_used()

    def is_storage_full(self, resource_type=None):
        """Check if storage is full for a resource type or overall."""
        if resource_type:
            return self.get_storage_free(resource_type) <= 0
        return self.get_storage_free() <= 0

    def can_store_resource(self, resource_type, amount=1):
        """Check if submarine can store a specific amount of a resource."""
        return self.get_storage_free(resource_type) >= amount

    def store_resource(self, resource_type, amount=1):
        """Store a resource in submarine storage. Returns amount actually stored."""
        if resource_type not in self.storage:
            return 0

        max_storable = self.get_storage_free(resource_type)
        actual_stored = min(amount, max_storable)
        self.storage[resource_type] += actual_stored
        return actual_stored

    def remove_resource(self, resource_type, amount=1):
        """Remove a resource from submarine storage. Returns amount actually removed."""
        if resource_type not in self.storage:
            return 0

        actual_removed = min(amount, self.storage[resource_type])
        self.storage[resource_type] -= actual_removed
        return actual_removed

    def get_storage_percentage(self):
        """Get storage usage as a percentage (0.0 to 1.0)."""
        total_capacity = self.get_storage_capacity()
        if total_capacity == 0:
            return 0.0
        return self.get_storage_used() / total_capacity
        
    def _update_movement_tracking(self):
        """Update movement tracking to determine if submarine is moving."""
        # Calculate movement since last frame
        dx = abs(self.x - self.prev_x)
        dy = abs(self.y - self.prev_y)
        movement_distance = math.sqrt(dx**2 + dy**2)

        # Simplified movement state tracking - keep it working like before but with anti-stuck measures
        if movement_distance > self.movement_threshold:
            self.is_moving = True
            self.stationary_timer = 0
        else:
            # Only consider truly stationary if movement is very small
            if movement_distance < 0.1:  # Very small movements are ignored
                self.is_moving = False
                self.stationary_timer += 1
            # If movement is between 0.1 and threshold, don't reset timer but don't increment either

        # Update previous position
        self.prev_x = self.x
        self.prev_y = self.y

    def is_stationary(self):
        """Check if submarine is stationary and has been for the required delay."""
        # More lenient stationary check - consider stationary if timer is high enough
        # even if is_moving is True (to handle micro-movements)
        return (not self.is_moving and self.stationary_timer >= self.stationary_delay) or \
               (self.stationary_timer >= self.stationary_delay * 2)  # Force stationary after longer delay

    def force_stationary_if_idle(self):
        """Force submarine to be considered stationary if no active movement systems."""
        # If submarine has been relatively still for a while, consider it stationary
        if self.stationary_timer >= 15:  # Half second of minimal movement
            self.is_moving = False
            self.stationary_timer = max(self.stationary_timer, self.stationary_delay)

        # Anti-stuck mechanism: if submarine hasn't moved significantly in a longer period,
        # reset movement tracking to prevent permanent stuck state
        if hasattr(self, 'position_history'):
            self.position_history.append((self.x, self.y))
            if len(self.position_history) > 180:  # Keep 3 seconds of history
                self.position_history.pop(0)

                # Check if submarine has been in roughly the same area
                if len(self.position_history) >= 180:
                    old_x, old_y = self.position_history[0]
                    current_distance = math.sqrt((self.x - old_x)**2 + (self.y - old_y)**2)
                    if current_distance < 10:  # Less than 10 pixels in 3 seconds
                        # Force reset movement state to prevent permanent stuck
                        self.is_moving = False
                        self.stationary_timer = self.stationary_delay + 10
        else:
            self.position_history = [(self.x, self.y)]

    def has_crew_aboard(self, divers):
        """Check if all crew members are aboard the submarine."""
        submarine_pos = self.get_center_pos()

        for diver in divers:
            # Check if diver is not idle (still deployed)
            if diver.state != 'idle':
                return False

            # Additional safety check: ensure diver is actually near submarine
            # Divers should be inside submarine when idle (small offset from center)
            distance = math.sqrt((diver.x - submarine_pos[0])**2 + (diver.y - submarine_pos[1])**2)
            if distance > 15:  # Divers should be within 15 pixels of submarine center when idle
                return False

        return True

    def has_grappling_hooks_aboard(self):
        """Check if all grappling hooks are aboard the submarine (idle state)."""
        # Check automated grappling hooks
        for hook in self.grappling_system.hooks:
            if hook.state != 'idle':
                return False

        # Check player grappling hook
        if self.player_hook.is_busy():
            return False

        return True

    def set_docked_state(self, is_docked, dock_reference=None):
        """Set the submarine's docked state."""
        self.is_docked = is_docked
        self.dock_reference = dock_reference

        # If docking, force all grappling hooks to return
        if is_docked:
            if hasattr(self, 'grappling_system'):
                self.grappling_system.force_all_hooks_return()
            if hasattr(self, 'player_hook'):
                self.player_hook.force_return()

    def dock_diver(self, diver):
        """Properly dock a diver to the submarine."""
        submarine_pos = self.get_center_pos()
        diver.x = submarine_pos[0]
        diver.y = submarine_pos[1]
        diver.state = 'idle'
        diver.oxygen = diver.max_oxygen

    def has_crew_in_emergency(self, divers):
        """Check if any crew members are in emergency situations."""
        submarine_pos = self.get_center_pos()

        for diver in divers:
            if diver.state != 'idle':
                # Check if diver is too far away
                distance = math.sqrt((diver.x - submarine_pos[0])**2 + (diver.y - submarine_pos[1])**2)
                if distance > diver.tether_range * 1.2:  # 20% beyond tether range
                    return True

                # Check if diver has critically low oxygen
                if diver.oxygen <= 15:
                    return True

        return False

    def get_active_divers(self):
        """Get list of divers that are currently active (not idle)."""
        # This method will be called by grappling hooks to check if divers are working
        # We need access to the divers list from the game, so this will be set externally
        if hasattr(self, '_divers_reference'):
            return [diver for diver in self._divers_reference if diver.state != 'idle']
        return []

    def get_diver_targeted_resources(self):
        """Get set of resources that divers are currently targeting or working on."""
        targeted_resources = set()
        if hasattr(self, '_divers_reference'):
            for diver in self._divers_reference:
                if diver.target_resource and diver.state in ['swimming', 'collecting']:
                    targeted_resources.add(diver.target_resource)
        return targeted_resources

    def get_grappling_hook_targeted_resources(self):
        """Get set of resources that grappling hooks are currently targeting or working on."""
        targeted_resources = set()

        # Check automated grappling hooks
        if hasattr(self, 'grappling_system') and self.grappling_system:
            for hook in self.grappling_system.hooks:
                if hook.target_resource and hook.state in ['extending', 'collecting', 'retracting']:
                    targeted_resources.add(hook.target_resource)

        # Check player grappling hook
        if hasattr(self, 'player_hook') and self.player_hook:
            if hasattr(self.player_hook, 'hook') and self.player_hook.hook:
                if self.player_hook.hook.target_resource and self.player_hook.hook.state in ['extending', 'collecting', 'retracting']:
                    targeted_resources.add(self.player_hook.hook.target_resource)

        return targeted_resources

    def set_divers_reference(self, divers):
        """Set reference to divers list for coordination with grappling hooks."""
        self._divers_reference = divers

    def is_potentially_stuck(self):
        """Check if submarine might be stuck and needs intervention."""
        # Check if submarine has been stationary for too long
        if hasattr(self, 'stuck_timer') and self.stuck_timer > 300:  # 5 seconds
            return True

        # Check position history for lack of movement
        if hasattr(self, 'position_history') and len(self.position_history) >= 180:
            old_x, old_y = self.position_history[0]
            current_distance = math.sqrt((self.x - old_x)**2 + (self.y - old_y)**2)
            if current_distance < 5:  # Very little movement in 3 seconds
                return True

        return False

    def resolve_stuck_state(self, divers=None):
        """Attempt to resolve a stuck state by forcing systems to return."""
        if hasattr(self, 'stuck_timer'):
            self.stuck_timer = 0

        # Force movement state reset
        self.is_moving = False
        self.stationary_timer = self.stationary_delay + 10

        # If divers are deployed, force one to return
        if divers:
            for diver in divers:
                if diver.state != 'idle':
                    diver.state = 'returning'
                    diver.target_x, diver.target_y = self.get_center_pos()
                    break

        # Force grappling hooks to return
        if hasattr(self, 'grappling_system'):
            self.grappling_system.force_all_hooks_return()
        if hasattr(self, 'player_hook'):
            self.player_hook.force_return()

    def get_visual_y(self):
        """Get y position with bobbing effect."""
        if self.depth == 0:  # Only bob at surface
            return self.y + math.sin(self.bob_offset) * 2
        return self.y
        
    def draw(self, screen, oxygen_level=1000, divers=None):
        """Draw the submarine with upgrades and grappling system."""
        visual_y = self.get_visual_y()

        # Main submarine body (oval shape)
        hull_color = (70, 130, 180)
        if self.hull_upgrades > 0:
            hull_color = (90, 150, 200)  # Upgraded hull color

        # Change color when oxygen is low
        if oxygen_level <= 100:
            hull_color = (180, 70, 70)  # Red when oxygen is critically low
        elif oxygen_level <= 200:
            hull_color = (180, 130, 70)  # Orange when oxygen is low
        # Change color when waiting for crew
        elif divers and not self.has_crew_aboard(divers):
            hull_color = (150, 100, 180)  # Purple when waiting for crew

        pygame.draw.ellipse(screen, hull_color,
                          (self.x, visual_y, self.width, self.height))

        # Submarine outline
        pygame.draw.ellipse(screen, WHITE,
                          (self.x, visual_y, self.width, self.height), 2)

        # Periscope/conning tower - positioned within submarine bounds
        tower_color = (50, 100, 150)
        if self.depth_upgrades > 0:
            tower_color = (100, 150, 200)  # Upgraded color

        # Draw tower within submarine bounds, not extending above
        tower_width = 10
        tower_height = min(12, self.height - 4)  # Ensure tower fits within submarine height
        tower_x = self.x + self.width//2 - tower_width//2
        tower_y = visual_y + 2  # Position inside submarine, not above it
        pygame.draw.rect(screen, tower_color,
                        (tower_x, tower_y, tower_width, tower_height))

        # Grappling hook mounts (visual indicators) - positioned within submarine bounds
        hook_count = len(self.grappling_system.hooks)
        if hook_count > 0:
            mount_spacing = min(15, (self.width - 20) // max(1, hook_count))  # Ensure mounts fit within submarine
            start_x = self.x + 10  # Start 10 pixels from left edge
            for i in range(hook_count):
                mount_x = start_x + (i * mount_spacing)
                mount_y = visual_y + self.height - 5  # Position on bottom of submarine, not below it
                if mount_x < self.x + self.width - 10:  # Ensure mount is within submarine bounds
                    pygame.draw.circle(screen, (150, 150, 150), (mount_x, mount_y), 2)  # Smaller mounts
                    pygame.draw.circle(screen, WHITE, (mount_x, mount_y), 2, 1)

        # Speed upgrade indicators (fins)
        if self.speed_upgrades > 0:
            fin_color = (255, 215, 0)  # Gold fins
            # Left fin
            pygame.draw.polygon(screen, fin_color, [
                (self.x - 5, visual_y + self.height//2),
                (self.x + 10, visual_y + self.height//2 - 5),
                (self.x + 10, visual_y + self.height//2 + 5)
            ])
            # Right fin
            pygame.draw.polygon(screen, fin_color, [
                (self.x + self.width + 5, visual_y + self.height//2),
                (self.x + self.width - 10, visual_y + self.height//2 - 5),
                (self.x + self.width - 10, visual_y + self.height//2 + 5)
            ])

        # Draw grappling systems
        self.grappling_system.draw(screen)  # Automated hooks
        self.player_hook.draw(screen)

        # Draw oxygen warning if low
        if oxygen_level <= 100:
            # Flashing warning indicator
            import time
            if int(time.time() * 4) % 2:  # Flash every 0.25 seconds
                warning_font = pygame.font.Font(None, 24)
                warning_text = warning_font.render("⚠️ LOW O2", True, (255, 255, 255))
                warning_rect = warning_text.get_rect(center=(self.x + self.width // 2, visual_y - 20))

                # Warning background
                bg_rect = warning_rect.inflate(10, 5)
                pygame.draw.rect(screen, (255, 0, 0), bg_rect)
                pygame.draw.rect(screen, (255, 255, 255), bg_rect, 2)

                screen.blit(warning_text, warning_rect)       # Player hook
            
    def upgrade_speed(self):
        """Upgrade submarine speed."""
        self.speed_upgrades += 1
        self.speed += 1

    def upgrade_depth(self):
        """Upgrade maximum diving depth."""
        self.depth_upgrades += 1
        self.max_depth += 50

    def upgrade_hull(self):
        """Upgrade submarine hull for better efficiency."""
        self.hull_upgrades += 1

    def upgrade_grappling_hooks(self, upgrade_type):
        """Upgrade automated grappling hook system."""
        if upgrade_type == 'count':
            return self.grappling_system.upgrade_hook_count()
        elif upgrade_type == 'range':
            return self.grappling_system.upgrade_hook_range()
        elif upgrade_type == 'speed':
            return self.grappling_system.upgrade_hook_speed()
        return False

    def upgrade_player_hook(self, upgrade_type):
        """Upgrade player grappling hook."""
        if upgrade_type == 'range':
            self.player_hook.upgrade_range()
            return True
        elif upgrade_type == 'speed':
            self.player_hook.upgrade_speed()
            return True
        return False

    def deploy_player_hook_to_position(self, x, y, resources=None):
        """Deploy player hook to specific position."""
        return self.player_hook.deploy_to_position(x, y, resources)

    def deploy_player_hook_to_resource(self, resource):
        """Deploy player hook to specific resource."""
        return self.player_hook.deploy_to_resource(resource)

    def get_grappling_stats(self):
        """Get grappling system statistics."""
        return {
            'automated': self.grappling_system.get_stats(),
            'player': self.player_hook.get_stats()
        }

    def get_grappling_upgrade_costs(self):
        """Get costs for grappling upgrades."""
        auto_costs = self.grappling_system.get_upgrade_costs()
        player_costs = {
            'player_range': {'kelp': 20, 'coral': 5},
            'player_speed': {'coral': 8, 'pearl': 2}
        }
        return {**auto_costs, **player_costs}

    def get_center_pos(self):
        """Get center position of submarine."""
        return (self.x + self.width // 2, self.y + self.height // 2)
