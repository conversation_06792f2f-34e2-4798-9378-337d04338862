"""Unified UI system that consolidates all UI functionality into a single, efficient system."""

import pygame
import math
from .constants import *

class UnifiedUI:
    """Single UI system that handles all UI responsibilities efficiently."""
    
    def __init__(self, screen_width, screen_height):
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.is_mobile = screen_width <= 800 or screen_height <= 600
        
        # Scale factors for responsive design
        self.scale_factor = min(screen_width / DEFAULT_SCREEN_WIDTH, 
                               screen_height / DEFAULT_SCREEN_HEIGHT)
        self.ui_scale = max(0.7, min(1.3, self.scale_factor))
        
        # Initialize fonts once
        pygame.font.init()
        self._init_fonts()
        
        # Color scheme
        self._init_colors()
        
        # Layout calculations
        self._calculate_layout()
        
        # UI state
        self.hover_element = None
        self.animation_time = 0
        self.tooltip_text = None
        self.tooltip_pos = (0, 0)
        self.tooltip_timer = 0
        self.tooltip_delay = 500  # ms before showing tooltip

        # Animation states
        self.pulse_animation = 0
        self.fade_animations = {}  # For fading elements

        # Notification system
        self.notifications = []  # List of active notifications
        
    def _init_fonts(self):
        """Initialize all fonts with adaptive scaling."""
        # Adaptive base sizes based on screen size
        if self.is_mobile:
            base_sizes = {
                'small': 14,
                'medium': 18,
                'large': 24,
                'title': 30,
                'heading': 20,
                'body': 16
            }
        elif self.screen_width < 1200:
            base_sizes = {
                'small': 16,
                'medium': 20,
                'large': 28,
                'title': 36,
                'heading': 24,
                'body': 18
            }
        else:
            base_sizes = {
                'small': 18,
                'medium': 22,
                'large': 32,
                'title': 42,
                'heading': 28,
                'body': 20
            }

        self.fonts = {}
        for name, size in base_sizes.items():
            scaled_size = max(10, int(size * self.ui_scale))
            self.fonts[name] = pygame.font.Font(None, scaled_size)
    
    def _init_colors(self):
        """Initialize consistent color scheme."""
        self.colors = {
            # Background colors
            'bg_primary': (15, 25, 35),      # Dark blue-gray
            'bg_secondary': (25, 35, 45),    # Lighter blue-gray
            'bg_panel': (20, 30, 40),        # Panel background
            'bg_overlay': (0, 0, 0, 180),    # Semi-transparent overlay
            
            # UI element colors
            'border': (60, 80, 100),         # Border color
            'border_active': (100, 150, 200), # Active border
            'text_primary': (255, 255, 255), # Primary text
            'text_secondary': (200, 220, 240), # Secondary text
            'text_accent': (100, 200, 255),  # Accent text
            
            # Status colors
            'success': (50, 200, 50),        # Green
            'warning': (255, 200, 50),       # Yellow
            'error': (255, 100, 100),        # Red
            'info': (100, 150, 255),         # Blue
            
            # Resource colors
            'oxygen': (100, 200, 255),       # Light blue
            'kelp': (50, 200, 100),          # Green
            'coral': (255, 150, 100),        # Orange
            'pearl': (255, 255, 200),        # Light yellow
            'treasure': (255, 200, 50),      # Gold

            # Additional colors for compatibility
            'light': (220, 230, 240),        # Light color for borders/highlights
            'accent': (100, 200, 255),       # Accent color
            'text_dim': (160, 180, 200),     # Dimmed text color
        }
    
    def _calculate_layout(self):
        """Calculate responsive layout regions with enhanced adaptivity."""
        # Adaptive margins and padding based on screen size
        self.margin = max(6, int(10 * self.ui_scale))
        self.padding = max(3, int(6 * self.ui_scale))

        # Enhanced responsive panel dimensions
        if self.is_mobile:
            # Mobile: ultra-compact panels for maximum playable area
            self.panel_width = min(int(160 * self.ui_scale), int(self.screen_width * 0.12))
            self.top_height = max(35, int(45 * self.ui_scale))
            self.bottom_height = max(35, int(50 * self.ui_scale))
        elif self.screen_width < 1200:
            # Small desktop: compact panels
            self.panel_width = min(int(200 * self.ui_scale), int(self.screen_width * 0.14))
            self.top_height = max(45, int(60 * self.ui_scale))
            self.bottom_height = max(45, int(70 * self.ui_scale))
        else:
            # Large desktop: comfortable panels
            self.panel_width = min(int(240 * self.ui_scale), int(self.screen_width * 0.15))
            self.top_height = max(55, int(75 * self.ui_scale))
            self.bottom_height = max(55, int(85 * self.ui_scale))
        
        # Define layout regions
        self.regions = {
            'top': {
                'x': 0, 'y': 0,
                'width': self.screen_width,
                'height': self.top_height
            },
            'left': {
                'x': 0, 'y': self.top_height,
                'width': self.panel_width,
                'height': self.screen_height - self.top_height - self.bottom_height
            },
            'right': {
                'x': self.screen_width - self.panel_width, 'y': self.top_height,
                'width': self.panel_width,
                'height': self.screen_height - self.top_height - self.bottom_height
            },
            'bottom': {
                'x': 0, 'y': self.screen_height - self.bottom_height,
                'width': self.screen_width,
                'height': self.bottom_height
            },
            'playable': {
                'x': self.panel_width, 'y': self.top_height,
                'width': self.screen_width - 2 * self.panel_width,
                'height': self.screen_height - self.top_height - self.bottom_height
            }
        }
    
    def resize(self, screen_width, screen_height):
        """Handle window resize efficiently."""
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.is_mobile = screen_width <= 800 or screen_height <= 600
        
        # Recalculate scale factor
        old_scale = self.ui_scale
        self.scale_factor = min(screen_width / DEFAULT_SCREEN_WIDTH, 
                               screen_height / DEFAULT_SCREEN_HEIGHT)
        self.ui_scale = max(0.7, min(1.3, self.scale_factor))
        
        # Only recreate fonts if scale changed significantly
        if abs(self.ui_scale - old_scale) > 0.1:
            self._init_fonts()
        
        # Recalculate layout
        self._calculate_layout()
    
    def get_playable_area(self):
        """Get the playable area region."""
        return self.regions['playable']
    
    def is_point_in_playable_area(self, x, y):
        """Check if a point is in the playable area."""
        playable = self.regions['playable']
        return (playable['x'] <= x < playable['x'] + playable['width'] and
                playable['y'] <= y < playable['y'] + playable['height'])
    
    def get_region_for_point(self, x, y):
        """Get which region a point belongs to."""
        for region_name, region in self.regions.items():
            if (region['x'] <= x < region['x'] + region['width'] and
                region['y'] <= y < region['y'] + region['height']):
                return region_name
        return None

    def update(self, dt):
        """Update UI animations and state."""
        self.animation_time += dt

        # Update pulse animation for visual feedback
        self.pulse_animation = (self.pulse_animation + dt * 0.003) % (2 * 3.14159)

        # Update tooltip timer
        if self.tooltip_text:
            self.tooltip_timer += dt
        else:
            self.tooltip_timer = 0

        # Update fade animations
        for element_id in list(self.fade_animations.keys()):
            self.fade_animations[element_id] += dt * 0.002
            if self.fade_animations[element_id] >= 1.0:
                del self.fade_animations[element_id]

        # Update notifications
        for notification in self.notifications[:]:  # Copy list to avoid modification during iteration
            notification['timer'] += dt
            if notification['timer'] > notification['duration']:
                self.notifications.remove(notification)

    def draw(self, screen, game_state):
        """Draw all UI elements."""
        # Reset tooltip for this frame
        self.tooltip_text = None

        # Draw region backgrounds
        self._draw_backgrounds(screen)

        # Draw content for each region
        self._draw_top_panel(screen, game_state)
        self._draw_left_panel(screen, game_state)
        self._draw_right_panel(screen, game_state)
        self._draw_bottom_panel(screen, game_state)

        # Draw tooltip if active and timer has elapsed
        if self.tooltip_text and self.tooltip_timer > self.tooltip_delay:
            self.draw_tooltip(screen, self.tooltip_text, self.tooltip_pos[0], self.tooltip_pos[1])

        # Draw notifications
        self._draw_notifications(screen)

    def _draw_backgrounds(self, screen):
        """Draw background panels for all UI regions with enhanced visuals."""
        for region_name, region in self.regions.items():
            if region_name == 'playable':
                continue  # Skip playable area

            # Create gradient background for better visual appeal
            gradient = self.create_gradient_surface(
                region['width'], region['height'],
                self.colors['bg_panel'], self.colors['bg_primary']
            )
            gradient.set_alpha(230)

            # Draw panel with gradient
            screen.blit(gradient, (region['x'], region['y']))

            # Draw enhanced border with inner highlight
            border_rect = (region['x'], region['y'], region['width'], region['height'])
            pygame.draw.rect(screen, self.colors['border'], border_rect, 2)

            # Inner highlight for depth effect
            inner_rect = (region['x'] + 1, region['y'] + 1,
                         region['width'] - 2, region['height'] - 2)
            pygame.draw.rect(screen, self.colors['border_active'], inner_rect, 1)

    def _draw_top_panel(self, screen, game_state):
        """Draw top status panel with enhanced visuals."""
        region = self.regions['top']
        submarine = game_state['submarine']

        # Submarine status
        x = region['x'] + self.margin
        y = region['y'] + self.margin

        # Depth indicator with symbol
        depth_text = f"~ Depth: {int(submarine.depth)}m"
        self._draw_text(screen, depth_text, x, y, 'large', self.colors['text_primary'], shadow=True)

        # Max depth with symbol
        x += 220
        max_depth_text = f"v Max: {submarine.max_depth}m"
        self._draw_text(screen, max_depth_text, x, y, 'medium', self.colors['text_secondary'])

        # Speed with symbol
        x += 170
        speed_text = f"> Speed: {submarine.speed:.1f}"
        self._draw_text(screen, speed_text, x, y, 'medium', self.colors['text_secondary'])

        # Oxygen status (right side) with enhanced styling
        oxygen = game_state['resources']['oxygen']
        oxygen_color = self.colors['oxygen']
        oxygen_icon = "O2"

        if oxygen <= 100:
            oxygen_color = self.colors['error']
            oxygen_icon = "!!"
        elif oxygen <= 200:
            oxygen_color = self.colors['warning']
            oxygen_icon = "!"

        oxygen_text = f"{oxygen_icon} Oxygen: {int(oxygen)}"
        oxygen_x = region['x'] + region['width'] - 180
        self._draw_text(screen, oxygen_text, oxygen_x, y, 'large', oxygen_color, shadow=True)

        # Add animated oxygen bar
        bar_x = oxygen_x
        bar_y = y + 25
        bar_width = 120
        bar_height = 8

        # Background bar
        pygame.draw.rect(screen, self.colors['bg_secondary'],
                        (bar_x, bar_y, bar_width, bar_height))

        # Oxygen level bar with animation
        oxygen_ratio = min(1.0, oxygen / 1000.0)
        fill_width = int(bar_width * oxygen_ratio)

        # Add pulsing effect when oxygen is low
        if oxygen <= 200:
            pulse_intensity = abs(math.sin(self.pulse_animation * 2))
            pulse_color = tuple(min(255, int(c + pulse_intensity * 50)) for c in oxygen_color)
            pygame.draw.rect(screen, pulse_color,
                            (bar_x, bar_y, fill_width, bar_height))
        else:
            pygame.draw.rect(screen, oxygen_color,
                            (bar_x, bar_y, fill_width, bar_height))

        # Bar border with glow effect for low oxygen
        border_color = self.colors['border']
        if oxygen <= 100:
            glow_intensity = abs(math.sin(self.pulse_animation * 3))
            border_color = tuple(min(255, int(c + glow_intensity * 100)) for c in self.colors['error'])

        pygame.draw.rect(screen, border_color,
                        (bar_x, bar_y, bar_width, bar_height), 1)

    def _draw_left_panel(self, screen, game_state):
        """Draw left control panel with enhanced visuals."""
        region = self.regions['left']
        automation = game_state.get('automation', {})

        x = region['x'] + self.margin
        y = region['y'] + self.margin

        # Title with symbol
        self._draw_text(screen, "# Controls", x, y, 'medium', self.colors['text_accent'], shadow=True)
        y += 35

        # Automation toggles with enhanced styling
        controls = [
            ("Auto Craft", automation.get('auto_craft', False))
        ]

        for i, (label, enabled) in enumerate(controls):
            # Draw toggle background
            toggle_x = x
            toggle_y = y - 2
            toggle_width = region['width'] - 2 * self.margin
            toggle_height = 20

            # Background color based on state
            bg_color = self.colors['success'] if enabled else self.colors['bg_secondary']
            toggle_surface = pygame.Surface((toggle_width, toggle_height))
            toggle_surface.set_alpha(100)
            toggle_surface.fill(bg_color)
            screen.blit(toggle_surface, (toggle_x, toggle_y))

            # Border
            border_color = self.colors['success'] if enabled else self.colors['border']
            pygame.draw.rect(screen, border_color,
                           (toggle_x, toggle_y, toggle_width, toggle_height), 1)

            # Text with status indicator
            color = self.colors['text_primary'] if enabled else self.colors['text_secondary']
            status_symbol = "[X]" if enabled else "[ ]"
            text = f"{status_symbol} {label}"
            self._draw_text(screen, text, x + 5, y, 'small', color)
            y += 28

        # Instructions section
        y += 15
        self._draw_text(screen, "? Help", x, y, 'medium', self.colors['text_accent'], shadow=True)
        y += 30

        instructions = [
            "SPACE - Pause Game",
            "ESC - Exit Game",
            "F1 - Performance Info",
            "Wheel - Zoom Camera",
            "Mid Click - Pan Camera",
            "Left Click - Grapple Hook",
            "Right Click - Move Sub"
        ]

        for instruction in instructions:
            self._draw_text(screen, instruction, x, y, 'small', self.colors['text_secondary'])
            y += 18

    def _draw_right_panel(self, screen, game_state):
        """Draw right resource and status panel with enhanced visuals."""
        region = self.regions['right']
        resources = game_state['resources']
        divers = game_state['divers']
        submarine = game_state.get('submarine')
        dock = game_state.get('dock')

        x = region['x'] + self.margin
        y = region['y'] + self.margin

        # Submarine Storage section
        self._draw_text(screen, "🚢 Submarine Storage", x, y, 'medium', self.colors['text_accent'], shadow=True)
        y += 30

        # Get submarine storage info
        if submarine:
            storage_used = submarine.get_storage_used()
            storage_capacity = submarine.get_storage_capacity()
            storage_percentage = submarine.get_storage_percentage()

            # Storage capacity indicator
            capacity_text = f"Capacity: {storage_used}/{storage_capacity}"
            capacity_color = self.colors['error'] if storage_percentage >= 0.9 else self.colors['warning'] if storage_percentage >= 0.7 else self.colors['text_primary']
            self._draw_text(screen, capacity_text, x, y, 'small', capacity_color)
            y += 18

            # Storage capacity bar
            bar_x = x + 5
            bar_y = y
            bar_width = region['width'] - 2 * self.margin - 10
            bar_height = 6

            # Background
            pygame.draw.rect(screen, self.colors['bg_secondary'], (bar_x, bar_y, bar_width, bar_height))

            # Fill based on storage percentage
            fill_width = int(bar_width * storage_percentage)
            if fill_width > 0:
                fill_color = self.colors['error'] if storage_percentage >= 0.9 else self.colors['warning'] if storage_percentage >= 0.7 else self.colors['success']
                pygame.draw.rect(screen, fill_color, (bar_x, bar_y, fill_width, bar_height))

            y += 25

            # Individual submarine resources
            resource_info = {
                'kelp': {'icon': 'K', 'color': self.colors['kelp']},
                'coral': {'icon': 'C', 'color': self.colors['coral']},
                'pearl': {'icon': 'P', 'color': self.colors['pearl']},
                'treasure': {'icon': 'T', 'color': self.colors['treasure']}
            }

            for resource_type in ['kelp', 'coral', 'pearl', 'treasure']:
                amount = submarine.storage.get(resource_type, 0)
                if amount > 0:  # Only show resources that are present
                    info = resource_info[resource_type]

                    # Resource text with icon
                    text = f"{info['icon']} {resource_type.title()}: {amount}"
                    self._draw_text(screen, text, x, y, 'small', info['color'])
                    y += 16

        y += 15

        # Warehouse & Dock Storage section
        self._draw_text(screen, "🏗️ Storage Facilities", x, y, 'medium', self.colors['text_accent'], shadow=True)
        y += 30

        if dock and hasattr(dock, 'warehouse'):
            # Warehouse storage
            warehouse = dock.warehouse
            warehouse_stored = warehouse.get_storage_used()
            warehouse_capacity = warehouse.get_storage_capacity()
            warehouse_percentage = warehouse_stored / warehouse_capacity if warehouse_capacity > 0 else 0

            warehouse_text = f"📦 Warehouse: {warehouse_stored}/{warehouse_capacity}"
            warehouse_color = self.colors['error'] if warehouse_percentage >= 0.9 else self.colors['warning'] if warehouse_percentage >= 0.7 else self.colors['info']
            self._draw_text(screen, warehouse_text, x, y, 'small', warehouse_color)
            y += 18

            # Warehouse storage bar
            bar_x = x + 5
            bar_y = y
            bar_width = region['width'] - 2 * self.margin - 10
            bar_height = 6

            pygame.draw.rect(screen, self.colors['bg_secondary'], (bar_x, bar_y, bar_width, bar_height))
            warehouse_fill_width = int(bar_width * warehouse_percentage)
            if warehouse_fill_width > 0:
                pygame.draw.rect(screen, warehouse_color, (bar_x, bar_y, warehouse_fill_width, bar_height))

            y += 20

            # Individual warehouse resources
            for resource_type in ['kelp', 'coral', 'pearl', 'treasure']:
                amount = warehouse.stored_resources.get(resource_type, 0)
                if amount > 0:  # Only show resources that are present
                    info = resource_info[resource_type]
                    text = f"  {info['icon']} {resource_type.title()}: {amount}"
                    self._draw_text(screen, text, x, y, 'small', info['color'])
                    y += 16

            y += 10

            # Dock storage (overflow storage)
            dock_stored = sum(dock.stored_resources.values())
            dock_capacity = dock.get_storage_capacity()
            dock_percentage = dock_stored / dock_capacity if dock_capacity > 0 else 0

            if dock_stored > 0:  # Only show dock storage if it has resources
                dock_text = f"⚓ Dock Overflow: {dock_stored}/{dock_capacity}"
                dock_color = self.colors['error'] if dock_percentage >= 0.9 else self.colors['warning'] if dock_percentage >= 0.7 else self.colors['text_secondary']
                self._draw_text(screen, dock_text, x, y, 'small', dock_color)
                y += 18

                # Dock storage bar
                pygame.draw.rect(screen, self.colors['bg_secondary'], (bar_x, bar_y, bar_width, bar_height))
                dock_fill_width = int(bar_width * dock_percentage)
                if dock_fill_width > 0:
                    pygame.draw.rect(screen, dock_color, (bar_x, bar_y, dock_fill_width, bar_height))

                y += 20

                # Individual dock overflow resources
                for resource_type in ['kelp', 'coral', 'pearl', 'treasure']:
                    amount = dock.stored_resources.get(resource_type, 0)
                    if amount > 0:
                        info = resource_info[resource_type]
                        text = f"  {info['icon']} {resource_type.title()}: {amount}"
                        self._draw_text(screen, text, x, y, 'small', info['color'])
                        y += 16

        y += 15

        # Total Resources section (combined)
        self._draw_text(screen, "💰 Total Resources", x, y, 'medium', self.colors['text_accent'], shadow=True)
        y += 30

        for resource_type in ['kelp', 'coral', 'pearl', 'treasure']:
            # Calculate total including submarine, dock, and warehouse
            submarine_amount = submarine.storage.get(resource_type, 0) if submarine else 0
            dock_amount = dock.stored_resources.get(resource_type, 0) if dock else 0
            warehouse_amount = dock.warehouse.stored_resources.get(resource_type, 0) if dock and hasattr(dock, 'warehouse') else 0

            total_amount = submarine_amount + dock_amount + warehouse_amount

            if total_amount > 0:  # Only show resources that exist
                info = resource_info[resource_type]

                # Resource text with icon showing total
                text = f"{info['icon']} {resource_type.title()}: {total_amount}"
                self._draw_text(screen, text, x, y, 'small', info['color'])

                # Adaptive progress bar for visual appeal
                bar_x = x + 5
                bar_y = y + 16
                bar_width = region['width'] - 2 * self.margin - 10
                bar_height = 4 if self.is_mobile else 3  # Slightly thicker on mobile

                # Background
                pygame.draw.rect(screen, self.colors['bg_secondary'],
                               (bar_x, bar_y, bar_width, bar_height))

                # Fill based on amount (max 100 for visual scaling)
                fill_ratio = min(1.0, amount / 100.0)
                fill_width = int(bar_width * fill_ratio)
                if fill_width > 0:
                    pygame.draw.rect(screen, info['color'],
                                   (bar_x, bar_y, fill_width, bar_height))

                y += 28

        # Crew section with symbol
        y += 15
        self._draw_text(screen, "@ Crew", x, y, 'medium', self.colors['text_accent'], shadow=True)
        y += 35

        # Diver status with enhanced styling
        diver_info = {
            'idle': {'icon': 'o', 'color': self.colors['success']},
            'swimming': {'icon': '~', 'color': self.colors['info']},
            'collecting': {'icon': '*', 'color': self.colors['warning']},
            'returning': {'icon': '^', 'color': self.colors['text_accent']}
        }

        for i, diver in enumerate(divers):
            info = diver_info.get(diver.state, {'icon': '⚪', 'color': self.colors['text_secondary']})

            # Diver status with icon
            text = f"{info['icon']} Diver {i+1}: {diver.state.title()}"
            self._draw_text(screen, text, x, y, 'small', info['color'])

            # Oxygen bar for active divers
            if hasattr(diver, 'oxygen') and diver.state != 'idle':
                bar_x = x + 5
                bar_y = y + 16
                bar_width = region['width'] - 2 * self.margin - 10
                bar_height = 3

                # Background
                pygame.draw.rect(screen, self.colors['bg_secondary'],
                               (bar_x, bar_y, bar_width, bar_height))

                # Oxygen level
                oxygen_ratio = diver.oxygen / 100.0
                fill_width = int(bar_width * oxygen_ratio)
                oxygen_color = self.colors['success']
                if diver.oxygen < 30:
                    oxygen_color = self.colors['error']
                elif diver.oxygen < 60:
                    oxygen_color = self.colors['warning']

                if fill_width > 0:
                    pygame.draw.rect(screen, oxygen_color,
                                   (bar_x, bar_y, fill_width, bar_height))

            y += 25

        # Storage section with symbol
        y += 15
        self._draw_text(screen, "📦 Storage", x, y, 'medium', self.colors['text_accent'], shadow=True)
        y += 35

        # Submarine storage
        submarine = game_state['submarine']
        submarine_storage = game_state.get('submarine_storage', {})

        # Submarine storage header with capacity
        storage_used = submarine.get_storage_used()
        storage_capacity = submarine.get_storage_capacity()
        storage_percentage = submarine.get_storage_percentage()

        storage_color = self.colors['success']
        if storage_percentage >= 0.9:
            storage_color = self.colors['error']
        elif storage_percentage >= 0.7:
            storage_color = self.colors['warning']

        sub_text = f"🚢 Sub: {storage_used}/{storage_capacity}"
        self._draw_text(screen, sub_text, x, y, 'small', storage_color)

        # Submarine storage bar
        bar_x = x + 5
        bar_y = y + 16
        bar_width = region['width'] - 2 * self.margin - 10
        bar_height = 4

        pygame.draw.rect(screen, self.colors['bg_secondary'], (bar_x, bar_y, bar_width, bar_height))
        fill_width = int(bar_width * storage_percentage)
        if fill_width > 0:
            pygame.draw.rect(screen, storage_color, (bar_x, bar_y, fill_width, bar_height))

        y += 28

        # Dock storage
        dock_resources = game_state.get('dock_resources', {})
        dock = game_state.get('dock')

        if dock:
            dock_used = sum(dock_resources.values())
            dock_capacity = dock.max_storage + (dock.upgrades.get('storage_capacity', 0) * 50)
            dock_percentage = dock_used / dock_capacity if dock_capacity > 0 else 0

            dock_color = self.colors['info']
            if dock_percentage >= 0.9:
                dock_color = self.colors['warning']

            dock_text = f"⚓ Dock: {dock_used}/{dock_capacity}"
            self._draw_text(screen, dock_text, x, y, 'small', dock_color)

            # Dock storage bar
            pygame.draw.rect(screen, self.colors['bg_secondary'], (bar_x, bar_y + 20, bar_width, bar_height))
            dock_fill_width = int(bar_width * dock_percentage)
            if dock_fill_width > 0:
                pygame.draw.rect(screen, dock_color, (bar_x, bar_y + 20, dock_fill_width, bar_height))

            y += 28

            # Dock workers status
            worker_count = dock.get_dock_worker_count()
            worker_efficiency = dock.get_dock_worker_efficiency()

            worker_text = f"👷 Workers: {worker_count} ({worker_efficiency:.0%})"
            worker_color = self.colors['success'] if worker_count > 0 else self.colors['text_secondary']
            self._draw_text(screen, worker_text, x, y, 'small', worker_color)

            y += 25

    def _draw_bottom_panel(self, screen, game_state):
        """Draw bottom action panel with enhanced button styling."""
        region = self.regions['bottom']

        x = region['x'] + self.margin
        y = region['y'] + self.margin

        # Enhanced action buttons with symbols
        buttons = [
            ("$ Upgrades", self.colors['info']),
            ("+ Focus Sub", self.colors['text_accent'])
        ]

        # Adaptive button sizing for better touch support
        if self.is_mobile:
            button_width = min(100, int(self.screen_width * 0.25))
            button_height = max(40, int(45 * self.ui_scale))
        else:
            button_width = min(140, int(self.screen_width * 0.12))
            button_height = max(35, int(40 * self.ui_scale))

        # Adaptive button spacing
        button_spacing = 10 if self.is_mobile else 15

        for i, (label, color) in enumerate(buttons):
            button_x = x + i * (button_width + button_spacing)

            # Check if button is being hovered (simple proximity check)
            mouse_pos = pygame.mouse.get_pos()
            button_rect = pygame.Rect(button_x, y, button_width, button_height)
            is_hovered = button_rect.collidepoint(mouse_pos)

            # Enhanced color for hover effect
            if is_hovered:
                hover_color = tuple(min(255, c + 30) for c in color)
                button_gradient = self.create_gradient_surface(
                    button_width, button_height, hover_color,
                    tuple(max(0, c - 20) for c in hover_color)
                )
            else:
                button_gradient = self.create_gradient_surface(
                    button_width, button_height, color,
                    tuple(max(0, c - 30) for c in color)
                )

            # Draw button with gradient
            screen.blit(button_gradient, (button_x, y))

            # Button border with highlight effect
            button_rect = pygame.Rect(button_x, y, button_width, button_height)
            pygame.draw.rect(screen, self.colors['border_active'], button_rect, 2)

            # Inner highlight for 3D effect
            inner_rect = pygame.Rect(button_x + 1, y + 1, button_width - 2, button_height - 2)
            pygame.draw.rect(screen, tuple(min(255, c + 40) for c in color), inner_rect, 1)

            # Center button text
            font = self.fonts['small']
            text_surface = font.render(label, True, self.colors['text_primary'])
            text_rect = text_surface.get_rect(center=(button_x + button_width // 2, y + button_height // 2))

            # Text shadow for better readability
            shadow_surface = font.render(label, True, (0, 0, 0))
            shadow_rect = text_rect.copy()
            shadow_rect.x += 1
            shadow_rect.y += 1
            screen.blit(shadow_surface, shadow_rect)
            screen.blit(text_surface, text_rect)

            # Set tooltip for hovered button
            if is_hovered:
                tooltips = [
                    "Open upgrade shop to improve your submarine",
                    "Center camera on your submarine"
                ]
                self.tooltip_text = tooltips[i]
                self.tooltip_pos = (button_x + button_width // 2, y - 10)

    def _draw_text(self, screen, text, x, y, font_size='medium', color=None, shadow=False):
        """Draw text with specified font and color, with optional shadow effect."""
        if color is None:
            color = self.colors['text_primary']

        font = self.fonts.get(font_size, self.fonts['medium'])

        # Draw shadow for better readability
        if shadow:
            shadow_surface = font.render(str(text), True, (0, 0, 0))
            screen.blit(shadow_surface, (x + 1, y + 1))

        # Draw main text
        text_surface = font.render(str(text), True, color)
        screen.blit(text_surface, (x, y))
        return text_surface.get_rect(topleft=(x, y))

    def handle_click(self, x, y, game_state):
        """Handle mouse clicks and route to appropriate handlers."""
        region = self.get_region_for_point(x, y)

        if region == 'playable':
            return {'action': 'game_click', 'x': x, 'y': y}
        elif region == 'left':
            return self._handle_left_panel_click(x, y, game_state)
        elif region == 'right':
            return self._handle_right_panel_click(x, y, game_state)
        elif region == 'bottom':
            return self._handle_bottom_panel_click(x, y, game_state)
        elif region == 'top':
            return self._handle_top_panel_click(x, y, game_state)

        return None

    def _handle_left_panel_click(self, x, y, game_state):
        """Handle clicks in the left control panel."""
        region = self.regions['left']
        relative_y = y - region['y'] - self.margin

        # Check automation toggles (starting at y=65, 28px spacing)
        if 65 <= relative_y <= 85:  # Auto Craft toggle area
            return {'action': 'toggle_auto_craft'}

        return None

    def _handle_right_panel_click(self, x, y, game_state):
        """Handle clicks in the right resource panel."""
        # Right panel is mostly informational, no interactive elements for now
        return None

    def _handle_bottom_panel_click(self, x, y, game_state):
        """Handle clicks in the bottom action panel with adaptive touch targets."""
        region = self.regions['bottom']
        relative_x = x - region['x'] - self.margin
        relative_y = y - region['y'] - self.margin

        # Adaptive button dimensions (same as in drawing)
        if self.is_mobile:
            button_width = min(100, int(self.screen_width * 0.25))
            button_height = max(40, int(45 * self.ui_scale))
        else:
            button_width = min(140, int(self.screen_width * 0.12))
            button_height = max(35, int(40 * self.ui_scale))

        button_spacing = 10 if self.is_mobile else 15

        # Check button clicks with adaptive sizing
        if 0 <= relative_y <= button_height:  # Button row
            # Button 1: Upgrades
            if 0 <= relative_x <= button_width:
                return {'action': 'toggle_upgrade_shop'}
            # Button 2: Focus Sub
            elif (button_width + button_spacing) <= relative_x <= (2 * button_width + button_spacing):
                return {'action': 'focus_submarine'}

        return None

    def _handle_top_panel_click(self, x, y, game_state):
        """Handle clicks in the top status panel."""
        # Top panel is mostly informational, no interactive elements for now
        return None

    def draw_tooltip(self, screen, text, x, y):
        """Draw a tooltip at the specified position."""
        if not text:
            return

        # Calculate tooltip size
        font = self.fonts['small']
        text_surface = font.render(text, True, self.colors['text_primary'])
        tooltip_width = text_surface.get_width() + 2 * self.padding
        tooltip_height = text_surface.get_height() + 2 * self.padding

        # Adjust position to keep tooltip on screen
        if x + tooltip_width > self.screen_width:
            x = self.screen_width - tooltip_width
        if y + tooltip_height > self.screen_height:
            y = y - tooltip_height - 10

        # Draw tooltip background
        tooltip_surface = pygame.Surface((tooltip_width, tooltip_height))
        tooltip_surface.set_alpha(240)
        tooltip_surface.fill(self.colors['bg_secondary'])
        screen.blit(tooltip_surface, (x, y))

        # Draw tooltip border
        pygame.draw.rect(screen, self.colors['border'],
                        (x, y, tooltip_width, tooltip_height), 1)

        # Draw tooltip text
        screen.blit(text_surface, (x + self.padding, y + self.padding))

    def draw_notification(self, screen, text, notification_type='info', duration=3.0):
        """Draw a notification message."""
        if not text:
            return

        # Notification colors
        type_colors = {
            'info': self.colors['info'],
            'success': self.colors['success'],
            'warning': self.colors['warning'],
            'error': self.colors['error']
        }

        bg_color = type_colors.get(notification_type, self.colors['info'])

        # Calculate notification size
        font = self.fonts['medium']
        text_surface = font.render(text, True, self.colors['text_primary'])
        notif_width = text_surface.get_width() + 4 * self.padding
        notif_height = text_surface.get_height() + 2 * self.padding

        # Position at top center
        x = (self.screen_width - notif_width) // 2
        y = self.top_height + self.margin

        # Draw notification background
        notif_surface = pygame.Surface((notif_width, notif_height))
        notif_surface.set_alpha(220)
        notif_surface.fill(bg_color)
        screen.blit(notif_surface, (x, y))

        # Draw notification border
        pygame.draw.rect(screen, self.colors['border'],
                        (x, y, notif_width, notif_height), 2)

        # Draw notification text
        text_x = x + (notif_width - text_surface.get_width()) // 2
        text_y = y + self.padding
        screen.blit(text_surface, (text_x, text_y))

    def create_gradient_surface(self, width, height, color1, color2):
        """Create a simple gradient surface from color1 to color2."""
        surface = pygame.Surface((width, height))

        # Simple vertical gradient
        for y in range(height):
            ratio = y / height
            r = int(color1[0] * (1 - ratio) + color2[0] * ratio)
            g = int(color1[1] * (1 - ratio) + color2[1] * ratio)
            b = int(color1[2] * (1 - ratio) + color2[2] * ratio)

            pygame.draw.line(surface, (r, g, b), (0, y), (width, y))

        return surface

    def show_notification(self, text, notification_type='info', duration=2000):
        """Show a notification message."""
        notification = {
            'text': text,
            'type': notification_type,
            'timer': 0,
            'duration': duration
        }
        self.notifications.append(notification)

        # Limit number of notifications
        if len(self.notifications) > 3:
            self.notifications.pop(0)

    def _draw_notifications(self, screen):
        """Draw all active notifications."""
        y_offset = self.regions['top']['height'] + 10

        for i, notification in enumerate(self.notifications):
            # Calculate fade effect
            fade_ratio = 1.0
            if notification['timer'] > notification['duration'] - 500:  # Fade out in last 500ms
                fade_ratio = (notification['duration'] - notification['timer']) / 500.0
            elif notification['timer'] < 300:  # Fade in first 300ms
                fade_ratio = notification['timer'] / 300.0

            fade_ratio = max(0.0, min(1.0, fade_ratio))

            # Notification colors
            type_colors = {
                'info': self.colors['info'],
                'success': self.colors['success'],
                'warning': self.colors['warning'],
                'error': self.colors['error']
            }

            bg_color = type_colors.get(notification['type'], self.colors['info'])

            # Calculate notification size
            font = self.fonts['medium']
            text_surface = font.render(notification['text'], True, self.colors['text_primary'])
            notif_width = text_surface.get_width() + 4 * self.padding
            notif_height = text_surface.get_height() + 2 * self.padding

            # Position at top center
            x = (self.screen_width - notif_width) // 2
            y = y_offset + i * (notif_height + 5)

            # Draw notification background with fade
            notif_surface = pygame.Surface((notif_width, notif_height))
            notif_surface.set_alpha(int(200 * fade_ratio))
            notif_surface.fill(bg_color)
            screen.blit(notif_surface, (x, y))

            # Draw notification border
            border_color = tuple(int(c * fade_ratio) for c in self.colors['border'])
            pygame.draw.rect(screen, border_color,
                            (x, y, notif_width, notif_height), 2)

            # Draw notification text with fade
            faded_text = text_surface.copy()
            faded_text.set_alpha(int(255 * fade_ratio))
            text_x = x + (notif_width - text_surface.get_width()) // 2
            text_y = y + self.padding
            screen.blit(faded_text, (text_x, text_y))

    def get_legacy_colors(self):
        """Get colors in the format expected by legacy systems."""
        return {
            'primary': self.colors['info'],
            'success': self.colors['success'],
            'warning': self.colors['warning'],
            'error': self.colors['error'],
            'text': self.colors['text_primary'],
            'panel': self.colors['bg_panel'],
            'darker': self.colors['bg_primary']
        }
