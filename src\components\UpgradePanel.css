/* Upgrade Panel Styles */
.upgrade-panel {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.upgrade-section h3,
.purchased-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
  color: #333;
  border-bottom: 2px solid #667eea;
  padding-bottom: 0.5rem;
}

.no-upgrades {
  text-align: center;
  padding: 3rem;
  color: #6c757d;
  font-style: italic;
}

.upgrade-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.upgrade-card {
  background: #ffffff;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.upgrade-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.upgrade-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.upgrade-card.disabled:hover {
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #e9ecef;
}

.upgrade-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.upgrade-icon {
  font-size: 2rem;
}

.upgrade-name {
  margin: 0;
  font-size: 1.25rem;
  color: #333;
}

.upgrade-description {
  color: #6c757d;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
  line-height: 1.4;
}

.upgrade-effects {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #17a2b8;
}

.upgrade-effects strong {
  display: block;
  margin-bottom: 0.5rem;
  color: #333;
  font-size: 0.9rem;
}

.effect-item {
  padding: 0.25rem 0;
  font-size: 0.9rem;
  color: #17a2b8;
  font-weight: 600;
}

.upgrade-cost {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.upgrade-cost strong {
  display: block;
  margin-bottom: 0.5rem;
  color: #333;
  font-size: 0.9rem;
}

.cost-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0;
  font-size: 0.9rem;
  color: #333;
}

.cost-item.insufficient {
  color: #dc3545;
  font-weight: 600;
}

.purchase-button {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.purchase-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.purchase-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Purchased Upgrades */
.purchased-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid #e9ecef;
}

.purchased-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.purchased-upgrade {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: #ffffff;
  border: 2px solid #28a745;
  border-radius: 10px;
  padding: 1rem;
}

.purchased-upgrade .upgrade-icon {
  font-size: 1.5rem;
}

.purchased-upgrade .upgrade-info {
  flex: 1;
}

.purchased-upgrade .upgrade-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.purchased-upgrade .upgrade-effects {
  background: none;
  padding: 0;
  border: none;
  margin: 0;
}

.purchased-upgrade .effect-item {
  color: #28a745;
  font-size: 0.8rem;
  padding: 0.1rem 0;
}

.purchased-badge {
  background: #28a745;
  color: white;
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .upgrade-grid {
    grid-template-columns: 1fr;
  }
  
  .upgrade-card {
    padding: 1rem;
  }
  
  .upgrade-header {
    gap: 0.75rem;
  }
  
  .upgrade-icon {
    font-size: 1.5rem;
  }
  
  .upgrade-name {
    font-size: 1.1rem;
  }
  
  .purchased-upgrade {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
}
