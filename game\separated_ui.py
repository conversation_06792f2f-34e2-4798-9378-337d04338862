"""Separated UI system that completely separates UI from playable area."""

import pygame
import math
from .constants import *

class SeparatedUI:
    """UI system that creates distinct regions for UI and gameplay."""
    
    def __init__(self, screen_width, screen_height):
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.is_mobile = screen_width <= 800 or screen_height <= 600
        
        # Scale factors for responsive design
        self.scale_factor = min(screen_width / DEFAULT_SCREEN_WIDTH, 
                               screen_height / DEFAULT_SCREEN_HEIGHT)
        self.ui_scale = max(0.7, min(1.3, self.scale_factor))
        
        # Initialize fonts
        pygame.font.init()
        base_font_size = int(18 * self.ui_scale)
        self.font_small = pygame.font.Font(None, max(14, base_font_size - 4))
        self.font_medium = pygame.font.Font(None, max(18, base_font_size))
        self.font_large = pygame.font.Font(None, max(24, base_font_size + 6))
        self.font_title = pygame.font.Font(None, max(32, base_font_size + 14))
        
        # Modern color palette
        self.colors = {
            'primary': (64, 128, 255),      # Blue
            'secondary': (128, 64, 255),    # Purple
            'success': (64, 255, 128),      # Green
            'warning': (255, 192, 64),      # Orange
            'danger': (255, 64, 64),        # Red
            'dark': (32, 32, 48),           # Dark blue-gray
            'darker': (16, 16, 24),         # Darker
            'light': (240, 240, 255),       # Light
            'accent': (255, 215, 0),        # Gold
            'background': (20, 25, 40),     # Deep blue background
            'panel': (40, 45, 65),          # Panel background
            'border': (80, 90, 120),        # Border color
            'text': (220, 225, 240),        # Text color
            'text_dim': (160, 170, 190),    # Dimmed text
            'ui_bg': (25, 30, 45),          # UI background
            'playable_bg': (15, 20, 35)     # Playable area background
        }
        
        # Calculate layout regions
        self._calculate_layout()
        
    def _calculate_layout(self):
        """Calculate the separated layout regions optimized for large playable area."""
        # Aggressive sizing to maximize playable area (target 75%+ of screen)
        if self.is_mobile:
            # Mobile: ultra-compact panels
            self.ui_panel_width = min(int(UI_PANEL_WIDTH * 0.4), int(self.screen_width * 0.12))
            self.ui_top_height = int(UI_TOP_HEIGHT * 0.6)
            self.ui_bottom_height = int(UI_BOTTOM_HEIGHT * 0.6)
        else:
            # Desktop: compact panels to maximize playable area
            self.ui_panel_width = min(UI_PANEL_WIDTH, int(self.screen_width * 0.10))  # Max 10% of width
            self.ui_top_height = UI_TOP_HEIGHT
            self.ui_bottom_height = UI_BOTTOM_HEIGHT
            
        self.ui_margin = int(UI_MARGIN * self.ui_scale)
        
        # Define screen regions
        self.regions = {
            # Top UI bar (depth, status indicators)
            'top_ui': {
                'x': 0,
                'y': 0,
                'width': self.screen_width,
                'height': self.ui_top_height
            },
            
            # Left UI panel (controls, automation)
            'left_ui': {
                'x': 0,
                'y': self.ui_top_height,
                'width': self.ui_panel_width,
                'height': self.screen_height - self.ui_top_height - self.ui_bottom_height
            },
            
            # Right UI panel (resources, crew, upgrades)
            'right_ui': {
                'x': self.screen_width - self.ui_panel_width,
                'y': self.ui_top_height,
                'width': self.ui_panel_width,
                'height': self.screen_height - self.ui_top_height - self.ui_bottom_height
            },
            
            # Bottom UI bar (quick actions, notifications)
            'bottom_ui': {
                'x': 0,
                'y': self.screen_height - self.ui_bottom_height,
                'width': self.screen_width,
                'height': self.ui_bottom_height
            },
            
            # Central playable area (game world)
            'playable': {
                'x': self.ui_panel_width,
                'y': self.ui_top_height,
                'width': self.screen_width - (2 * self.ui_panel_width),
                'height': self.screen_height - self.ui_top_height - self.ui_bottom_height
            }
        }
        
        # Ensure playable area is at least 80% of screen
        target_playable_ratio = 0.80
        target_playable_area = self.screen_width * self.screen_height * target_playable_ratio
        current_playable_area = self.regions['playable']['width'] * self.regions['playable']['height']

        if current_playable_area < target_playable_area:
            # Aggressively reduce UI sizes to reach target
            reduction_needed = target_playable_area - current_playable_area

            # Reduce panel widths first
            min_panel_width = 80 if self.is_mobile else 100  # Smaller minimum panels
            max_panel_reduction = self.ui_panel_width - min_panel_width
            if max_panel_reduction > 0:
                panel_reduction = min(max_panel_reduction, int(reduction_needed / (2 * self.screen_height)))
                self.ui_panel_width -= panel_reduction
                self._recalculate_regions()
                current_playable_area = self.regions['playable']['width'] * self.regions['playable']['height']

            # If still not enough, reduce top/bottom heights
            if current_playable_area < target_playable_area:
                remaining_reduction = target_playable_area - current_playable_area
                height_reduction = min(20, int(remaining_reduction / (2 * self.screen_width)))  # Max 20px reduction

                self.ui_top_height = max(25, self.ui_top_height - height_reduction)
                self.ui_bottom_height = max(30, self.ui_bottom_height - height_reduction)
                self._recalculate_regions()

        # Ensure absolute minimums for usability
        min_playable_width = 300
        min_playable_height = 200

        if self.regions['playable']['width'] < min_playable_width:
            excess = min_playable_width - self.regions['playable']['width']
            panel_reduction = excess // 2
            self.ui_panel_width = max(100, self.ui_panel_width - panel_reduction)
            self._recalculate_regions()

        if self.regions['playable']['height'] < min_playable_height:
            excess = min_playable_height - self.regions['playable']['height']
            height_reduction = excess // 2
            self.ui_top_height = max(25, self.ui_top_height - height_reduction)
            self.ui_bottom_height = max(30, self.ui_bottom_height - height_reduction)
            self._recalculate_regions()
    
    def _recalculate_regions(self):
        """Recalculate regions after adjustments."""
        self.regions['left_ui']['width'] = self.ui_panel_width
        self.regions['left_ui']['height'] = self.screen_height - self.ui_top_height - self.ui_bottom_height
        
        self.regions['right_ui']['x'] = self.screen_width - self.ui_panel_width
        self.regions['right_ui']['width'] = self.ui_panel_width
        self.regions['right_ui']['height'] = self.screen_height - self.ui_top_height - self.ui_bottom_height
        
        self.regions['top_ui']['height'] = self.ui_top_height
        
        self.regions['bottom_ui']['y'] = self.screen_height - self.ui_bottom_height
        self.regions['bottom_ui']['height'] = self.ui_bottom_height
        
        self.regions['playable']['x'] = self.ui_panel_width
        self.regions['playable']['y'] = self.ui_top_height
        self.regions['playable']['width'] = self.screen_width - (2 * self.ui_panel_width)
        self.regions['playable']['height'] = self.screen_height - self.ui_top_height - self.ui_bottom_height
    
    def get_region(self, region_name):
        """Get a specific UI region."""
        return self.regions.get(region_name, None)
    
    def get_playable_area(self):
        """Get the playable area region."""
        return self.regions['playable']
    
    def is_point_in_region(self, x, y, region_name):
        """Check if a point is within a specific region."""
        region = self.regions.get(region_name)
        if not region:
            return False
            
        return (region['x'] <= x <= region['x'] + region['width'] and
                region['y'] <= y <= region['y'] + region['height'])
    
    def is_point_in_playable_area(self, x, y):
        """Check if a point is within the playable area."""
        return self.is_point_in_region(x, y, 'playable')
    
    def get_region_for_point(self, x, y):
        """Get which region a point belongs to."""
        for region_name, region in self.regions.items():
            if self.is_point_in_region(x, y, region_name):
                return region_name
        return None
    
    def draw_ui_backgrounds(self, screen):
        """Draw background for all UI regions."""
        # Draw UI region backgrounds
        for region_name, region in self.regions.items():
            if region_name == 'playable':
                # Playable area gets a different background
                bg_color = self.colors['playable_bg']
            else:
                # UI regions get UI background
                bg_color = self.colors['ui_bg']
                
            pygame.draw.rect(screen, bg_color, 
                           (region['x'], region['y'], region['width'], region['height']))
        
        # Draw borders between regions
        border_color = self.colors['border']
        
        # Vertical borders
        pygame.draw.line(screen, border_color, 
                        (self.ui_panel_width, 0), 
                        (self.ui_panel_width, self.screen_height), 2)
        pygame.draw.line(screen, border_color, 
                        (self.screen_width - self.ui_panel_width, 0), 
                        (self.screen_width - self.ui_panel_width, self.screen_height), 2)
        
        # Horizontal borders
        pygame.draw.line(screen, border_color, 
                        (0, self.ui_top_height), 
                        (self.screen_width, self.ui_top_height), 2)
        pygame.draw.line(screen, border_color, 
                        (0, self.screen_height - self.ui_bottom_height), 
                        (self.screen_width, self.screen_height - self.ui_bottom_height), 2)
    
    def create_clipped_surface(self, region_name):
        """Create a surface clipped to a specific region."""
        region = self.regions[region_name]
        surface = pygame.Surface((region['width'], region['height']))
        return surface, (region['x'], region['y'])
    
    def draw_panel(self, screen, region_name, title, content_func, **kwargs):
        """Draw a panel within a specific region."""
        region = self.regions.get(region_name)
        if not region:
            return
            
        # Create panel background
        panel_surface = pygame.Surface((region['width'], region['height']))
        panel_surface.fill(self.colors['panel'])
        panel_surface.set_alpha(240)
        
        # Draw title if provided
        if title:
            title_surface = self.font_medium.render(title, True, self.colors['text'])
            panel_surface.blit(title_surface, (self.ui_margin, self.ui_margin))
            content_y = self.ui_margin + 30
        else:
            content_y = self.ui_margin
            
        # Draw content
        if content_func:
            content_func(panel_surface, self.ui_margin, content_y, 
                        region['width'] - 2 * self.ui_margin, **kwargs)
        
        # Blit to main screen
        screen.blit(panel_surface, (region['x'], region['y']))
