"""Responsive UI system for cross-platform compatibility."""

import pygame
import math
from .constants import *

class ResponsiveUI:
    """Handles responsive UI elements that adapt to screen size."""
    
    def __init__(self, screen_width, screen_height):
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.is_mobile = screen_width <= 800 or screen_height <= 600
        
        # Scale factors based on screen size
        self.scale_factor = min(screen_width / DEFAULT_SCREEN_WIDTH, 
                               screen_height / DEFAULT_SCREEN_HEIGHT)
        self.ui_scale = max(0.6, min(1.2, self.scale_factor))
        
        # Font sizes
        pygame.font.init()
        self.font_large = pygame.font.Font(None, int(36 * self.ui_scale))
        self.font_medium = pygame.font.Font(None, int(24 * self.ui_scale))
        self.font_small = pygame.font.Font(None, int(18 * self.ui_scale))
        
        # UI layout
        self._calculate_layout()
        
    def _calculate_layout(self):
        """Calculate responsive layout positions."""
        # Panel dimensions
        self.panel_width = int(min(250, self.screen_width * 0.25))
        self.panel_margin = int(10 * self.ui_scale)
        
        # Resource panel
        self.resource_panel = {
            'x': self.screen_width - self.panel_width - self.panel_margin,
            'y': self.panel_margin,
            'width': self.panel_width,
            'height': int(150 * self.ui_scale)
        }
        
        # Diver status panel
        self.diver_panel = {
            'x': self.screen_width - self.panel_width - self.panel_margin,
            'y': self.resource_panel['y'] + self.resource_panel['height'] + self.panel_margin,
            'width': self.panel_width,
            'height': int(120 * self.ui_scale)
        }
        
        # Grappling status panel
        self.grappling_panel = {
            'x': self.screen_width - self.panel_width - self.panel_margin,
            'y': self.diver_panel['y'] + self.diver_panel['height'] + self.panel_margin,
            'width': self.panel_width,
            'height': int(140 * self.ui_scale)
        }
        
        # Controls panel
        if self.is_mobile:
            # On mobile, put controls at bottom
            self.controls_panel = {
                'x': self.panel_margin,
                'y': self.screen_height - int(120 * self.ui_scale) - self.panel_margin,
                'width': min(int(350 * self.ui_scale), self.screen_width - 2 * self.panel_margin),
                'height': int(120 * self.ui_scale)
            }
        else:
            # On desktop, put controls on left
            self.controls_panel = {
                'x': self.panel_margin,
                'y': self.screen_height - int(180 * self.ui_scale) - self.panel_margin,
                'width': int(320 * self.ui_scale),
                'height': int(180 * self.ui_scale)
            }
            
        # Automation buttons
        button_size = int(80 * self.ui_scale)
        button_spacing = int(10 * self.ui_scale)
        
        if self.is_mobile:
            # Mobile: horizontal layout at top
            start_x = self.screen_width - (4 * button_size + 3 * button_spacing) - self.panel_margin
            start_y = self.panel_margin
        else:
            # Desktop: vertical layout on right
            start_x = self.screen_width - button_size - self.panel_margin
            start_y = self.grappling_panel['y'] + self.grappling_panel['height'] + self.panel_margin
            
        self.automation_buttons = []
        for i in range(4):  # AutoPilot, Crew AI, AutoCraft, Mouse Follow
            if self.is_mobile:
                x = start_x + i * (button_size + button_spacing)
                y = start_y
            else:
                x = start_x
                y = start_y + i * (button_size + button_spacing)
                
            self.automation_buttons.append({
                'rect': pygame.Rect(x, y, button_size, int(button_size * 0.6)),
                'index': i
            })
            
    def get_touch_zones(self):
        """Get touch-friendly interaction zones."""
        zones = {}
        
        # Automation buttons
        button_names = ['autopilot', 'crew_ai', 'auto_craft', 'mouse_follow']
        for i, button in enumerate(self.automation_buttons):
            zones[button_names[i]] = button['rect']
            
        return zones
        
    def draw_panel(self, screen, panel_info, title, content_func):
        """Draw a responsive panel with content."""
        x, y, width, height = panel_info['x'], panel_info['y'], panel_info['width'], panel_info['height']
        
        # Background with transparency
        panel_surface = pygame.Surface((width, height))
        panel_surface.set_alpha(200)
        panel_surface.fill(BLACK)
        screen.blit(panel_surface, (x, y))
        
        # Border
        pygame.draw.rect(screen, WHITE, (x, y, width, height), 2)
        
        # Title
        title_surface = self.font_medium.render(title, True, WHITE)
        screen.blit(title_surface, (x + self.panel_margin, y + self.panel_margin))
        
        # Content
        content_y = y + int(35 * self.ui_scale)
        content_func(screen, x + self.panel_margin, content_y, width - 2 * self.panel_margin)
        
    def draw_button(self, screen, rect, text, enabled, color_enabled=GREEN, color_disabled=RED):
        """Draw a responsive button."""
        color = color_enabled if enabled else color_disabled
        
        # Button background
        pygame.draw.rect(screen, color, rect)
        pygame.draw.rect(screen, WHITE, rect, 2)
        
        # Button text
        font_size = int(self.font_small.get_height() * 0.8)
        font = pygame.font.Font(None, font_size)
        
        # Split text into lines if too long
        words = text.split()
        lines = []
        current_line = ""
        
        for word in words:
            test_line = current_line + (" " if current_line else "") + word
            if font.size(test_line)[0] <= rect.width - 10:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        if current_line:
            lines.append(current_line)
            
        # Draw text lines
        total_height = len(lines) * font.get_height()
        start_y = rect.centery - total_height // 2
        
        for i, line in enumerate(lines):
            text_surface = font.render(line, True, WHITE)
            text_rect = text_surface.get_rect(centerx=rect.centerx, 
                                            y=start_y + i * font.get_height())
            screen.blit(text_surface, text_rect)
            
    def scale_position(self, x, y):
        """Scale position based on screen size."""
        return int(x * self.scale_factor), int(y * self.scale_factor)
        
    def scale_size(self, size):
        """Scale size based on screen size."""
        return int(size * self.scale_factor)
        
    def is_touch_device(self):
        """Check if this is likely a touch device."""
        return self.is_mobile
        
    def get_click_tolerance(self):
        """Get click tolerance for touch vs mouse."""
        return int(50 * self.ui_scale) if self.is_mobile else int(30 * self.ui_scale)
        
    def draw_depth_indicator(self, screen, submarine):
        """Draw responsive depth indicator."""
        x, y = self.panel_margin, self.panel_margin
        
        depth_text = self.font_large.render(f"Depth: {int(submarine.depth)}m", True, WHITE)
        screen.blit(depth_text, (x, y))
        
        max_depth_text = self.font_medium.render(f"Max: {submarine.max_depth}m", True, WHITE)
        screen.blit(max_depth_text, (x, y + int(40 * self.ui_scale)))
        
        speed_text = self.font_medium.render(f"Speed: {submarine.speed}", True, WHITE)
        screen.blit(speed_text, (x, y + int(65 * self.ui_scale)))
        
    def draw_mobile_instructions(self, screen):
        """Draw mobile-specific instructions."""
        if not self.is_mobile:
            return
            
        instruction_text = "Tap resources to deploy grappling hook"
        text_surface = self.font_small.render(instruction_text, True, WHITE)
        
        # Center at bottom of screen
        x = (self.screen_width - text_surface.get_width()) // 2
        y = self.screen_height - int(30 * self.ui_scale)
        
        # Background
        bg_rect = pygame.Rect(x - 10, y - 5, text_surface.get_width() + 20, text_surface.get_height() + 10)
        bg_surface = pygame.Surface((bg_rect.width, bg_rect.height))
        bg_surface.set_alpha(180)
        bg_surface.fill(BLACK)
        screen.blit(bg_surface, bg_rect)
        
        screen.blit(text_surface, (x, y))
