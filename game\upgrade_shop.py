"""Interactive upgrade shop interface."""

import pygame
import math
from .constants import *
from .modern_ui import ModernUI

class UpgradeShop:
    """Interactive upgrade shop with categories and visual feedback."""
    
    def __init__(self, screen_width, screen_height, upgrade_manager):
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.upgrade_manager = upgrade_manager
        self.modern_ui = ModernUI(screen_width, screen_height)
        
        self.is_open = False
        self.selected_category = 'submarine'
        self.scroll_offset = 0
        self.max_scroll = 0

        # Animation states
        self.open_animation = 0.0
        self.category_animations = {}
        
        # Layout
        self._calculate_layout()
        
    def _calculate_layout(self):
        """Calculate shop layout."""
        margin = int(20 * self.modern_ui.ui_scale)
        
        # Shop window - adaptive sizing to fit screen
        max_shop_width = self.screen_width - 4 * margin
        max_shop_height = self.screen_height - 4 * margin

        # Preferred sizes but constrained to screen
        shop_width = min(int(900 * self.modern_ui.ui_scale), max_shop_width)
        shop_height = min(int(650 * self.modern_ui.ui_scale), max_shop_height)
        
        self.shop_rect = pygame.Rect(
            (self.screen_width - shop_width) // 2,
            (self.screen_height - shop_height) // 2,
            shop_width,
            shop_height
        )
        
        # Category tabs - simplified layout
        tab_width = shop_width // 7  # Changed from 6 to 7 categories
        tab_height = 70  # Fixed tab height to match simplified drawing

        self.category_tabs = {}
        categories = ['submarine', 'grappling', 'crew', 'collection', 'automation', 'research', 'dock']
        category_names = ['Sub', 'Hooks', 'Crew', 'Range', 'Auto', 'Research', 'Dock']  # Simplified names

        for i, (category, name) in enumerate(zip(categories, category_names)):
            self.category_tabs[category] = {
                'rect': pygame.Rect(
                    self.shop_rect.x + i * tab_width,
                    self.shop_rect.y + 50,  # Position tabs below title
                    tab_width,
                    tab_height
                ),
                'name': name
            }
            
        # Upgrade list area - simplified layout
        tab_height = 70  # Fixed tab height to match simplified drawing
        title_height = 50  # Fixed title height
        self.upgrade_list_rect = pygame.Rect(
            self.shop_rect.x + margin,
            self.shop_rect.y + title_height + tab_height,  # Space for title and tabs
            self.shop_rect.width - 2 * margin,
            self.shop_rect.height - title_height - tab_height - 60  # Space for title, tabs, and bottom margin
        )
        
        # Close button - simplified positioning
        close_size = 30  # Fixed size
        self.close_button = pygame.Rect(
            self.shop_rect.right - close_size - margin,
            self.shop_rect.y + margin,
            close_size,
            close_size
        )
        
    def toggle_shop(self):
        """Toggle shop open/closed."""
        self.is_open = not self.is_open
        # Reset animation when opening to ensure it starts properly
        if self.is_open:
            self.open_animation = 0.0
        
    def handle_click(self, pos):
        """Handle click events in the shop."""
        if not self.is_open:
            return False

        # Check if click is on the upgrade button area (don't close shop for button clicks)
        from game.constants import SCREEN_HEIGHT
        button_y = SCREEN_HEIGHT - 70
        button_height = 50
        upgrade_button_rect = pygame.Rect(20, button_y, 140, button_height)

        if upgrade_button_rect.collidepoint(pos):
            # Click is on the upgrade button, don't close shop
            return True

        # Check if click is outside shop (close shop)
        if not self.shop_rect.collidepoint(pos):
            self.is_open = False
            return True

        # Close button
        if self.close_button.collidepoint(pos):
            self.is_open = False
            return True

        # Category tabs
        for category, tab_info in self.category_tabs.items():
            if tab_info['rect'].collidepoint(pos):
                self.selected_category = category
                self.scroll_offset = 0
                return True

        # Upgrade buttons - return the upgrade name if clicked, not just True/False
        upgrade_result = self._handle_upgrade_click(pos)
        if upgrade_result:
            return upgrade_result

        # Click was inside shop but not on any interactive element - keep shop open
        return True
        
    def _handle_upgrade_click(self, pos):
        """Handle clicks on upgrade buttons."""
        if not self.upgrade_list_rect.collidepoint(pos):
            return None

        # Calculate which upgrade was clicked using the same sizing as the simplified drawing
        relative_y = pos[1] - self.upgrade_list_rect.y + self.scroll_offset

        # Use the same fixed sizes as in _draw_simple_upgrade_list
        upgrade_height = 60
        upgrade_spacing = 10

        upgrade_index = relative_y // (upgrade_height + upgrade_spacing)

        upgrades = list(self.upgrade_manager.upgrades[self.selected_category].keys())
        if 0 <= upgrade_index < len(upgrades):
            upgrade_name = upgrades[upgrade_index]
            return upgrade_name  # Return the upgrade name instead of trying to purchase

        return None
        
    def _try_purchase_upgrade(self, upgrade_name):
        """Try to purchase an upgrade."""
        # This will be set by the game when handling clicks
        return True

    def try_purchase_upgrade(self, upgrade_name, resources):
        """Try to purchase an upgrade with actual resources."""
        return self.upgrade_manager.purchase_upgrade(self.selected_category, upgrade_name, resources)
        
    def update(self, dt):
        """Update shop animations."""
        # Open/close animation - use same system as dock upgrade shop
        if self.is_open and self.open_animation < 1.0:
            self.open_animation = min(1.0, self.open_animation + dt * 0.05)
        elif not self.is_open and self.open_animation > 0.0:
            self.open_animation = max(0.0, self.open_animation - dt * 0.05)
        
        # Category animations
        for category in self.category_tabs:
            if category not in self.category_animations:
                self.category_animations[category] = 0.0
                
            target = 1.0 if category == self.selected_category else 0.0
            self.category_animations[category] += (target - self.category_animations[category]) * 0.15
            
    def draw(self, screen, resources):
        """Draw the upgrade shop."""
        if self.open_animation <= 0.01:
            return

        # Semi-transparent overlay
        overlay = pygame.Surface((self.screen_width, self.screen_height))
        overlay.set_alpha(int(120 * self.open_animation))
        overlay.fill((0, 0, 0))
        screen.blit(overlay, (0, 0))
        
        # Simple shop background (bypass modern UI for now)
        pygame.draw.rect(screen, (40, 40, 60), self.shop_rect)
        pygame.draw.rect(screen, (100, 100, 150), self.shop_rect, 3)

        # Simple title
        font = pygame.font.Font(None, 36)
        title_text = "UPGRADE SHOP"
        title_surface = font.render(title_text, True, (255, 255, 255))
        title_x = self.shop_rect.x + (self.shop_rect.width - title_surface.get_width()) // 2
        title_y = self.shop_rect.y + 15
        screen.blit(title_surface, (title_x, title_y))
        
        # Simple category tabs
        self._draw_simple_category_tabs(screen)

        # Upgrade list
        self._draw_simple_upgrade_list(screen, resources)

        # Simple close button
        pygame.draw.rect(screen, (200, 100, 100), self.close_button)
        close_font = pygame.font.Font(None, 24)
        close_text = close_font.render("X", True, (255, 255, 255))
        close_rect = close_text.get_rect(center=self.close_button.center)
        screen.blit(close_text, close_rect)
        
    def _draw_simple_category_tabs(self, screen):
        """Draw simplified category tabs."""
        font = pygame.font.Font(None, 24)
        categories = ['submarine', 'grappling', 'crew', 'automation', 'research', 'dock']
        category_names = ['Sub', 'Hooks', 'Crew', 'Auto', 'Research', 'Dock']

        for i, (category, name) in enumerate(zip(categories, category_names)):
            tab_info = self.category_tabs[category]
            rect = tab_info['rect']

            # Tab background
            if category == self.selected_category:
                color = (100, 150, 200)
            else:
                color = (60, 60, 80)

            pygame.draw.rect(screen, color, rect)
            pygame.draw.rect(screen, (150, 150, 150), rect, 2)

            # Tab text
            text_surface = font.render(name, True, (255, 255, 255))
            text_rect = text_surface.get_rect(center=rect.center)
            screen.blit(text_surface, text_rect)

    def _draw_simple_upgrade_list(self, screen, resources):
        """Draw simplified upgrade list."""
        upgrades = self.upgrade_manager.upgrades[self.selected_category]
        font = pygame.font.Font(None, 20)

        y_offset = 0
        upgrade_height = 60
        upgrade_spacing = 10

        for upgrade_name, upgrade_info in upgrades.items():
            upgrade_y = self.upgrade_list_rect.y + y_offset

            # Skip if not visible
            if upgrade_y + upgrade_height > self.upgrade_list_rect.bottom:
                break

            # Upgrade button rect
            upgrade_rect = pygame.Rect(
                self.upgrade_list_rect.x,
                upgrade_y,
                self.upgrade_list_rect.width,
                upgrade_height
            )

            # Check if affordable
            can_afford = self.upgrade_manager.can_afford_upgrade(self.selected_category, upgrade_name, resources)
            is_maxed = upgrade_info['level'] >= upgrade_info['max_level']

            # Background color
            if is_maxed:
                bg_color = (100, 150, 100)
            elif can_afford:
                bg_color = (100, 100, 150)
            else:
                bg_color = (80, 80, 80)

            pygame.draw.rect(screen, bg_color, upgrade_rect)
            pygame.draw.rect(screen, (150, 150, 150), upgrade_rect, 2)

            # Upgrade name and level
            level_text = f"{upgrade_name} (Level {upgrade_info['level']}/{upgrade_info['max_level']})"
            text_surface = font.render(level_text, True, (255, 255, 255))
            screen.blit(text_surface, (upgrade_rect.x + 10, upgrade_rect.y + 10))

            # Cost
            if not is_maxed:
                cost = self.upgrade_manager.get_upgrade_cost(self.selected_category, upgrade_name)
                if cost:
                    cost_text = f"Cost: {cost}"
                    cost_surface = font.render(cost_text, True, (200, 200, 200))
                    screen.blit(cost_surface, (upgrade_rect.x + 10, upgrade_rect.y + 30))

            y_offset += upgrade_height + upgrade_spacing

    def _draw_category_tabs(self, screen):
        """Draw category selection tabs."""
        for category, tab_info in self.category_tabs.items():
            is_selected = category == self.selected_category
            animation = self.category_animations.get(category, 0.0)
            
            # Tab background
            tab_color = self.modern_ui.colors['primary'] if is_selected else self.modern_ui.colors['dark']
            
            # Animate tab
            tab_rect = tab_info['rect'].copy()
            if is_selected:
                tab_rect.y -= int(5 * animation)
                
            pygame.draw.rect(screen, tab_color, tab_rect)
            pygame.draw.rect(screen, self.modern_ui.colors['border'], tab_rect, 2)
            
            # Tab text
            text_color = self.modern_ui.colors['light'] if is_selected else self.modern_ui.colors['text_dim']
            text_surface = self.modern_ui.fonts['small'].render(tab_info['name'], True, text_color)
            text_rect = text_surface.get_rect(center=tab_rect.center)
            screen.blit(text_surface, text_rect)
            
    def _draw_upgrade_list(self, screen, resources):
        """Draw the list of upgrades for the selected category."""
        upgrades = self.upgrade_manager.upgrades[self.selected_category]
        upgrade_data = self.upgrade_manager.upgrade_data[self.selected_category]
        
        y_offset = 0
        # Adaptive upgrade item height based on available space
        available_height = self.upgrade_list_rect.height
        max_items_visible = 6  # Aim to show at least 6 items

        upgrade_height = min(int(120 * self.modern_ui.ui_scale),
                           int(available_height / max_items_visible * 0.8))
        upgrade_spacing = int(min(15, upgrade_height * 0.1) * self.modern_ui.ui_scale)
        
        for upgrade_name, upgrade_info in upgrades.items():
            upgrade_y = self.upgrade_list_rect.y + y_offset - self.scroll_offset
            
            # Skip if not visible
            if upgrade_y + upgrade_height < self.upgrade_list_rect.y or upgrade_y > self.upgrade_list_rect.bottom:
                y_offset += upgrade_height + upgrade_spacing
                continue
                
            # Upgrade button rect
            upgrade_rect = pygame.Rect(
                self.upgrade_list_rect.x,
                upgrade_y,
                self.upgrade_list_rect.width,
                upgrade_height
            )
            
            # Check if affordable
            can_afford = self.upgrade_manager.can_afford_upgrade(self.selected_category, upgrade_name, resources)
            is_maxed = upgrade_info['level'] >= upgrade_info['max_level']
            
            self._draw_upgrade_item(screen, upgrade_rect, upgrade_name, upgrade_info, 
                                  upgrade_data[upgrade_name], resources, can_afford, is_maxed)
            
            y_offset += upgrade_height + upgrade_spacing
            
    def _draw_upgrade_item(self, screen, rect, upgrade_name, upgrade_info, upgrade_data, 
                          resources, can_afford, is_maxed):
        """Draw an individual upgrade item."""
        # Background
        if is_maxed:
            bg_color = self.modern_ui.colors['success']
            bg_alpha = 100
        elif can_afford:
            bg_color = self.modern_ui.colors['primary']
            bg_alpha = 150
        else:
            bg_color = self.modern_ui.colors['dark']
            bg_alpha = 120
            
        bg_surface = pygame.Surface((rect.width, rect.height))
        bg_surface.set_alpha(bg_alpha)
        bg_surface.fill(bg_color)
        screen.blit(bg_surface, rect)
        
        # Border
        border_color = self.modern_ui.colors['success'] if is_maxed else self.modern_ui.colors['border']
        pygame.draw.rect(screen, border_color, rect, 2)
        
        # Icon and title with larger fonts
        icon = upgrade_data['icon']
        title = upgrade_data['name']

        icon_surface = self.modern_ui.fonts['title'].render(icon, True, self.modern_ui.colors['light'])  # Larger icon
        screen.blit(icon_surface, (rect.x + 15, rect.y + 15))  # More padding

        title_surface = self.modern_ui.fonts['heading'].render(title, True, self.modern_ui.colors['light'])
        screen.blit(title_surface, (rect.x + 70, rect.y + 15))  # Adjusted position
        
        # Level and progress - adjusted for taller items
        level_text = f"Level {upgrade_info['level']}/{upgrade_info['max_level']}"
        if is_maxed:
            level_text += " (MAX)"

        level_surface = self.modern_ui.fonts['body'].render(level_text, True,   # Larger font
                                                           self.modern_ui.colors['text_dim'])
        screen.blit(level_surface, (rect.right - 200, rect.y + 20))  # More space from edge

        # Progress bar - larger and better positioned
        progress = upgrade_info['level'] / upgrade_info['max_level']
        progress_rect = pygame.Rect(rect.x + 80, rect.y + 55, rect.width - 300, 16)  # Taller progress bar
        self.modern_ui.draw_progress_bar(screen, progress_rect.x, progress_rect.y,
                                       progress_rect.width, progress_rect.height, progress)

        # Cost (if not maxed) - larger font and better positioned
        if not is_maxed:
            cost = self.upgrade_manager.get_upgrade_cost(self.selected_category, upgrade_name)
            cost_text = " | ".join([f"{amount} {resource.title()}" for resource, amount in cost.items()])
            cost_color = self.modern_ui.colors['light'] if can_afford else self.modern_ui.colors['danger']
            cost_surface = self.modern_ui.fonts['body'].render(cost_text, True, cost_color)  # Larger font
            screen.blit(cost_surface, (rect.x + 80, rect.y + 80))  # Better position

        # Description - larger font and better positioned
        desc_surface = self.modern_ui.fonts['small'].render(upgrade_data['description'], True,   # Larger font
                                                         self.modern_ui.colors['text_dim'])
        screen.blit(desc_surface, (rect.x + 80, rect.y + 110))  # Better position for taller items
