"""Grappling hook system for autonomous resource collection."""

import pygame
import math
import random
from .constants import *

class GrapplingHook:
    """Individual grappling hook that can collect resources."""
    
    def __init__(self, submarine_x, submarine_y, hook_id, range_multiplier=1.0, speed_multiplier=1.0):
        self.submarine_x = submarine_x
        self.submarine_y = submarine_y
        self.hook_id = hook_id
        
        # Upgrade properties
        self.range_multiplier = range_multiplier
        self.speed_multiplier = speed_multiplier
        self.base_range = 100  # Unified base range with divers
        self.max_range = self.base_range * range_multiplier  # Will be modified by unified range upgrades
        self.speed = 2 * speed_multiplier  # Reduced from 3 to 2 for slower start
        self._last_range_bonus = 0  # Track last applied range bonus
        
        # Hook state
        self.state = 'idle'  # idle, extending, retracting, collecting
        self.x = submarine_x
        self.y = submarine_y
        self.target_x = submarine_x
        self.target_y = submarine_y
        self.target_resource = None
        self.collected_resource = None
        
        # Visual properties
        self.segments = []
        self.hook_color = (200, 200, 200)
        self.cable_color = (150, 150, 150)
        
        # Timing
        self.idle_timer = 0
        self.collection_timer = 0
        self.state_timer = 0  # Track how long we've been in current state
        
    def update(self, submarine_x, submarine_y, resources, dt, submarine_stationary=True, submarine=None):
        """Update grappling hook behavior."""
        self.submarine_x = submarine_x
        self.submarine_y = submarine_y

        # Increment state timer
        self.state_timer += 1

        # Safety timeout - if stuck in any non-idle state for too long, force return
        if self.state != 'idle' and self.state_timer > 600:  # 10 seconds timeout
            self._start_retracting()
            self.state_timer = 0

        if self.state == 'idle':
            # Keep hook position synchronized with submarine when idle
            self.x = submarine_x
            self.y = submarine_y
            # Only find new targets if submarine is stationary
            if submarine_stationary:
                self._find_target(resources, submarine)
            self.idle_timer += 1
            
        elif self.state == 'extending':
            # Check if target resource still exists and is not collected
            if self.target_resource and self.target_resource.collected:
                self._start_retracting()
                return
            self._extend_to_target(dt)
            
        elif self.state == 'collecting':
            self.collection_timer += 1

            # Check if hook is still close enough to the resource
            if self.target_resource:
                distance_to_resource = math.sqrt((self.target_resource.x - self.x)**2 + (self.target_resource.y - self.y)**2)
                if distance_to_resource > 15:  # If hook drifted too far from resource
                    self._start_retracting()
                    return

            # Check if submarine can no longer store this resource type (storage became full)
            if (self.target_resource and submarine and
                not submarine.can_store_resource(self.target_resource.type, 1)):
                # Storage is full for this resource type, abandon collection
                if self.target_resource:
                    self.target_resource.stop_collection()
                    # Mark that we abandoned due to storage (for potential notification)
                    self.abandoned_due_to_storage = True
                self._start_retracting()
                return

            # Collect incrementally every few frames
            if self.collection_timer % 60 == 0:  # Collect every 1.0 seconds (slower for better balance)
                if self.target_resource and not self.target_resource.collected:
                    # Collect a small increment
                    resource_type, collected_amount = self.target_resource.collect_increment(1)
                    if collected_amount > 0:
                        # Store the collected amount (we'll deliver it when retracting)
                        if not hasattr(self, 'collected_amount'):
                            self.collected_amount = 0
                        self.collected_amount += collected_amount
                        self.collected_resource = (resource_type, self.collected_amount)

            # Only retract if resource is fully collected, removed, or we've been collecting too long
            if not self.target_resource:  # Resource was removed
                self._start_retracting()
            elif self.target_resource.collected:  # Resource is fully depleted
                self._start_retracting()
            elif self.collection_timer >= 600:  # 10 seconds max (safety timeout)
                # Force retract if stuck collecting for too long
                self._start_retracting()
                
        elif self.state == 'retracting':
            collected = self._retract_to_submarine(dt)
            if collected:
                self._update_cable_segments()
                return collected

        self._update_cable_segments()
        return None
        
    def _find_target(self, resources, submarine=None):
        """Find a nearby resource to collect, but only when divers are idle and not targeting the same resource."""
        if self.idle_timer < 180:  # Wait 3 seconds between attempts (increased to give divers priority)
            return

        self.idle_timer = 0

        # Check if all divers are idle - grappling hooks should only work when divers are not active
        if submarine and hasattr(submarine, 'get_active_divers'):
            active_divers = submarine.get_active_divers()
            if active_divers:
                return  # Don't deploy grappling hooks while divers are working

        # Get list of resources that divers are currently targeting or working on
        diver_targeted_resources = set()
        if submarine and hasattr(submarine, 'get_diver_targeted_resources'):
            diver_targeted_resources = submarine.get_diver_targeted_resources()

        # Find resources within range
        nearby_resources = []
        for resource in resources:
            if resource.collected:
                continue

            # Skip resources that divers are already targeting or working on
            if resource in diver_targeted_resources:
                continue

            # Check if submarine can store this resource type
            if submarine and not submarine.can_store_resource(resource.type, 1):
                # Mark that we're skipping due to storage (for potential notification)
                if not hasattr(self, '_storage_skip_notified'):
                    self._storage_skip_notified = {}
                self._storage_skip_notified[resource.type] = True
                continue  # Skip if submarine storage is full for this resource type

            distance = math.sqrt((resource.x - self.submarine_x)**2 + (resource.y - self.submarine_y)**2)
            if distance <= self.max_range:
                nearby_resources.append((resource, distance))

        if nearby_resources:
            # Sort by priority (treasure > pearl > coral > kelp) and distance
            def resource_priority(item):
                resource, distance = item
                priority_values = {'treasure': 4, 'pearl': 3, 'coral': 2, 'kelp': 1}
                priority = priority_values.get(resource.type, 1)
                return priority * 100 - distance

            nearby_resources.sort(key=resource_priority, reverse=True)

            # Target the best resource
            best_resource, _ = nearby_resources[0]
            self.target_resource = best_resource
            self.target_x = best_resource.x
            self.target_y = best_resource.y
            self.state = 'extending'
            
    def _extend_to_target(self, dt):
        """Extend hook towards target resource."""
        dx = self.target_x - self.x
        dy = self.target_y - self.y
        distance = math.sqrt(dx**2 + dy**2)

        if distance < 5:
            # Reached target
            self.state = 'collecting'
            self.collection_timer = 0
            self.state_timer = 0  # Reset state timer
            if self.target_resource:
                # Start the collection process instead of instantly collecting
                self.target_resource.start_collection()
                self.collected_resource = self.target_resource.type  # Store just type for now, value comes later
        elif distance > 0:  # Prevent division by zero
            # Move towards target
            move_x = (dx / distance) * self.speed
            move_y = (dy / distance) * self.speed
            self.x += move_x
            self.y += move_y
            
    def _start_retracting(self):
        """Start retracting the hook."""
        self.state = 'retracting'
        self.state_timer = 0  # Reset state timer
        # Target the current submarine position (updated in each frame)
        self.target_x = self.submarine_x
        self.target_y = self.submarine_y

        # Handle collection based on resource state
        if self.target_resource:
            # Always stop the collection process
            self.target_resource.stop_collection()

            # If we have accumulated collected resources, keep them
            if hasattr(self, 'collected_amount') and self.collected_amount > 0:
                # We already have the collected resource from incremental collection
                pass
            elif self.target_resource.collected:
                # Resource was just fully depleted - collect any remaining value
                resource_type, remaining_value = self.target_resource.collect()
                if remaining_value > 0:
                    if not hasattr(self, 'collected_amount'):
                        self.collected_amount = 0
                    self.collected_amount += remaining_value
                    self.collected_resource = (resource_type, self.collected_amount)

            # Clear target resource reference
            self.target_resource = None
        
    def _retract_to_submarine(self, dt):
        """Retract hook back to submarine."""
        # Always target the current submarine position (submarine may be moving)
        self.target_x = self.submarine_x
        self.target_y = self.submarine_y

        dx = self.submarine_x - self.x
        dy = self.submarine_y - self.y
        distance = math.sqrt(dx**2 + dy**2)

        if distance < 15:  # Reduced back to 15 for more precise return
            # Reached submarine
            self.state = 'idle'
            self.x = self.submarine_x
            self.y = self.submarine_y
            collected = self.collected_resource
            self.collected_resource = None
            self.target_resource = None
            # Reset collected amount for next collection cycle
            if hasattr(self, 'collected_amount'):
                self.collected_amount = 0
            return collected
        elif distance > 0:  # Prevent division by zero
            # Move towards submarine's current position
            move_x = (dx / distance) * self.speed * 1.5  # Retract faster
            move_y = (dy / distance) * self.speed * 1.5
            self.x += move_x
            self.y += move_y

        return None
        
    def _update_cable_segments(self):
        """Update cable segments for smooth drawing."""
        self.segments = []
        
        # Calculate cable segments
        dx = self.x - self.submarine_x
        dy = self.y - self.submarine_y
        distance = math.sqrt(dx**2 + dy**2)
        
        if distance > 0:
            num_segments = max(3, int(distance / 20))
            for i in range(num_segments + 1):
                t = i / num_segments
                # Add slight curve to cable
                curve_offset = math.sin(t * math.pi) * 10
                seg_x = self.submarine_x + dx * t + curve_offset
                seg_y = self.submarine_y + dy * t
                self.segments.append((seg_x, seg_y))
                
    def draw(self, screen):
        """Draw the grappling hook and cable."""
        if self.state == 'idle':
            return

        # Draw cable segments with enhanced visuals
        if len(self.segments) > 1:
            for i in range(len(self.segments) - 1):
                start_pos = (int(self.segments[i][0]), int(self.segments[i][1]))
                end_pos = (int(self.segments[i + 1][0]), int(self.segments[i + 1][1]))

                # Draw cable with varying thickness based on state
                cable_thickness = 3
                if self.state == 'collecting':
                    cable_thickness = 4
                elif self.collected_resource:
                    cable_thickness = 5

                pygame.draw.line(screen, self.cable_color, start_pos, end_pos, cable_thickness)

        # Draw hook with enhanced animations
        hook_size = 8
        hook_x = int(self.x)
        hook_y = int(self.y)

        if self.state == 'collecting':
            # Pulsing animation when collecting
            pulse = math.sin(self.state_timer * 0.3) * 0.3 + 0.7
            hook_size = int(12 * pulse)
            self.hook_color = (int(255 * pulse), int(255 * pulse), 0)  # Pulsing yellow

            # Add collection sparks
            for i in range(4):
                spark_angle = (self.state_timer * 0.2 + i * 1.57)  # 90 degrees apart
                spark_x = hook_x + math.cos(spark_angle) * 15
                spark_y = hook_y + math.sin(spark_angle) * 15
                pygame.draw.circle(screen, YELLOW, (int(spark_x), int(spark_y)), 2)

        elif self.state == 'extending':
            # Slight animation when extending
            hook_size = 8 + int(math.sin(self.state_timer * 0.1) * 2)
            self.hook_color = (200, 200, 200)
        elif self.state == 'retracting':
            # Animation when retracting
            if self.collected_resource:
                self.hook_color = GREEN
                hook_size = 10
            else:
                self.hook_color = (200, 200, 200)
        else:
            self.hook_color = (200, 200, 200)

        pygame.draw.circle(screen, self.hook_color, (hook_x, hook_y), hook_size)
        pygame.draw.circle(screen, WHITE, (hook_x, hook_y), hook_size, 2)

        # Draw collected resource indicator with animation
        if self.collected_resource:
            resource_colors = {
                'kelp': GREEN,
                'coral': ORANGE,
                'pearl': WHITE,
                'treasure': GOLD
            }
            # Handle both old format (string) and new format (tuple)
            resource_type = self.collected_resource
            if isinstance(self.collected_resource, tuple):
                resource_type = self.collected_resource[0]

            color = resource_colors.get(resource_type, WHITE)
            # Animated resource indicator
            resource_pulse = math.sin(self.state_timer * 0.2) * 0.5 + 0.5
            resource_size = int(4 + resource_pulse * 2)
            pygame.draw.circle(screen, color, (hook_x, hook_y), resource_size)
            
    def is_busy(self):
        """Check if hook is currently busy."""
        return self.state != 'idle'
        
    def upgrade_range(self, multiplier=1.2):
        """Upgrade hook range."""
        self.range_multiplier *= multiplier
        self.max_range = 150 * self.range_multiplier
        
    def upgrade_speed(self, multiplier=1.3):
        """Upgrade hook speed."""
        self.speed_multiplier *= multiplier
        self.speed = 3 * self.speed_multiplier

class PlayerGrapplingHook:
    """Player-controlled grappling hook that can be manually targeted."""

    def __init__(self, submarine):
        self.submarine = submarine
        self.hook = None
        self.range_upgrades = 0
        self.speed_upgrades = 0
        self.base_range = 100  # Base range before upgrades
        self.max_range = self.base_range  # Will be modified by unified range upgrades
        self.hook_speed = 3   # Reduced from 4 to 3 for slower start

    def deploy_to_position(self, target_x, target_y, resources=None):
        """Deploy grappling hook to specific position."""
        # Don't deploy if submarine is docked
        if self.submarine.is_docked:
            return False

        # Player hook can deploy even when submarine is moving (more responsive)
        # Only check if hook is already busy
        if self.hook and self.hook.state != 'idle':
            return False

        submarine_center = self.submarine.get_center_pos()
        distance = math.sqrt((target_x - submarine_center[0])**2 + (target_y - submarine_center[1])**2)

        if distance <= self.max_range:
            # Create new hook if none exists, or reuse existing idle hook
            if not self.hook:
                self.hook = GrapplingHook(
                    submarine_center[0], submarine_center[1],
                    'player',
                    1.0 + self.range_upgrades * 0.3,
                    1.0 + self.speed_upgrades * 0.2
                )
                self.hook.hook_color = (255, 215, 0)  # Gold color for player hook
                self.hook.cable_color = (255, 215, 0)
            else:
                # Reset existing hook for new deployment
                self.hook.submarine_x = submarine_center[0]
                self.hook.submarine_y = submarine_center[1]
                self.hook.x = submarine_center[0]
                self.hook.y = submarine_center[1]
                self.hook.collected_resource = None
                self.hook.target_resource = None
                self.hook.state_timer = 0
                self.hook.idle_timer = 0
                self.hook.collection_timer = 0

            # Set target and deploy
            self.hook.target_x = target_x
            self.hook.target_y = target_y
            self.hook.state = 'extending'

            # Look for resources near the target position
            if resources:
                for resource in resources:
                    if not resource.collected:
                        res_distance = math.sqrt((resource.x - target_x)**2 + (resource.y - target_y)**2)
                        if res_distance <= 30:  # Within 30 pixels of click
                            self.hook.target_resource = resource
                            self.hook.target_x = resource.x
                            self.hook.target_y = resource.y
                            break

            return True
        return False

    def deploy_to_resource(self, resource):
        """Deploy grappling hook to a specific resource."""
        # Player hook can deploy even when submarine is moving (more responsive)
        # Only check if hook is already busy
        if self.hook and self.hook.state != 'idle':
            return False

        # Don't deploy if submarine is docked
        if self.submarine.is_docked:
            return False

        if resource and not resource.collected:
            submarine_center = self.submarine.get_center_pos()
            distance = math.sqrt((resource.x - submarine_center[0])**2 + (resource.y - submarine_center[1])**2)

            if distance <= self.max_range:
                # Create new hook if none exists, or reuse existing idle hook
                if not self.hook:
                    self.hook = GrapplingHook(
                        submarine_center[0], submarine_center[1],
                        'player',
                        1.0 + self.range_upgrades * 0.3,
                        1.0 + self.speed_upgrades * 0.2
                    )
                    self.hook.hook_color = (255, 215, 0)  # Gold color for player hook
                    self.hook.cable_color = (255, 215, 0)
                else:
                    # Reset existing hook for new deployment
                    self.hook.submarine_x = submarine_center[0]
                    self.hook.submarine_y = submarine_center[1]
                    self.hook.x = submarine_center[0]
                    self.hook.y = submarine_center[1]
                    self.hook.collected_resource = None
                    self.hook.target_resource = None
                    self.hook.state_timer = 0
                    self.hook.idle_timer = 0
                    self.hook.collection_timer = 0

                # Set target and deploy
                self.hook.target_x = resource.x
                self.hook.target_y = resource.y
                self.hook.target_resource = resource  # Set the target resource!
                self.hook.state = 'extending'
                return True
        return False

    def update(self, resources, dt):
        """Update player grappling hook."""
        if self.hook:
            submarine_center = self.submarine.get_center_pos()
            # Player hook works even when submarine is moving (more responsive)
            submarine_stationary = True  # Always treat as stationary for player hook
            collected = self.hook.update(submarine_center[0], submarine_center[1], resources, dt, submarine_stationary, self.submarine)
            if collected:
                return collected
        return None

    def draw(self, screen):
        """Draw player grappling hook."""
        if self.hook:
            self.hook.draw(screen)

    def draw_range_indicator(self, screen, submarine_pos):
        """Draw range indicator circle around submarine."""
        import pygame
        # Draw a faint circle showing grappling hook range
        pygame.draw.circle(screen, (255, 255, 255, 30), submarine_pos, int(self.max_range), 2)

    def upgrade_range(self):
        """Upgrade hook range - DEPRECATED: Now handled by unified range system."""
        # This method is kept for compatibility but range is now handled by unified system
        pass

    def upgrade_speed(self):
        """Upgrade hook speed."""
        self.speed_upgrades += 1
        self.hook_speed = 3 + (self.speed_upgrades * 1)  # Start at 3, +1 per upgrade
        if self.hook:
            self.hook.upgrade_speed()

    def is_busy(self):
        """Check if hook is currently deployed."""
        return self.hook and self.hook.state != 'idle'

    def get_stats(self):
        """Get player hook stats."""
        return {
            'max_range': self.max_range,
            'speed': self.hook_speed,
            'range_upgrades': self.range_upgrades,
            'speed_upgrades': self.speed_upgrades,
            'is_busy': self.is_busy()
        }

    def force_return(self):
        """Force the player hook to return immediately to resolve stuck states."""
        if self.hook and self.hook.state != 'idle':
            self.hook.state = 'retracting'  # Use retracting instead of returning
            self.hook.target_resource = None
            self.hook.target_x = self.hook.submarine_x
            self.hook.target_y = self.hook.submarine_y

class GrapplingSystem:
    """Manages multiple automated grappling hooks for the submarine."""

    def __init__(self, submarine):
        self.submarine = submarine
        self.hooks = []
        self.max_hooks = 2
        self.hook_upgrades = {
            'count': 0,
            'range': 0,
            'speed': 0
        }

        # Initialize starting hooks
        self._spawn_hooks()
        
    def _spawn_hooks(self):
        """Spawn initial grappling hooks."""
        self.hooks = []
        for i in range(min(self.max_hooks, 2 + self.hook_upgrades['count'])):
            hook = GrapplingHook(
                self.submarine.x + self.submarine.width // 2,
                self.submarine.y + self.submarine.height // 2,
                i,
                1.0 + self.hook_upgrades['range'] * 0.3,
                1.0 + self.hook_upgrades['speed'] * 0.2
            )
            self.hooks.append(hook)
            
    def update(self, resources, dt):
        """Update all grappling hooks."""
        submarine_center_x = self.submarine.x + self.submarine.width // 2
        submarine_center_y = self.submarine.y + self.submarine.height // 2

        collected_resources = []

        # Only allow new deployments if submarine is stationary and not docked
        submarine_stationary = self.submarine.is_stationary() and not self.submarine.is_docked

        for hook in self.hooks:
            collected = hook.update(submarine_center_x, submarine_center_y, resources, dt, submarine_stationary, self.submarine)
            if collected:
                collected_resources.append(collected)

        return collected_resources
        
    def draw(self, screen):
        """Draw all grappling hooks."""
        for hook in self.hooks:
            hook.draw(screen)
            
    def upgrade_hook_count(self):
        """Add an additional grappling hook."""
        if len(self.hooks) < 6:  # Maximum 6 hooks
            self.hook_upgrades['count'] += 1
            self._spawn_hooks()
            return True
        return False
        
    def upgrade_hook_range(self):
        """Upgrade range of all hooks - DEPRECATED: Now handled by unified range system."""
        # This method is kept for compatibility but range is now handled by unified system
        return True
        
    def upgrade_hook_speed(self):
        """Upgrade speed of all hooks."""
        self.hook_upgrades['speed'] += 1
        for hook in self.hooks:
            hook.upgrade_speed()
        return True
        
    def get_upgrade_costs(self):
        """Get costs for different upgrades."""
        base_costs = {
            'count': {'coral': 5, 'pearl': 2},
            'range': {'kelp': 15, 'coral': 3},
            'speed': {'kelp': 10, 'treasure': 1}
        }
        
        # Increase costs based on current upgrade level
        costs = {}
        for upgrade_type, base_cost in base_costs.items():
            level = self.hook_upgrades[upgrade_type]
            costs[upgrade_type] = {}
            for resource, amount in base_cost.items():
                costs[upgrade_type][resource] = amount * (level + 1)
                
        return costs
        
    def get_stats(self):
        """Get current grappling system stats."""
        # Get actual range from first hook if available, otherwise use base range
        actual_range = self.hooks[0].max_range if self.hooks else 60
        return {
            'hook_count': len(self.hooks),
            'max_range': int(actual_range),  # Use actual current range from unified system
            'speed_multiplier': round(1.0 + self.hook_upgrades['speed'] * 0.2, 1),
            'upgrades': self.hook_upgrades.copy()
        }

    def force_all_hooks_return(self):
        """Force all hooks to return immediately to resolve stuck states."""
        for hook in self.hooks:
            if hook.state != 'idle':
                hook.state = 'retracting'  # Use retracting instead of returning
                hook.target_resource = None
                hook.target_x = hook.submarine_x
                hook.target_y = hook.submarine_y
