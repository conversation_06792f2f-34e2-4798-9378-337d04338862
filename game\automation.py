"""Automation system for idle gameplay mechanics."""

import pygame
import random
import math
from .constants import *

class AutoPilot:
    """Automatic submarine movement system."""
    
    def __init__(self):
        self.enabled = True
        self.target_depth = 0
        self.patrol_mode = 'explore'  # explore, resource_hunt, return_surface
        self.patrol_timer = 0
        self.patrol_interval = 300  # 5 seconds (increased to be less aggressive and allow diver operations)
        self.movement_target = None
        self.last_position_score = 0  # Track last position score to prevent oscillation
        self.position_stability_timer = 0  # Timer to enforce minimum stay duration
        self.target_lock_timer = 0  # Timer to prevent rapid target switching
        self.locked_target = None  # Current locked target to prevent oscillation
        self.post_undocking_grace_timer = 0  # Grace period after undocking to prevent immediate surface return
        
    def update(self, submarine, resources, dt, oxygen_level=1000, divers=None, mouse_controller=None, dock=None):
        """Update autopilot behavior."""
        if not self.enabled:
            return



        # Don't interfere if submarine is docked and being unloaded
        if dock and dock.is_occupied and dock.docked_submarine == submarine:
            return

        # Don't interfere if submarine is being smoothly undocked
        if dock and dock.undocking_submarine == submarine:
            return

        # Check if undocking just completed and set grace period
        if (dock and hasattr(dock, '_last_undocking_submarine') and
            dock._last_undocking_submarine == submarine and
            dock.undocking_submarine != submarine):
            # Undocking just completed, start grace period
            self.post_undocking_grace_timer = 300  # 5 seconds grace period
            self.patrol_mode = 'explore'  # Force explore mode
            dock._last_undocking_submarine = None  # Clear the flag

        # Don't interfere with active user control - but allow autopilot to resume after user control ends
        if mouse_controller and mouse_controller.submarine_follow_mouse:
            # User is actively controlling submarine, don't interfere unless it's an emergency
            if oxygen_level > 75:  # Only override for emergencies
                return

        # Emergency override: Force surface when oxygen is very low
        if oxygen_level <= 75:
            self.patrol_mode = 'emergency_surface'
            self._emergency_surface_behavior(submarine, dt)
            return

        # Don't interfere with emergency docking when oxygen is low
        if oxygen_level <= 100 and submarine.depth <= 5:
            return

        # Check deployment status
        crew_deployed = divers and not submarine.has_crew_aboard(divers)
        hooks_deployed = not submarine.has_grappling_hooks_aboard()

        # Check if there are available divers and collectible resources nearby
        available_divers = [d for d in divers if d.is_available()] if divers else []
        nearby_resources = []
        if resources and submarine:
            submarine_pos = submarine.get_center_pos()
            for resource in resources:
                if not resource.collected and not resource.being_collected:
                    distance = math.sqrt((resource.x - submarine_pos[0])**2 + (resource.y - submarine_pos[1])**2)
                    # Check if resource is within any diver's tether range
                    for diver in available_divers:
                        if distance <= diver.tether_range and submarine.can_store_resource(resource.type, 1):
                            nearby_resources.append(resource)
                            break

        # Enhanced crew coordination - be much more conservative about moving when divers can work
        available_divers = [d for d in divers if d.is_available()] if divers else []
        nearby_resources = []
        if resources and submarine:
            submarine_pos = submarine.get_center_pos()
            for resource in resources:
                if not resource.collected and not resource.being_collected:
                    distance = math.sqrt((resource.x - submarine_pos[0])**2 + (resource.y - submarine_pos[1])**2)
                    # Check if resource is within any diver's tether range
                    for diver in available_divers:
                        if distance <= diver.tether_range and submarine.can_store_resource(resource.type, 1):
                            nearby_resources.append(resource)
                            break

        # Don't move submarine if:
        # 1. Crew is deployed (already working)
        # 2. Available divers and nearby resources (could work)
        # 3. Submarine is docked or docking
        should_wait_for_crew = (crew_deployed or
                               (available_divers and nearby_resources) or
                               (dock and dock.is_occupied))

        if should_wait_for_crew and oxygen_level > 150:
            # Force submarine to be stationary when waiting for crew
            if submarine and not submarine.is_stationary():
                submarine.is_moving = False
                submarine.stationary_timer = max(submarine.stationary_timer, submarine.stationary_delay)
            return

        # Don't move submarine if grappling hooks are deployed - wait for all hooks to return
        # Exception: if oxygen is getting low, override grappling restrictions
        if hooks_deployed and oxygen_level > 150:
            return

        self.patrol_timer += 1

        # Update post-undocking grace timer
        if self.post_undocking_grace_timer > 0:
            self.post_undocking_grace_timer -= 1

        # Decide on behavior based on current situation and diver needs
        available_divers = [d for d in (divers or []) if d.is_available()]
        resources_in_range = self._count_resources_in_diver_range(submarine, resources, divers or [])
        collectible_resources_in_range = self._count_collectible_resources_in_range(submarine, resources, divers or [])
        uncollected_resources = [r for r in resources if not r.collected]

        # Count collectible resources (resources we can actually store)
        collectible_resources = [r for r in uncollected_resources if submarine.can_store_resource(r.type, 1)]

        # Check if ALL spawned resources cannot be gathered due to storage limits
        all_resources_blocked_by_storage = (len(uncollected_resources) > 0 and len(collectible_resources) == 0)

        # Check if there are no resources available at all
        no_resources_available = (len(uncollected_resources) == 0)

        # Check if all divers are idle (not actively working on resources)
        all_divers_idle = all(d.is_available() for d in (divers or []))

        # Check if there are divers currently working (swimming, collecting, or returning with resources)
        divers_working = any(d.state in ['swimming', 'collecting', 'returning'] for d in (divers or []))

        # Check if submarine storage is nearly full (>90%) - force movement to find new resource types
        storage_percent = submarine.get_storage_used() / submarine.get_storage_capacity() if submarine.get_storage_capacity() > 0 else 0
        storage_nearly_full = storage_percent > 0.9
        storage_full = storage_percent >= 0.95  # Consider storage full at 95%

        # Add mode stability to prevent rapid switching
        if not hasattr(self, 'mode_stability_timer'):
            self.mode_stability_timer = 0
            self.last_mode = None

        # Track mode changes and add stability delay
        if self.last_mode != self.patrol_mode:
            self.mode_stability_timer = 0
            self.last_mode = self.patrol_mode
        else:
            self.mode_stability_timer += 1

        # Only allow mode changes if we've been stable for a minimum time (except for emergencies)
        min_stability_time = 60  # 1 second minimum between mode changes
        can_change_mode = self.mode_stability_timer >= min_stability_time

        # Improved mode switching with priority hierarchy and stability
        new_mode = self.patrol_mode  # Default to current mode

        # PRIORITY 1: Emergency depth - always takes precedence
        if submarine.depth > submarine.max_depth * 0.85:  # Slightly higher threshold for more decisive action
            new_mode = 'return_surface'
        # PRIORITY 1.5: Stay in docking mode if already docking and conditions still require it
        elif (self.patrol_mode == 'docking' and
              (no_resources_available or all_resources_blocked_by_storage) and
              not divers_working and submarine.depth <= 10):
            new_mode = 'docking'  # Stay in docking mode
        # PRIORITY 2: No resources available - prioritize docking (but not during post-undocking grace period)
        elif (no_resources_available and not divers_working and can_change_mode and
              self.post_undocking_grace_timer <= 0):
            new_mode = 'dock_priority'  # Dock when no resources are available
            # Clear movement target to force surfacing
            self.movement_target = None
        # PRIORITY 3: All spawned resources blocked by storage - prioritize docking
        elif all_resources_blocked_by_storage and not divers_working and can_change_mode:
            new_mode = 'dock_priority'  # New mode for prioritizing docking
            # Clear movement target to force surfacing
            self.movement_target = None
        # PRIORITY 4: Storage management when not working
        elif storage_nearly_full and not divers_working and can_change_mode:
            new_mode = 'resource_hunt'
            # Clear movement target to force new target selection
            self.movement_target = None
        # PRIORITY 5: Diver positioning (only if not too deep)
        elif (submarine.depth <= submarine.max_depth * 0.7 and  # Only position for divers if not too deep
              len(available_divers) > 0 and collectible_resources_in_range > 0 and can_change_mode):
            # Stay in position for divers if collectible resources are in range - STOP MOVING
            new_mode = 'position_for_divers'
            # Clear movement target to ensure we stop
            self.movement_target = None
        # PRIORITY 6: Move to position for divers (only if not too deep)
        elif (submarine.depth <= submarine.max_depth * 0.7 and  # Only position for divers if not too deep
              len(available_divers) > 0 and collectible_resources_in_range == 0 and
              len(collectible_resources) >= 2 and all_divers_idle and not divers_working and can_change_mode):
            new_mode = 'position_for_divers'
        # PRIORITY 7: Force movement when in position_for_divers mode but no resources in range
        elif (self.patrol_mode == 'position_for_divers' and collectible_resources_in_range == 0 and
              all_divers_idle and not divers_working and can_change_mode):
            # Force movement to find new resources when current area is depleted
            if len(collectible_resources) >= 2:
                new_mode = 'resource_hunt'
            else:
                new_mode = 'explore'
        # PRIORITY 8: Resource hunting
        elif submarine.depth > 0 and len(collectible_resources) >= 3 and can_change_mode:
            new_mode = 'resource_hunt'
        # PRIORITY 9: Default exploration
        elif can_change_mode:
            new_mode = 'explore'

        # Apply the new mode (with stability check)
        if new_mode != self.patrol_mode and (can_change_mode or new_mode == 'return_surface'):
            self.patrol_mode = new_mode
            self.mode_stability_timer = 0  # Reset stability timer for new mode

        # Execute current behavior
        if self.patrol_mode == 'explore':
            self._explore_behavior(submarine, dt)
        elif self.patrol_mode == 'position_for_divers':
            self._position_for_divers_behavior(submarine, resources, divers or [], dt)
        elif self.patrol_mode == 'resource_hunt':
            self._resource_hunt_behavior(submarine, resources, dt)
        elif self.patrol_mode == 'return_surface':
            self._return_surface_behavior(submarine, dt)
        elif self.patrol_mode == 'dock_priority':
            self._dock_priority_behavior(submarine, dt)
        elif self.patrol_mode == 'docking':
            self._docking_behavior(submarine, dt)

        # Enhanced anti-stuck mechanism with direct resource targeting
        if hasattr(submarine, 'stationary_timer') and submarine.stationary_timer > 180:  # 3 seconds (reduced from 5)
            # Force movement to prevent getting stuck
            if self.patrol_timer % 30 == 0:  # Every 0.5 seconds when stuck
                # Clear any existing targets that might be causing the stuck state
                self.movement_target = None
                self.target_lock_timer = 0
                self.position_stability_timer = 0

                # Try to move directly toward the nearest collectible resource
                collectible_resources = [r for r in resources if not r.collected and submarine.can_store_resource(r.type, 1)]
                if collectible_resources and divers:
                    # Find nearest collectible resource
                    submarine_pos = submarine.get_center_pos()
                    nearest_resource = min(collectible_resources,
                                         key=lambda r: math.sqrt((r.x - submarine_pos[0])**2 + (r.y - submarine_pos[1])**2))

                    # Move toward it at optimal range
                    max_range = max([d.tether_range for d in divers], default=120)
                    optimal_range = max_range * 0.7  # 70% of max range

                    # Calculate direction to resource
                    dx = nearest_resource.x - submarine_pos[0]
                    dy = nearest_resource.y - submarine_pos[1]
                    distance = math.sqrt(dx**2 + dy**2)

                    if distance > optimal_range:
                        # Move closer to optimal range
                        target_distance = optimal_range
                        target_x = submarine_pos[0] + (dx / distance) * target_distance
                        target_y = submarine_pos[1] + (dy / distance) * target_distance
                        self.movement_target = (target_x, target_y)
                    else:
                        # We're close enough, force explore mode to get moving
                        self.patrol_mode = 'explore'
                        submarine.x += random.randint(-30, 30)
                        submarine.x = max(50, min(WORLD_WIDTH - submarine.width - 50, submarine.x))
            
    def _explore_behavior(self, submarine, dt):
        """Slowly dive and explore new areas."""
        # First, move away from dock area if we're too close at surface
        if submarine.depth <= 5:  # At or near surface
            dock_center_x = WORLD_WIDTH // 2 - 100 + 100  # Dock center in world coordinates
            distance_to_dock = abs(submarine.x - dock_center_x)

            if distance_to_dock < 200:  # Increased distance to avoid dock area
                # Move away from dock horizontally first with full speed
                move_speed = submarine.speed * 1.0  # Full speed for responsive movement
                if submarine.x < dock_center_x:
                    # Move left
                    submarine.x = max(50, submarine.x - move_speed)
                else:
                    # Move right
                    submarine.x = min(WORLD_WIDTH - submarine.width - 50, submarine.x + move_speed)
                return  # Don't dive yet, just move away from dock

        if self.patrol_timer >= self.patrol_interval:  # Use full interval, not half
            self.patrol_timer = 0
            # Less aggressive exploration movement
            if random.random() < 0.7:  # 70% chance to dive deeper (prefer vertical movement)
                self.target_depth = min(submarine.max_depth * 0.8, submarine.depth + random.randint(20, 50))
            else:  # 30% chance to move horizontally - smaller movements
                if not self.movement_target:  # Only set new target if we don't have one
                    target_x = submarine.x + random.randint(-150, 150)  # Reduced range
                    target_x = max(50, min(WORLD_WIDTH - submarine.width - 50, target_x))
                    self.movement_target = (target_x, submarine.y)

        # Handle smooth horizontal movement if we have a target
        if self.movement_target:
            target_x, target_y = self.movement_target
            dx = target_x - submarine.x
            distance = abs(dx)

            if distance > 5:  # Move towards target smoothly
                move_speed = submarine.speed * 0.8
                if dx > 0:
                    submarine.x += min(move_speed, dx)
                else:
                    submarine.x += max(-move_speed, dx)
            else:
                # Reached horizontal target
                self.movement_target = None

        # Move towards target depth
        if submarine.depth < self.target_depth:
            submarine.y += submarine.speed * 0.8  # Increased speed for diving
            submarine.depth = max(0, submarine.y - SURFACE_LEVEL)
            
    def _resource_hunt_behavior(self, submarine, resources, dt):
        """Hunt for resources at optimal depths, prioritizing collectible resource types."""
        if self.patrol_timer >= self.patrol_interval // 2:
            self.patrol_timer = 0
            # Find optimal depth for collectible resources
            if resources:
                # Filter for resources that can actually be stored AND are within reachable depth
                collectible_resources = [r for r in resources
                                       if not r.collected and
                                       submarine.can_store_resource(r.type, 1) and
                                       (r.y - SURFACE_LEVEL) <= submarine.max_depth * 0.9]  # Only consider resources within 90% of max depth

                if collectible_resources:
                    # Prioritize resources we can actually collect and reach
                    avg_resource_depth = sum(r.y for r in collectible_resources) / len(collectible_resources)
                    target_depth = avg_resource_depth - SURFACE_LEVEL
                    # Ensure target depth is within submarine's capability
                    self.target_depth = min(target_depth, submarine.max_depth * 0.8)  # Stay within 80% of max depth for safety
                else:
                    # If no collectible resources within reach, look for different resource types at reachable depths
                    uncollected = [r for r in resources if not r.collected and
                                 (r.y - SURFACE_LEVEL) <= submarine.max_depth * 0.9]
                    if uncollected:
                        # Try different depths to find different resource types, but stay within limits
                        current_depth_resources = [r for r in uncollected
                                                 if abs(r.y - submarine.y) < 100]
                        if current_depth_resources:
                            # Move to a different depth to find different resources, but respect max depth
                            if submarine.depth < min(200, submarine.max_depth * 0.6):
                                self.target_depth = min(submarine.depth + 100, submarine.max_depth * 0.8)  # Go deeper but stay safe
                            else:
                                self.target_depth = max(50, submarine.depth - 100)  # Go shallower
                        else:
                            avg_resource_depth = sum(r.y for r in uncollected) / len(uncollected)
                            target_depth = avg_resource_depth - SURFACE_LEVEL
                            # Ensure target depth is within submarine's capability
                            self.target_depth = min(target_depth, submarine.max_depth * 0.8)
                    else:
                        # No reachable resources, stay at a safe depth
                        self.target_depth = min(submarine.max_depth * 0.5, 200)  # Go to middle depth range

        # Move towards resource-rich depth, but respect maximum depth limits
        depth_diff = self.target_depth - submarine.depth
        if abs(depth_diff) > 10:
            move_speed = submarine.speed * 0.8  # Increased speed for resource hunting
            if depth_diff > 0:
                # Going deeper - check if we're approaching max depth
                if submarine.depth < submarine.max_depth * 0.8:  # Only go deeper if we're not near max depth
                    submarine.y += move_speed
                # If we're near max depth, don't go deeper
            else:
                submarine.y -= move_speed
            submarine.depth = max(0, submarine.y - SURFACE_LEVEL)
            
    def _return_surface_behavior(self, submarine, dt):
        """Return to surface when needed."""
        if submarine.depth > 10:
            submarine.y -= submarine.speed * 0.8
            submarine.depth = max(0, submarine.y - SURFACE_LEVEL)
        # Don't automatically switch to explore - let main mode logic decide

    def _emergency_surface_behavior(self, submarine, dt):
        """Emergency surfacing when oxygen is critically low."""
        if submarine.depth > 0:
            # Fast emergency surfacing
            emergency_speed = submarine.speed * 1.5
            submarine.y = max(SURFACE_LEVEL, submarine.y - emergency_speed)
            submarine.depth = max(0, submarine.y - SURFACE_LEVEL)

            # Force to surface if very close
            if submarine.depth <= 3:
                submarine.y = SURFACE_LEVEL
                submarine.depth = 0
                self.patrol_mode = 'explore'  # Resume normal behavior once surfaced

    def _dock_priority_behavior(self, submarine, dt):
        """Priority surfacing and docking when all spawned resources are blocked by storage limits."""
        # Fast surfacing to get to dock quickly
        if submarine.depth > 5:
            # Surface quickly but not as fast as emergency
            surface_speed = submarine.speed * 1.2
            submarine.y = max(SURFACE_LEVEL, submarine.y - surface_speed)
            submarine.depth = max(0, submarine.y - SURFACE_LEVEL)
        else:
            # At surface, switch to docking mode to stay near dock
            self.patrol_mode = 'docking'

    def _docking_behavior(self, submarine, dt):
        """Stay at surface near dock and wait for docking to occur."""
        # Keep submarine at surface
        if submarine.depth > 5:
            submarine.y = max(SURFACE_LEVEL, submarine.y - submarine.speed)
            submarine.depth = max(0, submarine.y - SURFACE_LEVEL)

        # Move towards dock center if not close enough
        dock_center_x = WORLD_WIDTH // 2 - 100 + 100  # Dock center in world coordinates
        distance_to_dock = abs(submarine.x - dock_center_x)

        if distance_to_dock > 50:  # Move closer to dock
            move_speed = submarine.speed * 0.8
            if submarine.x < dock_center_x:
                submarine.x += move_speed
            else:
                submarine.x -= move_speed

        # Stay in docking mode until storage is no longer full or submarine gets docked
        # The mode will be changed by the main update logic when conditions change

    def _position_for_divers_behavior(self, submarine, resources, divers, dt):
        """Position submarine to maximize resources within diver tether range."""
        # Don't move if any divers are currently working on resources
        divers_working = any(d.state in ['swimming', 'collecting', 'returning'] for d in divers)
        if divers_working:
            return  # Wait for all divers to finish their current tasks

        # Check if we're already in optimal range of resources that we can actually collect
        current_resources_in_range = self._count_collectible_resources_in_range(submarine, resources, divers)

        # Check if there are resources in range but we can't collect them due to storage
        uncollectable_resources_in_range = self._count_uncollectable_resources_in_range(submarine, resources, divers)

        # If we have collectible resources in range, stay put
        if current_resources_in_range > 0:
            # We have collectible resources in range, stay put and let divers work
            self.movement_target = None  # Clear any movement target to stop moving
            return

        # If we have uncollectable resources in range but no collectible ones, force repositioning
        elif uncollectable_resources_in_range > 0:
            # Force immediate repositioning to find collectible resources
            self.movement_target = None  # Clear current target to force new search
            self.patrol_timer = self.patrol_interval  # Force immediate repositioning
            self.position_stability_timer = 60  # Reset stability timer

        # Increment stability timer
        self.position_stability_timer += 1

        # Update target lock timer
        if self.movement_target:
            self.target_lock_timer += 1
        else:
            self.target_lock_timer = 0

        # Check if there are no collectible resources in range for faster repositioning
        current_resources_in_range = self._count_collectible_resources_in_range(submarine, resources, divers)
        no_resources_nearby = current_resources_in_range == 0

        # Much faster timing for better responsiveness
        required_patrol_time = self.patrol_interval // 3 if no_resources_nearby else self.patrol_interval
        required_stability_time = 30 if no_resources_nearby else 60  # 0.5 second vs 1 second

        # Only consider repositioning if we've been stable for a while and not moving
        # Also prevent rapid target switching by requiring minimum lock time
        can_change_target = (not self.movement_target or self.target_lock_timer > 120)  # 2 seconds minimum lock

        if (self.patrol_timer >= required_patrol_time and  # Much faster when no resources nearby
            self.position_stability_timer >= required_stability_time and  # Much faster timing
            can_change_target):  # Not currently moving or lock timer expired

            self.patrol_timer = 0

            # Find the best position for submarine to maximize accessible resources
            best_position = self._find_optimal_submarine_position(submarine, resources, divers)
            if best_position:
                # Only move if the improvement is significant
                current_score = self._score_submarine_position(submarine.get_center_pos(),
                                                             [r for r in resources if not r.collected],
                                                             [d for d in divers if d.is_available()],
                                                             submarine)
                new_score = self._score_submarine_position(best_position,
                                                         [r for r in resources if not r.collected],
                                                         [d for d in divers if d.is_available()],
                                                         submarine)

                # Only move if there's a significant improvement AND we're not already close to a good position
                improvement_threshold = 1.3 if no_resources_nearby else 1.5  # Higher threshold to prevent oscillation
                score_improvement = new_score - self.last_position_score

                # Check if we're already in a decent position
                current_position_good = current_score > 10  # Arbitrary threshold for "good enough"

                # Only move if improvement is significant AND (we're in a bad position OR no resources nearby)
                if (new_score > current_score * improvement_threshold and
                    (not current_position_good or no_resources_nearby)):
                    self.movement_target = best_position
                    self.last_position_score = current_score
                    self.position_stability_timer = 0  # Reset stability timer
                    self.target_lock_timer = 0  # Reset target lock timer for new target

        # Move towards optimal position (only if no divers are working)
        if self.movement_target and not divers_working:
            submarine_center = submarine.get_center_pos()
            dx = self.movement_target[0] - submarine_center[0]
            dy = self.movement_target[1] - submarine_center[1]
            distance = math.sqrt(dx**2 + dy**2)

            # Only stop for resources if we're very close to target OR if we have significantly more resources than expected
            current_resources_in_range = self._count_collectible_resources_in_range(submarine, resources, divers or [])
            close_to_target = distance <= 15  # Much closer to target before considering stopping

            if current_resources_in_range > 0 and close_to_target:
                # We're close to target and have collectible resources, stop moving
                self.movement_target = None
                self.target_lock_timer = 0
                self.position_stability_timer = 0
                return

            if distance > 12:  # Much smaller tolerance for more precise positioning
                move_speed = submarine.speed * 1.0  # Full speed for responsive movement
                submarine.x += (dx / distance) * move_speed
                submarine.y += (dy / distance) * move_speed
                submarine.depth = max(0, submarine.y - SURFACE_LEVEL)
            else:
                self.movement_target = None  # Reached target
                self.target_lock_timer = 0  # Reset target lock timer
                # Add a brief pause after reaching target to prevent immediate repositioning
                self.patrol_timer = -60  # Reset timer to add 1 second pause
                self.position_stability_timer = 0  # Reset stability timer after reaching target

    def _count_resources_in_diver_range(self, submarine, resources, divers):
        """Count how many resources are within range of available divers and grappling hooks."""
        if not divers:
            return 0

        submarine_pos = submarine.get_center_pos()
        max_tether_range = max([d.tether_range for d in divers], default=120)

        # Also consider grappling hook range if available
        grappling_range = 0
        if hasattr(submarine, 'grappling_system') and submarine.grappling_system:
            if hasattr(submarine.grappling_system, 'hooks') and submarine.grappling_system.hooks:
                grappling_range = submarine.grappling_system.hooks[0].max_range if submarine.grappling_system.hooks else 0

        # Use the maximum of diver range and grappling range for optimal positioning
        # Use 85% of max range to ensure resources are comfortably within reach
        effective_range = max(max_tether_range, grappling_range) * 0.85

        count = 0
        for resource in resources:
            if not resource.collected:
                distance = math.sqrt((resource.x - submarine_pos[0])**2 +
                                   (resource.y - submarine_pos[1])**2)
                if distance <= effective_range:
                    count += 1
        return count

    def _count_collectible_resources_in_range(self, submarine, resources, divers):
        """Count how many resources are within range AND can be stored by the submarine AND are reachable."""
        if not divers:
            return 0

        submarine_pos = submarine.get_center_pos()
        max_tether_range = max([d.tether_range for d in divers], default=120)

        # Also consider grappling hook range if available
        grappling_range = 0
        if hasattr(submarine, 'grappling_system') and submarine.grappling_system:
            if hasattr(submarine.grappling_system, 'hooks') and submarine.grappling_system.hooks:
                grappling_range = submarine.grappling_system.hooks[0].max_range if submarine.grappling_system.hooks else 0

        # Use the maximum of diver range and grappling range for optimal positioning
        # Use 85% of max range to ensure resources are comfortably within reach
        effective_range = max(max_tether_range, grappling_range) * 0.85

        count = 0
        for resource in resources:
            if not resource.collected:
                # Check if submarine can store this resource type AND resource is within reachable depth
                resource_depth = resource.y - SURFACE_LEVEL
                if (submarine.can_store_resource(resource.type, 1) and
                    resource_depth <= submarine.max_depth * 0.9):  # Only count reachable resources
                    distance = math.sqrt((resource.x - submarine_pos[0])**2 +
                                       (resource.y - submarine_pos[1])**2)
                    if distance <= effective_range:
                        count += 1
        return count

    def _count_uncollectable_resources_in_range(self, submarine, resources, divers):
        """Count resources within range that CANNOT be collected due to storage limits."""
        if not divers:
            return 0

        submarine_pos = submarine.get_center_pos()
        max_tether_range = max([d.tether_range for d in divers], default=120)

        # Also consider grappling hook range if available
        grappling_range = 0
        if hasattr(submarine, 'grappling_system') and submarine.grappling_system:
            if hasattr(submarine.grappling_system, 'hooks') and submarine.grappling_system.hooks:
                grappling_range = submarine.grappling_system.hooks[0].max_range if submarine.grappling_system.hooks else 0

        # Use the maximum of diver range and grappling range for optimal positioning
        effective_range = max(max_tether_range, grappling_range) * 0.85

        count = 0
        for resource in resources:
            if not resource.collected:
                # Check if resource is within reachable depth but submarine CANNOT store this resource type
                resource_depth = resource.y - SURFACE_LEVEL
                if (not submarine.can_store_resource(resource.type, 1) and
                    resource_depth <= submarine.max_depth * 0.9):  # Only count reachable resources
                    distance = math.sqrt((resource.x - submarine_pos[0])**2 +
                                       (resource.y - submarine_pos[1])**2)
                    if distance <= effective_range:
                        count += 1
        return count

    def _find_optimal_submarine_position(self, submarine, resources, divers):
        """Find the submarine position that maximizes accessible resources for divers."""
        if not divers or not resources:
            return None

        # Filter for resources that are within submarine's depth capability AND can be stored
        collectible_resources = [r for r in resources if not r.collected and
                               (r.y - SURFACE_LEVEL) <= submarine.max_depth * 0.9 and
                               submarine.can_store_resource(r.type, 1)]  # Only consider reachable AND storable resources
        if not collectible_resources:
            return None

        available_divers = [d for d in divers if d.is_available()]
        if not available_divers:
            return None

        max_tether_range = max([d.tether_range for d in available_divers])

        # Calculate maximum depth the submarine should position at
        max_submarine_depth = submarine.max_depth * 0.8  # Stay within 80% of max depth for safety
        max_submarine_y = SURFACE_LEVEL + max_submarine_depth

        # Try different positions around resource clusters
        best_position = None
        best_score = 0

        # Find resource clusters and sample positions around them
        resource_clusters = self._find_resource_clusters(collectible_resources, max_tether_range)

        # If we have a current target, prefer positions near it to reduce oscillation
        if self.movement_target:
            resource_clusters.insert(0, self.movement_target)  # Prioritize current target area

        # Sample positions around resource clusters - get closer to resources
        for cluster_center in resource_clusters[:2]:  # Check only top 2 clusters to reduce oscillation
            # Try positions closer to resources for better positioning
            for offset_x in [-max_tether_range * 0.3, 0, max_tether_range * 0.3]:  # Include center position
                for offset_y in [-max_tether_range * 0.3, 0, max_tether_range * 0.3]:  # Include center position
                    test_pos = (cluster_center[0] + offset_x, cluster_center[1] + offset_y)

                    # Keep submarine within world bounds and respect depth limits
                    test_pos = (
                        max(50, min(WORLD_WIDTH - 50, test_pos[0])),
                        max(SURFACE_LEVEL, min(max_submarine_y, test_pos[1]))  # Respect submarine's max depth capability
                    )

                    # Only consider positions that are within submarine's depth capability
                    test_depth = test_pos[1] - SURFACE_LEVEL
                    if test_depth <= submarine.max_depth * 0.8:  # Ensure position is reachable
                        score = self._score_submarine_position(test_pos, collectible_resources, available_divers, submarine)
                        if score > best_score:
                            best_score = score
                            best_position = test_pos

        return best_position

    def _find_resource_clusters(self, resources, max_range):
        """Find clusters of resources to optimize positioning."""
        if not resources:
            return []

        clusters = []
        cluster_radius = max_range * 0.8  # Resources within this distance are considered a cluster

        for resource in resources:
            # Check if this resource belongs to an existing cluster
            added_to_cluster = False
            for i, cluster_center in enumerate(clusters):
                distance = math.sqrt((resource.x - cluster_center[0])**2 + (resource.y - cluster_center[1])**2)
                if distance <= cluster_radius:
                    # Update cluster center to average position
                    cluster_weight = getattr(clusters, '_weights', [1] * len(clusters))[i] if hasattr(clusters, '_weights') else 1
                    new_weight = cluster_weight + 1
                    new_x = (cluster_center[0] * cluster_weight + resource.x) / new_weight
                    new_y = (cluster_center[1] * cluster_weight + resource.y) / new_weight
                    clusters[i] = (new_x, new_y)
                    added_to_cluster = True
                    break

            if not added_to_cluster:
                # Create new cluster
                clusters.append((resource.x, resource.y))

        return clusters

    def _score_submarine_position(self, position, resources, divers, submarine=None):
        """Score a submarine position based on accessible resources with stability bias and storage capacity."""
        if not divers:
            return 0

        max_tether_range = max([d.tether_range for d in divers])
        # Use 90% of tether range to ensure resources are within reach but allow closer positioning
        effective_range = max_tether_range * 0.9
        accessible_resources = 0
        resource_count = 0
        collectible_count = 0

        # Calculate the depth of the test position
        position_depth = position[1] - SURFACE_LEVEL

        # Only consider this position if it's within submarine's depth capability
        if submarine and position_depth > submarine.max_depth * 0.8:
            return 0  # Position is too deep for submarine

        for resource in resources:
            # Only consider resources that are within submarine's depth capability
            resource_depth = resource.y - SURFACE_LEVEL
            if submarine and resource_depth > submarine.max_depth * 0.9:
                continue  # Skip resources that are too deep

            distance = math.sqrt((resource.x - position[0])**2 + (resource.y - position[1])**2)

            # Penalize positions that are too close to resources (avoid collision)
            if distance < 25:  # Reduced minimum distance for closer positioning
                accessible_resources -= 5  # Reduced penalty for being close
                continue

            if distance <= effective_range:
                resource_count += 1

                # Check if this resource can actually be stored (if submarine provided)
                can_store = True
                if submarine:
                    can_store = submarine.can_store_resource(resource.type, 1)
                    if can_store:
                        collectible_count += 1

                # Weight by resource value and prefer closer positions within range
                resource_value = RESOURCE_TYPES.get(resource.type, {}).get('value', 1)

                # Bonus for collectible resources
                if can_store:
                    resource_value *= 2.0  # Double value for resources we can actually collect
                else:
                    resource_value *= 0.1  # Very low value for resources we can't store

                # Optimal distance is around 30-50% of tether range (closer than before)
                optimal_distance = effective_range * 0.4
                distance_factor = 1.0 - abs(distance - optimal_distance) / effective_range
                accessible_resources += resource_value * max(0.7, distance_factor)  # Higher minimum factor

        # Bonus for resource density - prefer positions with multiple resources
        if resource_count > 1:
            density_bonus = resource_count * 5  # Bonus for each additional resource
            accessible_resources += density_bonus

        # Extra bonus for collectible resources
        if collectible_count > 0:
            collectible_bonus = collectible_count * 15  # Strong bonus for collectible resources
            accessible_resources += collectible_bonus

        # Stability bonus - prefer staying near current position if it's already good
        if hasattr(self, 'movement_target') and self.movement_target:
            current_target_distance = math.sqrt((position[0] - self.movement_target[0])**2 +
                                              (position[1] - self.movement_target[1])**2)
            if current_target_distance < 50:  # If close to current target
                accessible_resources += 20  # Stability bonus

        # Extra bonus for staying in current position if it has collectible resources
        # This helps prevent the submarine from leaving good areas
        if collectible_count > 0:
            accessible_resources += collectible_count * 20  # Very strong bonus for staying near collectible resources
        elif resource_count > 0:
            accessible_resources += resource_count * 5  # Smaller bonus for non-collectible resources

        return accessible_resources

    def get_status(self, submarine=None, divers=None):
        """Get current autopilot status for UI display."""
        if not self.enabled:
            return "Disabled"

        # Check if waiting for crew
        if submarine and divers and not submarine.has_crew_aboard(divers):
            return "Waiting for Crew"

        # Check if waiting for grappling hooks
        if submarine and not submarine.has_grappling_hooks_aboard():
            return "Waiting for Hooks"

        # Check if moving to target
        if self.movement_target:
            return "Moving to Target"

        status_map = {
            'explore': "Exploring",
            'position_for_divers': "Positioning for Divers",
            'resource_hunt': "Hunting Resources",
            'return_surface': "Returning to Surface",
            'emergency_surface': "Emergency Surface"
        }

        return status_map.get(self.patrol_mode, f"Mode: {self.patrol_mode}")

    def toggle(self):
        """Toggle autopilot on/off."""
        self.enabled = not self.enabled

class CrewAI:
    """AI system for managing diver crew automatically."""
    
    def __init__(self):
        self.enabled = True
        self.assignment_timer = 0
        self.assignment_interval = 30  # 0.5 seconds (faster for better multiple trip handling)
        self.resource_priorities = ['treasure', 'pearl', 'coral', 'kelp']
        
    def update(self, divers, resources, submarine_pos, dt, submarine=None):
        """Update crew AI behavior."""
        if not self.enabled:
            return

        # Emergency recall check - force divers to return if in danger
        if submarine:
            self._check_emergency_recall(divers, submarine_pos, submarine)

        # Don't deploy divers if submarine is moving or hasn't been stationary long enough
        if submarine and not submarine.is_stationary():
            return

        self.assignment_timer += 1

        if self.assignment_timer >= self.assignment_interval:
            self.assignment_timer = 0

            self._assign_divers(divers, resources, submarine_pos, submarine)
            
    def _assign_divers(self, divers, resources, submarine_pos, submarine=None):
        """Automatically assign divers to collect resources."""
        # Don't deploy divers if submarine is docked
        if submarine and submarine.is_docked:
            return

        available_divers = [d for d in divers if d.is_available()]
        # Include partially collected resources that are not currently being worked on
        # This allows multiple trips to the same resource
        uncollected_resources = [r for r in resources if not r.collected]

        # Filter out resources that are actively being collected by someone else
        available_resources = []
        for resource in uncollected_resources:
            # Check if any diver is currently working on this resource
            being_worked_by_diver = any(
                d.target_resource == resource and d.state in ['swimming', 'collecting']
                for d in divers
            )
            # Check if any grappling hook is currently working on this resource
            being_worked_by_hook = False
            if submarine and hasattr(submarine, 'grappling_system'):
                being_worked_by_hook = any(
                    hook.target_resource == resource and hook.state in ['extending', 'collecting']
                    for hook in submarine.grappling_system.hooks
                )
            if submarine and hasattr(submarine, 'player_hook') and submarine.player_hook.hook:
                if (submarine.player_hook.hook.target_resource == resource and
                    submarine.player_hook.hook.state in ['extending', 'collecting']):
                    being_worked_by_hook = True

            # Only include resources that are not currently being worked on
            if not being_worked_by_diver and not being_worked_by_hook:
                available_resources.append(resource)

        uncollected_resources = available_resources

        if not available_divers or not uncollected_resources:
            return

        # Get list of resources that grappling hooks are currently targeting
        grappling_targeted_resources = set()
        if submarine and hasattr(submarine, 'get_grappling_hook_targeted_resources'):
            grappling_targeted_resources = submarine.get_grappling_hook_targeted_resources()

        # Filter out resources that submarine cannot store or that grappling hooks are targeting
        collectible_resources = []
        for resource in uncollected_resources:
            # Skip resources that grappling hooks are already working on
            if resource in grappling_targeted_resources:
                continue

            if submarine and submarine.can_store_resource(resource.type, 1):
                collectible_resources.append(resource)

        if not collectible_resources:
            return  # No resources that can be collected

        # Sort resources by priority and distance
        def resource_score(resource):
            priority_score = len(self.resource_priorities) - self.resource_priorities.index(resource.type) if resource.type in self.resource_priorities else 0
            distance = math.sqrt((resource.x - submarine_pos[0])**2 + (resource.y - submarine_pos[1])**2)
            # Add resource value to scoring to prioritize larger resources
            value_score = resource.current_value * 0.1
            return priority_score * 100 - distance + value_score

        sorted_resources = sorted(collectible_resources, key=resource_score, reverse=True)

        # Assign ALL available divers to resources (multiple divers can work on different resources)
        for diver in available_divers:
            # Find the best available resource within this diver's tether range
            for resource in sorted_resources:
                # Check if resource is not being collected by another diver
                resource_being_worked = any(
                    other_diver.target_resource == resource and other_diver.state in ['swimming', 'collecting']
                    for other_diver in divers if other_diver != diver
                )

                if not resource_being_worked and diver.send_to_resource(resource.get_position(), resource.type, submarine_pos, resource, submarine):
                    # Successfully assigned diver to this resource
                    break

    def _check_emergency_recall(self, divers, submarine_pos, submarine):
        """Check for emergency situations and recall divers if needed."""
        for diver in divers:
            if diver.state in ['swimming', 'collecting', 'returning']:
                # Check distance from submarine
                distance = math.sqrt((diver.x - submarine_pos[0])**2 + (diver.y - submarine_pos[1])**2)

                # Emergency conditions
                emergency_recall = False

                # Too far from submarine
                if distance > diver.tether_range * 1.3:
                    emergency_recall = True

                # Critical oxygen level
                if diver.oxygen <= 15:
                    emergency_recall = True

                # Force return if emergency
                if emergency_recall and diver.state != 'returning':
                    diver.state = 'returning'
                    diver.target_x, diver.target_y = submarine_pos

    def toggle(self):
        """Toggle crew AI on/off."""
        self.enabled = not self.enabled

class AutoCrafter:
    """Automatic crafting system."""
    
    def __init__(self):
        self.enabled = True
        self.craft_timer = 0
        self.craft_interval = 180  # 3 seconds
        self.craft_priorities = [
            ('oxygen_tank', lambda res: res['oxygen'] < 500 and res['kelp'] >= 5),
            ('depth_upgrade', lambda res: res['coral'] >= 3 and res['pearl'] >= 1),
            ('speed_upgrade', lambda res: res['kelp'] >= 10 and res['treasure'] >= 1)
        ]
        
    def update(self, resources, submarine, dt):
        """Update auto-crafting behavior."""
        if not self.enabled:
            return
            
        self.craft_timer += 1
        
        if self.craft_timer >= self.craft_interval:
            self.craft_timer = 0
            self._try_auto_craft(resources, submarine)
            
    def _try_auto_craft(self, resources, submarine):
        """Try to automatically craft items."""
        for craft_type, condition in self.craft_priorities:
            if condition(resources):
                if craft_type == 'oxygen_tank' and resources['kelp'] >= 5:
                    resources['kelp'] -= 5
                    resources['oxygen'] += 100
                    return True
                elif craft_type == 'depth_upgrade' and resources['coral'] >= 3 and resources['pearl'] >= 1:
                    resources['coral'] -= 3
                    resources['pearl'] -= 1
                    submarine.upgrade_depth()
                    return True
                elif craft_type == 'speed_upgrade' and resources['kelp'] >= 10 and resources['treasure'] >= 1:
                    resources['kelp'] -= 10
                    resources['treasure'] -= 1
                    submarine.upgrade_speed()
                    return True
        return False
        
    def toggle(self):
        """Toggle auto-crafting on/off."""
        self.enabled = not self.enabled

class MouseController:
    """Mouse-only control system."""

    def __init__(self):
        self.submarine_follow_mouse = False
        self.mouse_target = None
        self.click_zones = {}
        self._setup_click_zones()

        # Auto-resume autonomous movement
        self.user_control_timeout = 180  # 3 seconds (60 FPS * 3) - reduced for faster response
        self.user_control_timer = 0
        self.was_user_controlled = False
        self.resource_check_timer = 0
        self.resource_check_interval = 60  # Check every second
        
    def _setup_click_zones(self):
        """Setup clickable zones for different actions."""
        self.click_zones = {
            'autopilot_toggle': pygame.Rect(SCREEN_WIDTH - 100, 10, 90, 30),
            'crew_ai_toggle': pygame.Rect(SCREEN_WIDTH - 100, 50, 90, 30),
            'auto_craft_toggle': pygame.Rect(SCREEN_WIDTH - 100, 90, 90, 30),
            'submarine_follow': pygame.Rect(10, SCREEN_HEIGHT - 50, 150, 40)
        }
        
    def handle_click(self, pos, submarine, divers, resource_manager, automation_systems):
        """Handle mouse clicks for various actions."""
        # Check click zones first
        for zone_name, rect in self.click_zones.items():
            if rect.collidepoint(pos):
                return self._handle_zone_click(zone_name, automation_systems)

        # Check for resource clicks - deploy player grappling hook
        nearest_resource = resource_manager.get_nearest_resource(pos)
        if nearest_resource:
            distance = math.sqrt((nearest_resource.x - pos[0])**2 + (nearest_resource.y - pos[1])**2)
            if distance <= 50:  # Click tolerance
                # Deploy player grappling hook to resource
                if submarine.deploy_player_hook_to_resource(nearest_resource):
                    return True

        # If no resource nearby, try to deploy hook to clicked position
        submarine_center = submarine.get_center_pos()
        click_distance = math.sqrt((pos[0] - submarine_center[0])**2 + (pos[1] - submarine_center[1])**2)
        if click_distance <= submarine.player_hook.max_range:
            if submarine.deploy_player_hook_to_position(pos[0], pos[1], resource_manager.resources):
                return True

        # Submarine movement (if enabled)
        if self.submarine_follow_mouse:
            self.mouse_target = pos
            return True

        return False
        
    def _handle_zone_click(self, zone_name, automation_systems):
        """Handle clicks on UI zones."""
        autopilot, crew_ai, auto_crafter = automation_systems
        
        if zone_name == 'autopilot_toggle':
            autopilot.toggle()
            return True
        elif zone_name == 'crew_ai_toggle':
            crew_ai.toggle()
            return True
        elif zone_name == 'auto_craft_toggle':
            auto_crafter.toggle()
            return True
        elif zone_name == 'submarine_follow':
            self.submarine_follow_mouse = not self.submarine_follow_mouse
            return True
            
        return False
        
    def update_submarine(self, submarine, dt, resources=None, autopilot=None, divers=None, dock=None):
        """Update submarine position based on autonomous movement."""
        # Don't interfere if submarine is being smoothly undocked
        if dock and dock.undocking_submarine == submarine:
            return

        # Update timers
        self.resource_check_timer += 1

        # Improved movement blocking logic to prevent getting stuck
        crew_deployed = divers and not submarine.has_crew_aboard(divers)
        hooks_deployed = not submarine.has_grappling_hooks_aboard()

        # More lenient movement blocking - only block if both systems are active AND submarine is stationary
        # This prevents the submarine from getting permanently stuck
        if crew_deployed and hooks_deployed and submarine.is_stationary():
            # Allow emergency movement if oxygen is low or submarine has been stuck too long
            if hasattr(submarine, 'stuck_timer'):
                submarine.stuck_timer += 1
            else:
                submarine.stuck_timer = 0

            # If stuck for more than 5 seconds, allow movement anyway
            if submarine.stuck_timer > 300:  # 5 seconds at 60 FPS
                submarine.stuck_timer = 0
                # Force one system to return to allow movement
                if crew_deployed and divers:
                    for diver in divers:
                        if diver.state != 'idle':
                            diver.state = 'returning'
                            diver.target_x, diver.target_y = submarine.get_center_pos()
                            break
            else:
                return
        else:
            # Reset stuck timer when not blocked
            if hasattr(submarine, 'stuck_timer'):
                submarine.stuck_timer = 0

        if self.submarine_follow_mouse and self.mouse_target:
            # User is controlling submarine
            self.was_user_controlled = True
            # Don't increment timer while actively moving - reset it instead
            self.user_control_timer = 0

            target_x, target_y = self.mouse_target

            # Move submarine towards target with smooth autonomous movement
            dx = target_x - submarine.x
            dy = target_y - submarine.y
            distance = math.sqrt(dx**2 + dy**2)

            if distance > 15:  # Reduced threshold for more precise positioning
                # Smoother, more autonomous movement
                move_speed = submarine.speed * 1.2  # Increased speed for more responsive movement
                submarine.x += (dx / distance) * move_speed
                submarine.y += (dy / distance) * move_speed

                # Update depth based on y position
                submarine.depth = max(0, submarine.y - SURFACE_LEVEL)

                # Keep submarine within world bounds
                submarine.x = max(0, min(WORLD_WIDTH - submarine.width, submarine.x))
                submarine.y = max(SURFACE_LEVEL, min(WORLD_HEIGHT - submarine.height, submarine.y))
            else:
                # Reached target, stop following mouse but don't reset timer
                self.submarine_follow_mouse = False
                self.mouse_target = None
                # Keep timer running to count idle time after reaching target

        # Check if we should resume autonomous movement
        if self.was_user_controlled and not self.submarine_follow_mouse:
            # Increment timer while waiting for auto-resume
            self.user_control_timer += 1
            should_resume_auto = False

            # Resume after timeout
            if self.user_control_timer >= self.user_control_timeout:
                should_resume_auto = True

            # Resume if no resources in range and no divers working (check periodically)
            elif self.resource_check_timer >= self.resource_check_interval and resources:
                should_resume_auto = self._check_no_resources_in_range(submarine, resources, divers)
                self.resource_check_timer = 0

            if should_resume_auto:
                self._resume_autonomous_movement(autopilot)

    def _check_no_resources_in_range(self, submarine, resources, divers=None):
        """Check if there are no uncollected resources within reasonable range and no divers working."""
        if not resources:
            return True

        # Check if any divers are currently working on resources
        if divers:
            divers_working = any(d.state in ['swimming', 'collecting', 'returning'] for d in divers)
            if divers_working:
                return False  # Don't resume auto movement while divers are working

        submarine_center = submarine.get_center_pos()

        # Use actual diver tether range instead of fixed value
        if divers:
            max_range = max([d.tether_range for d in divers], default=120)
        else:
            max_range = 120  # Default if no divers

        # Add small buffer to account for submarine positioning - use 90% of tether range
        # to ensure divers can actually reach resources when submarine stops
        max_range *= 0.9

        for resource in resources:
            if not resource.collected:
                distance = math.sqrt((resource.x - submarine_center[0])**2 +
                                   (resource.y - submarine_center[1])**2)
                if distance <= max_range:
                    return False  # Found resource in range

        return True  # No resources in range and no divers working

    def _resume_autonomous_movement(self, autopilot):
        """Resume autonomous submarine movement."""
        self.was_user_controlled = False
        self.user_control_timer = 0
        self.resource_check_timer = 0

        # Re-enable autopilot if it was disabled and clear any conflicting targets
        if autopilot:
            if not autopilot.enabled:
                autopilot.enabled = True
            # Clear any movement targets to prevent returning to old locations
            autopilot.movement_target = None
            autopilot.target_lock_timer = 0  # Reset target lock
            autopilot.position_stability_timer = 0  # Reset stability timer
            autopilot.patrol_timer = 0  # Reset patrol timer to immediately evaluate new position

    def set_submarine_target(self, target_pos, autopilot=None):
        """Set submarine target and temporarily disable autopilot."""
        self.submarine_follow_mouse = True
        self.mouse_target = target_pos
        self.user_control_timer = 0
        self.was_user_controlled = True

        # Temporarily disable autopilot when user takes control and clear its targets
        if autopilot and autopilot.enabled:
            autopilot.enabled = False
            autopilot.movement_target = None  # Clear any conflicting movement targets

    def get_status(self):
        """Get current mouse controller status for debugging."""
        if self.submarine_follow_mouse:
            return "Manual Control (Moving)"
        elif self.was_user_controlled:
            remaining_time = max(0, self.user_control_timeout - self.user_control_timer)
            return f"Manual Control (Idle {remaining_time//60:.1f}s)"
        else:
            return "Autonomous"
                
    def draw_ui(self, screen, automation_systems):
        """Draw mouse control UI elements."""
        autopilot, crew_ai, auto_crafter = automation_systems
        font = pygame.font.Font(None, 24)
        
        # Draw toggle buttons
        buttons = [
            ('AutoPilot', autopilot.enabled, self.click_zones['autopilot_toggle']),
            ('Crew AI', crew_ai.enabled, self.click_zones['crew_ai_toggle']),
            ('AutoCraft', auto_crafter.enabled, self.click_zones['auto_craft_toggle']),
            ('Mouse Follow', self.submarine_follow_mouse, self.click_zones['submarine_follow'])
        ]
        
        for text, enabled, rect in buttons:
            color = GREEN if enabled else RED
            pygame.draw.rect(screen, color, rect)
            pygame.draw.rect(screen, WHITE, rect, 2)
            
            text_surface = font.render(text, True, WHITE)
            text_rect = text_surface.get_rect(center=rect.center)
            screen.blit(text_surface, text_rect)
