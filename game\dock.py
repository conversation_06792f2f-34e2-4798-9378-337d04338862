"""Dock system for submarine resurfacing and resource unloading."""

import pygame
import math
import random
from .constants import *

class Warehouse:
    """Warehouse building at the dock for storing resources."""

    def __init__(self, dock_x, dock_y):
        # Position warehouse directly on the dock platform
        self.dock_x = dock_x
        self.dock_y = dock_y
        # Position warehouse on the right side of the dock platform
        self.x = dock_x + 140  # Position on the dock platform itself
        self.y = dock_y - 80   # Elevated above the dock platform

        # Warehouse dimensions - sized to fit on dock
        self.width = 140
        self.height = 80

        # Storage system
        self.stored_resources = {
            'kelp': 0,
            'coral': 0,
            'pearl': 0,
            'treasure': 0
        }

        # Warehouse properties
        self.base_storage_capacity = 200  # Base storage per resource type
        self.storage_multiplier = 1.0     # Upgrade multiplier

        # Upgrade levels
        self.upgrades = {
            'capacity': 0,        # Increases storage capacity
            'efficiency': 0,      # Faster resource processing
            'automation': 0,      # Auto-sorting and organization
            'security': 0         # Prevents resource loss/theft
        }

        # Visual effects
        self.activity_particles = []
        self.storage_indicators = []
        self.loading_animation = 0

        # Worker interaction
        self.receiving_workers = []  # Workers currently delivering to warehouse
        self.processing_queue = []   # Resources being processed

    def get_storage_capacity(self, resource_type=None):
        """Get storage capacity for a resource type or total."""
        capacity_per_type = int(self.base_storage_capacity * (1.0 + self.upgrades['capacity'] * 0.5))
        if resource_type:
            return capacity_per_type
        return capacity_per_type * len(self.stored_resources)

    def get_storage_used(self, resource_type=None):
        """Get current storage used for a resource type or total."""
        if resource_type:
            return self.stored_resources.get(resource_type, 0)
        return sum(self.stored_resources.values())

    def get_storage_free(self, resource_type=None):
        """Get free storage space for a resource type or total."""
        if resource_type:
            return self.get_storage_capacity(resource_type) - self.get_storage_used(resource_type)
        return self.get_storage_capacity() - self.get_storage_used()

    def can_store_resource(self, resource_type, amount=1):
        """Check if warehouse can store a specific amount of a resource."""
        return self.get_storage_free(resource_type) >= amount

    def store_resource(self, resource_type, amount=1):
        """Store a resource in warehouse. Returns amount actually stored."""
        if resource_type not in self.stored_resources:
            return 0

        max_storable = self.get_storage_free(resource_type)
        actual_stored = min(amount, max_storable)
        self.stored_resources[resource_type] += actual_stored

        # Create visual feedback
        if actual_stored > 0:
            self._create_storage_particle(resource_type, actual_stored)

        return actual_stored

    def remove_resource(self, resource_type, amount=1):
        """Remove a resource from warehouse. Returns amount actually removed."""
        if resource_type not in self.stored_resources:
            return 0

        actual_removed = min(amount, self.stored_resources[resource_type])
        self.stored_resources[resource_type] -= actual_removed
        return actual_removed

    def update(self, dt):
        """Update warehouse systems and animations."""
        self.loading_animation += dt * 2.0

        # Update activity particles
        self.activity_particles = [p for p in self.activity_particles if self._update_particle(p, dt)]

        # Process any queued resources
        self._process_resource_queue(dt)

        # Update storage indicators
        self._update_storage_indicators()

    def _create_storage_particle(self, resource_type, amount):
        """Create visual particle for resource storage."""
        resource_colors = {
            'kelp': (50, 150, 50),
            'coral': (255, 100, 100),
            'pearl': (200, 200, 255),
            'treasure': (255, 215, 0)
        }

        color = resource_colors.get(resource_type, (255, 255, 255))

        for _ in range(min(amount, 3)):  # Max 3 particles per storage event
            particle = {
                'x': self.x + random.randint(10, self.width - 10),
                'y': self.y + random.randint(10, self.height - 10),
                'vx': random.uniform(-1, 1),
                'vy': random.uniform(-2, -0.5),
                'color': color,
                'size': random.randint(2, 4),
                'life': 1.0,
                'type': 'storage'
            }
            self.activity_particles.append(particle)

    def _update_particle(self, particle, dt):
        """Update a single particle. Returns True if particle should continue."""
        particle['x'] += particle['vx'] * dt * 60
        particle['y'] += particle['vy'] * dt * 60
        particle['vy'] += 0.1 * dt * 60  # Gravity
        particle['life'] -= dt * 2.0

        return particle['life'] > 0

    def _process_resource_queue(self, dt):
        """Process any queued resources for efficiency upgrades."""
        # This could be expanded for more complex processing mechanics
        pass

    def _update_storage_indicators(self):
        """Update visual storage level indicators."""
        self.storage_indicators.clear()

        # Create indicators for each resource type
        for i, (resource_type, amount) in enumerate(self.stored_resources.items()):
            if amount > 0:
                capacity = self.get_storage_capacity(resource_type)
                fill_percentage = amount / capacity if capacity > 0 else 0

                indicator = {
                    'resource_type': resource_type,
                    'fill_percentage': fill_percentage,
                    'position': i,
                    'amount': amount
                }
                self.storage_indicators.append(indicator)

    def draw(self, screen):
        """Draw the warehouse building and storage indicators."""
        # Draw warehouse foundation that connects to dock platform
        self._draw_warehouse_foundation(screen)

        # Main warehouse building - integrated with dock
        building_color = (140, 100, 70)  # Lighter brown warehouse color
        roof_color = (100, 70, 50)       # Darker brown for roof

        # Main building base with texture
        self._draw_warehouse_walls(screen, building_color)

        # Building details - vertical support beams
        self._draw_warehouse_beams(screen)

        # Roof - more detailed with multiple sections
        roof_height = 25
        roof_points = [
            (self.x - 8, self.y),
            (self.x + self.width // 2, self.y - roof_height),
            (self.x + self.width + 8, self.y),
        ]
        pygame.draw.polygon(screen, roof_color, roof_points)
        pygame.draw.polygon(screen, (0, 0, 0), roof_points, 3)

        # Roof ridge
        ridge_color = (60, 40, 30)
        pygame.draw.line(screen, ridge_color,
                        (self.x - 8, self.y), (self.x + self.width + 8, self.y), 4)

        # Large warehouse doors - double doors for industrial look
        door_width = 50
        door_height = 65
        door_x = self.x + (self.width - door_width) // 2
        door_y = self.y + self.height - door_height

        # Left door
        left_door_width = door_width // 2 - 1
        pygame.draw.rect(screen, (80, 50, 30), (door_x, door_y, left_door_width, door_height))
        pygame.draw.rect(screen, (0, 0, 0), (door_x, door_y, left_door_width, door_height), 2)

        # Right door
        right_door_x = door_x + door_width // 2 + 1
        pygame.draw.rect(screen, (80, 50, 30), (right_door_x, door_y, left_door_width, door_height))
        pygame.draw.rect(screen, (0, 0, 0), (right_door_x, door_y, left_door_width, door_height), 2)

        # Door handles
        handle_size = 4
        left_handle_x = door_x + left_door_width - 6
        right_handle_x = right_door_x + 6
        handle_y = door_y + door_height // 2
        pygame.draw.circle(screen, (200, 200, 200), (left_handle_x, handle_y), handle_size)
        pygame.draw.circle(screen, (200, 200, 200), (right_handle_x, handle_y), handle_size)

        # Door reinforcement bars
        bar_color = (60, 40, 20)
        for door_start_x in [door_x, right_door_x]:
            for i in range(3):
                bar_y = door_y + 10 + i * 15
                pygame.draw.rect(screen, bar_color, (door_start_x + 3, bar_y, left_door_width - 6, 3))

        # Windows - larger and more detailed
        window_width = 18
        window_height = 15
        window_y1 = self.y + 25  # Upper row
        window_y2 = self.y + 50  # Lower row

        # Upper row of windows
        for i in range(4):
            window_x = self.x + 20 + i * 30
            # Window frame
            pygame.draw.rect(screen, (100, 70, 50),
                           (window_x - 2, window_y1 - 2, window_width + 4, window_height + 4))
            # Window glass
            pygame.draw.rect(screen, (150, 200, 255), (window_x, window_y1, window_width, window_height))
            pygame.draw.rect(screen, (0, 0, 0), (window_x, window_y1, window_width, window_height), 2)

            # Window cross bars
            pygame.draw.line(screen, (0, 0, 0),
                           (window_x + window_width // 2, window_y1),
                           (window_x + window_width // 2, window_y1 + window_height), 2)
            pygame.draw.line(screen, (0, 0, 0),
                           (window_x, window_y1 + window_height // 2),
                           (window_x + window_width, window_y1 + window_height // 2), 2)

        # Lower row of windows (fewer, larger)
        for i in range(3):
            window_x = self.x + 30 + i * 35
            # Window frame
            pygame.draw.rect(screen, (100, 70, 50),
                           (window_x - 2, window_y2 - 2, window_width + 4, window_height + 4))
            # Window glass
            pygame.draw.rect(screen, (150, 200, 255), (window_x, window_y2, window_width, window_height))
            pygame.draw.rect(screen, (0, 0, 0), (window_x, window_y2, window_width, window_height), 2)

            # Window cross bars
            pygame.draw.line(screen, (0, 0, 0),
                           (window_x + window_width // 2, window_y2),
                           (window_x + window_width // 2, window_y2 + window_height), 2)
            pygame.draw.line(screen, (0, 0, 0),
                           (window_x, window_y2 + window_height // 2),
                           (window_x + window_width, window_y2 + window_height // 2), 2)

        # Loading dock area
        self._draw_loading_dock(screen)

        # Storage level indicators
        self._draw_storage_indicators(screen)

        # Activity particles
        self._draw_particles(screen)

        # Warehouse sign and details
        self._draw_warehouse_sign(screen)
        self._draw_warehouse_details(screen)

    def _draw_warehouse_walls(self, screen, building_color):
        """Draw warehouse walls with texture and detail."""
        # Main wall with siding texture
        siding_height = 4
        siding_colors = [
            building_color,
            (building_color[0] - 10, building_color[1] - 5, building_color[2] - 3),
            (building_color[0] + 10, building_color[1] + 5, building_color[2] + 3)
        ]

        # Draw horizontal siding
        for y in range(0, self.height, siding_height):
            siding_y = self.y + y
            color_index = (y // siding_height) % len(siding_colors)
            siding_color = siding_colors[color_index]

            pygame.draw.rect(screen, siding_color,
                           (self.x, siding_y, self.width, min(siding_height, self.height - y)))

            # Siding separation line
            if y + siding_height < self.height:
                pygame.draw.line(screen, (building_color[0] - 30, building_color[1] - 20, building_color[2] - 10),
                               (self.x, siding_y + siding_height - 1),
                               (self.x + self.width, siding_y + siding_height - 1), 1)

        # Main building outline
        pygame.draw.rect(screen, (0, 0, 0), (self.x, self.y, self.width, self.height), 3)

        # Corner trim
        corner_color = (building_color[0] - 20, building_color[1] - 15, building_color[2] - 8)
        pygame.draw.rect(screen, corner_color, (self.x, self.y, 4, self.height))
        pygame.draw.rect(screen, corner_color, (self.x + self.width - 4, self.y, 4, self.height))

    def _draw_warehouse_beams(self, screen):
        """Draw warehouse structural beams and supports."""
        beam_color = (100, 70, 50)
        beam_detail_color = (80, 50, 30)
        beam_width = 8

        # Vertical support beams
        beam_positions = [
            self.x + self.width // 4,
            self.x + self.width // 2,
            self.x + 3 * self.width // 4
        ]

        for beam_x in beam_positions:
            # Main beam
            pygame.draw.rect(screen, beam_color, (beam_x, self.y, beam_width, self.height))

            # Beam details
            pygame.draw.rect(screen, beam_detail_color, (beam_x, self.y, beam_width, self.height), 2)

            # Beam rivets/bolts
            for rivet_y in range(self.y + 10, self.y + self.height - 10, 15):
                pygame.draw.circle(screen, (60, 40, 20), (beam_x + beam_width // 2, rivet_y), 2)

        # Horizontal support beam
        horizontal_beam_y = self.y + self.height // 2
        pygame.draw.rect(screen, beam_color, (self.x + 5, horizontal_beam_y, self.width - 10, 6))
        pygame.draw.rect(screen, beam_detail_color, (self.x + 5, horizontal_beam_y, self.width - 10, 6), 1)

    def _draw_storage_indicators(self, screen):
        """Draw visual indicators of storage levels."""
        if not self.storage_indicators:
            return

        # Large storage tanks/silos integrated with warehouse - positioned on dock platform
        tank_width = 18
        tank_height = 50
        tank_spacing = 22
        # Position tanks behind warehouse but still on the dock platform
        start_x = self.x + self.width - 60  # Keep tanks within dock bounds
        start_y = self.y + self.height - tank_height

        resource_colors = {
            'kelp': (50, 150, 50),
            'coral': (255, 100, 100),
            'pearl': (200, 200, 255),
            'treasure': (255, 215, 0)
        }

        for i, indicator in enumerate(self.storage_indicators):
            tank_x = start_x + i * tank_spacing
            tank_y = start_y

            # Tank base/foundation
            base_height = 8
            pygame.draw.rect(screen, (80, 80, 80),
                           (tank_x - 2, tank_y + tank_height, tank_width + 4, base_height))

            # Tank outline with metallic appearance
            pygame.draw.rect(screen, (140, 140, 140), (tank_x, tank_y, tank_width, tank_height))
            pygame.draw.rect(screen, (0, 0, 0), (tank_x, tank_y, tank_width, tank_height), 3)

            # Tank bands (industrial look)
            band_color = (100, 100, 100)
            for band in range(3):
                band_y = tank_y + 10 + band * 20
                pygame.draw.rect(screen, band_color, (tank_x - 1, band_y, tank_width + 2, 3))

            # Fill level with gradient effect
            fill_height = int(tank_height * indicator['fill_percentage'])
            if fill_height > 0:
                fill_y = tank_y + tank_height - fill_height
                color = resource_colors.get(indicator['resource_type'], (255, 255, 255))

                # Main fill
                pygame.draw.rect(screen, color, (tank_x + 2, fill_y, tank_width - 4, fill_height))

                # Highlight effect on left side
                highlight_color = tuple(min(255, c + 30) for c in color)
                pygame.draw.rect(screen, highlight_color, (tank_x + 2, fill_y, 3, fill_height))

                # Shadow effect on right side
                shadow_color = tuple(max(0, c - 30) for c in color)
                pygame.draw.rect(screen, shadow_color, (tank_x + tank_width - 5, fill_y, 3, fill_height))

            # Tank top cap
            cap_height = 6
            pygame.draw.rect(screen, (120, 120, 120),
                           (tank_x - 1, tank_y - cap_height, tank_width + 2, cap_height))
            pygame.draw.rect(screen, (0, 0, 0),
                           (tank_x - 1, tank_y - cap_height, tank_width + 2, cap_height), 2)

            # Resource type label
            font = pygame.font.Font(None, 14)
            label = indicator['resource_type'][0].upper()  # First letter
            text = font.render(label, True, (255, 255, 255))
            text_rect = text.get_rect(center=(tank_x + tank_width // 2, tank_y - 10))
            screen.blit(text, text_rect)

            # Amount text
            if indicator['amount'] > 0:
                amount_text = font.render(str(indicator['amount']), True, (255, 255, 255))
                amount_rect = amount_text.get_rect(center=(tank_x + tank_width // 2, tank_y + tank_height + 10))

                # Text shadow
                shadow_text = font.render(str(indicator['amount']), True, (0, 0, 0))
                shadow_rect = amount_rect.copy()
                shadow_rect.x += 1
                shadow_rect.y += 1
                screen.blit(shadow_text, shadow_rect)
                screen.blit(amount_text, amount_rect)

    def _draw_particles(self, screen):
        """Draw activity particles."""
        for particle in self.activity_particles:
            alpha = int(255 * particle['life'])
            color = (*particle['color'], alpha)

            # Create a surface for alpha blending
            particle_surface = pygame.Surface((particle['size'] * 2, particle['size'] * 2), pygame.SRCALPHA)
            pygame.draw.circle(particle_surface, color, (particle['size'], particle['size']), particle['size'])
            screen.blit(particle_surface, (particle['x'] - particle['size'], particle['y'] - particle['size']))

    def _draw_warehouse_sign(self, screen):
        """Draw warehouse identification sign."""
        # Warehouse sign mounted on building
        sign_width = 80
        sign_height = 20
        sign_x = self.x + (self.width - sign_width) // 2
        sign_y = self.y - 35

        # Sign background
        sign_bg_color = (200, 200, 200)
        pygame.draw.rect(screen, sign_bg_color, (sign_x, sign_y, sign_width, sign_height))
        pygame.draw.rect(screen, (0, 0, 0), (sign_x, sign_y, sign_width, sign_height), 2)

        # Sign background with border
        pygame.draw.rect(screen, (240, 240, 240), (sign_x, sign_y, sign_width, sign_height))
        pygame.draw.rect(screen, (0, 0, 0), (sign_x, sign_y, sign_width, sign_height), 3)

        # Sign border decoration
        pygame.draw.rect(screen, (200, 200, 200), (sign_x + 2, sign_y + 2, sign_width - 4, sign_height - 4), 1)

        # Sign text - larger and more prominent
        font = pygame.font.Font(None, 20)
        text = font.render("WAREHOUSE", True, (0, 0, 0))
        text_rect = text.get_rect(center=(sign_x + sign_width // 2, sign_y + sign_height // 2))
        screen.blit(text, text_rect)

        # Subtitle
        font_small = pygame.font.Font(None, 12)
        subtitle = font_small.render("STORAGE FACILITY", True, (100, 100, 100))
        subtitle_rect = subtitle.get_rect(center=(sign_x + sign_width // 2, sign_y + sign_height + 8))
        screen.blit(subtitle, subtitle_rect)

    def get_upgrade_cost(self, upgrade_type):
        """Get the cost for the next level of an upgrade."""
        if upgrade_type not in self.upgrades:
            return None

        current_level = self.upgrades[upgrade_type]

        # Define upgrade costs
        costs = {
            'capacity': {
                'base_cost': {'kelp': 50, 'coral': 25},
                'multiplier': 1.5
            },
            'efficiency': {
                'base_cost': {'coral': 30, 'pearl': 15},
                'multiplier': 1.4
            },
            'automation': {
                'base_cost': {'pearl': 20, 'treasure': 5},
                'multiplier': 1.6
            },
            'security': {
                'base_cost': {'treasure': 10, 'pearl': 25},
                'multiplier': 1.3
            }
        }

        if upgrade_type not in costs:
            return None

        cost_info = costs[upgrade_type]
        final_cost = {}

        for resource, base_amount in cost_info['base_cost'].items():
            final_cost[resource] = int(base_amount * (cost_info['multiplier'] ** current_level))

        return final_cost

    def upgrade(self, upgrade_type, resources):
        """Upgrade a warehouse feature if affordable."""
        cost = self.get_upgrade_cost(upgrade_type)
        if not cost:
            return False

        # Check if affordable
        for resource, amount in cost.items():
            if resources.get(resource, 0) < amount:
                return False

        # Deduct resources
        for resource, amount in cost.items():
            resources[resource] -= amount

        # Apply upgrade
        self.upgrades[upgrade_type] += 1

        # Apply upgrade effects
        if upgrade_type == 'capacity':
            # Storage capacity is calculated dynamically
            pass
        elif upgrade_type == 'efficiency':
            # Could affect processing speed or resource yields
            pass
        elif upgrade_type == 'automation':
            # Could enable auto-sorting or other features
            pass
        elif upgrade_type == 'security':
            # Could prevent resource loss or provide bonuses
            pass

        return True

    def _draw_warehouse_foundation(self, screen):
        """Draw warehouse foundation that integrates with dock platform."""
        # Foundation that extends from dock platform to support warehouse
        foundation_color = (101, 67, 33)  # Same as dock platform outline
        platform_color = (139, 69, 19)   # Same as dock platform

        # Extended platform section for warehouse
        platform_extension_width = self.width + 20
        platform_extension_height = 20
        platform_x = self.x - 10
        platform_y = self.y + self.height

        # Draw extended platform
        pygame.draw.rect(screen, platform_color,
                        (platform_x, platform_y, platform_extension_width, platform_extension_height))
        pygame.draw.rect(screen, foundation_color,
                        (platform_x, platform_y, platform_extension_width, platform_extension_height), 3)

        # Platform supports under warehouse
        support_width = 8
        support_height = 25
        for i in range(0, platform_extension_width, 30):
            support_x = platform_x + i
            support_y = platform_y + platform_extension_height
            pygame.draw.rect(screen, foundation_color,
                           (support_x, support_y, support_width, support_height))

        # Connecting walkway from main dock to warehouse platform
        walkway_width = 15
        walkway_start_x = self.dock_x + 200  # End of main dock
        walkway_end_x = platform_x
        walkway_y = self.dock_y + 5

        if walkway_end_x > walkway_start_x:
            pygame.draw.rect(screen, platform_color,
                            (walkway_start_x, walkway_y, walkway_end_x - walkway_start_x, walkway_width))
            pygame.draw.rect(screen, foundation_color,
                            (walkway_start_x, walkway_y, walkway_end_x - walkway_start_x, walkway_width), 2)

    def _draw_loading_dock(self, screen):
        """Draw loading dock area for warehouse."""
        # Loading bay integrated into warehouse
        bay_color = (100, 100, 100)
        bay_width = 35
        bay_height = 25
        bay_x = self.x - 5
        bay_y = self.y + self.height - bay_height

        # Loading bay opening
        pygame.draw.rect(screen, bay_color,
                        (bay_x, bay_y, bay_width, bay_height))
        pygame.draw.rect(screen, (60, 60, 60),
                        (bay_x, bay_y, bay_width, bay_height), 3)

        # Loading dock doors
        door_color = (80, 80, 80)
        door_width = bay_width // 2 - 2
        door_height = bay_height - 4

        # Left door
        pygame.draw.rect(screen, door_color,
                        (bay_x + 2, bay_y + 2, door_width, door_height))
        # Right door
        pygame.draw.rect(screen, door_color,
                        (bay_x + bay_width // 2 + 1, bay_y + 2, door_width, door_height))

        # Door handles
        handle_color = (200, 200, 200)
        pygame.draw.circle(screen, handle_color, (bay_x + door_width - 3, bay_y + bay_height // 2), 2)
        pygame.draw.circle(screen, handle_color, (bay_x + bay_width - 3, bay_y + bay_height // 2), 2)

        # Support pillars for loading bay
        pillar_color = (80, 80, 80)
        pillar_width = 6
        pillar_height = 20
        for i in range(2):
            pillar_x = bay_x + 5 + i * 25
            pillar_y = bay_y + bay_height
            pygame.draw.rect(screen, pillar_color,
                           (pillar_x, pillar_y, pillar_width, pillar_height))

    def _draw_warehouse_details(self, screen):
        """Draw additional warehouse details."""
        # Ventilation system on roof
        vent_color = (60, 60, 60)
        vent_size = 8
        for i in range(3):
            vent_x = self.x + 30 + i * 40
            vent_y = self.y - 15
            pygame.draw.rect(screen, vent_color, (vent_x, vent_y, vent_size, vent_size))
            pygame.draw.rect(screen, (0, 0, 0), (vent_x, vent_y, vent_size, vent_size), 1)

            # Vent grilles
            for j in range(3):
                line_y = vent_y + 2 + j * 2
                pygame.draw.line(screen, (40, 40, 40),
                               (vent_x + 1, line_y), (vent_x + vent_size - 1, line_y), 1)

        # External storage containers - positioned on dock platform
        container_color = (100, 100, 120)
        container_width = 20
        container_height = 25

        for i in range(2):
            # Position containers beside warehouse but within dock bounds
            container_x = self.x + self.width - 45 + i * 25
            container_y = self.y + self.height - container_height

            pygame.draw.rect(screen, container_color,
                           (container_x, container_y, container_width, container_height))
            pygame.draw.rect(screen, (0, 0, 0),
                           (container_x, container_y, container_width, container_height), 2)

            # Container labels
            font = pygame.font.Font(None, 12)
            label = f"C{i+1}"
            text = font.render(label, True, (255, 255, 255))
            text_rect = text.get_rect(center=(container_x + container_width//2,
                                            container_y + container_height//2))
            screen.blit(text, text_rect)

        # Warehouse activity indicator lights
        if sum(self.stored_resources.values()) > 0:
            # Active light - green
            light_color = (0, 255, 0)
        else:
            # Inactive light - red
            light_color = (255, 0, 0)

        light_x = self.x + self.width - 15
        light_y = self.y + 10
        pygame.draw.circle(screen, light_color, (light_x, light_y), 4)
        pygame.draw.circle(screen, (0, 0, 0), (light_x, light_y), 4, 2)

class DockWorker:
    """Individual dock worker that transfers resources from submarine to dock storage."""

    def __init__(self, dock_x, dock_y, worker_id):
        self.dock_x = dock_x
        self.dock_y = dock_y
        self.worker_id = worker_id

        # Position and movement
        self.x = dock_x + random.uniform(-20, 20)
        self.y = dock_y + random.uniform(-10, 10)
        self.target_x = self.x
        self.target_y = self.y

        # Worker properties
        self.base_speed = 1.5
        self.base_capacity = 1  # Resources per trip
        self.base_work_time = 15  # Frames to load/unload (significantly reduced for faster early game)

        # Upgrade bonuses (set by dock)
        self.speed_bonus = 0.0
        self.capacity_bonus = 0
        self.work_speed_bonus = 0.0

        # Current state
        self.state = 'idle'  # idle, moving_to_sub, loading, moving_to_dock, unloading
        self.carrying_resources = {}
        self.work_timer = 0
        self.target_submarine = None

        # Animation
        self.animation_offset = random.random() * math.pi * 2

    def update(self, dt, submarine=None):
        """Update dock worker behavior."""
        # dt is now in seconds, scale for animation speed
        self.animation_offset += dt * 3.0  # 0.05 * 60

        if self.state == 'idle' and submarine:
            # Check if submarine has resources to unload
            if submarine.get_storage_used() > 0:
                self.state = 'moving_to_sub'
                self.target_submarine = submarine
                # Move to a position near the submarine
                self.target_x = submarine.x + submarine.width // 2 + random.randint(-15, 15)
                self.target_y = submarine.y + submarine.height + random.randint(-5, 5)

        elif self.state == 'moving_to_sub':
            self._move_towards_target(dt)
            if self._reached_target():
                self.state = 'loading'
                self.work_timer = 0

        elif self.state == 'loading':
            self.work_timer += 1
            work_time = self.base_work_time * (1.0 - self.work_speed_bonus)
            if self.work_timer >= work_time:
                self._load_resources_from_submarine()
                if self.carrying_resources:
                    self.state = 'moving_to_dock'
                    self.target_x = self.dock_x + random.uniform(-15, 15)
                    self.target_y = self.dock_y + random.uniform(-10, 10)
                else:
                    self.state = 'idle'

        elif self.state == 'moving_to_dock':
            self._move_towards_target(dt)
            if self._reached_target():
                self.state = 'moving_to_warehouse'
                # Set target to warehouse loading dock area
                self.target_x = self.dock_x + 210 - 20  # Warehouse loading area
                self.target_y = self.dock_y + random.uniform(-5, 5)

        elif self.state == 'moving_to_warehouse':
            self._move_towards_target(dt)
            if self._reached_target():
                self.state = 'unloading'
                self.work_timer = 0

        elif self.state == 'unloading':
            self.work_timer += 1
            # Make unloading take slightly longer than loading but not too much
            base_unload_time = self.base_work_time * 1.2  # 1.2x the time for unloading (reduced from 1.5x for faster early game)
            work_time = base_unload_time * (1.0 - self.work_speed_bonus)
            if self.work_timer >= work_time:
                # Mark as ready for resource collection by dock
                self.state = 'ready_to_unload'

    def _move_towards_target(self, dt):
        """Move towards current target position."""
        dx = self.target_x - self.x
        dy = self.target_y - self.y
        distance = math.sqrt(dx**2 + dy**2)

        if distance > 2:
            speed = self.base_speed * (1.0 + self.speed_bonus)
            self.x += (dx / distance) * speed
            self.y += (dy / distance) * speed

    def _reached_target(self):
        """Check if worker has reached target position."""
        distance = math.sqrt((self.target_x - self.x)**2 + (self.target_y - self.y)**2)
        return distance <= 3

    def _load_resources_from_submarine(self):
        """Load resources from submarine storage."""
        if not self.target_submarine:
            return

        capacity = self.base_capacity + self.capacity_bonus
        loaded = 0

        # Try to load resources in priority order (most valuable first)
        for resource_type in ['treasure', 'pearl', 'coral', 'kelp']:
            if loaded >= capacity:
                break

            available = self.target_submarine.get_storage_used(resource_type)
            if available > 0:
                to_load = min(available, capacity - loaded)
                actual_removed = self.target_submarine.remove_resource(resource_type, to_load)
                if actual_removed > 0:
                    self.carrying_resources[resource_type] = self.carrying_resources.get(resource_type, 0) + actual_removed
                    loaded += actual_removed

        # If we couldn't load anything, go idle
        if loaded == 0:
            self.state = 'idle'

    def _unload_resources_to_dock(self):
        """Unload carried resources to dock storage."""
        # This method should actually transfer resources to dock storage
        # The dock will handle this through the get_carried_resources method
        # when the worker completes unloading
        pass

    def get_carried_resources(self):
        """Get and clear carried resources."""
        resources = self.carrying_resources.copy()
        self.carrying_resources.clear()
        return resources

class Dock:
    """Main dock where submarines can resurface and unload resources."""
    
    def __init__(self, x=None, y=None):
        # Position dock at surface level, centered by default
        self.x = x if x is not None else WORLD_WIDTH // 2 - 100
        self.y = y if y is not None else SURFACE_LEVEL - 20
        
        # Dock dimensions - extended to accommodate warehouse
        self.width = 280  # Extended to fit warehouse
        self.height = 40
        self.platform_height = 15
        
        # Dock state
        self.is_occupied = False
        self.docked_submarine = None
        self.unloading_progress = 0.0
        self.base_unloading_duration = 90  # 1.5 seconds at 60 FPS (significantly reduced for faster early game)
        self.current_unloading_duration = self.base_unloading_duration
        self.dock_cooldown = 0  # Cooldown after unloading to prevent immediate re-docking
        self.dock_start_time = 0  # Track how long submarine has been docked

        # Smooth undocking state
        self.undocking_submarine = None  # Submarine that's currently undocking
        self.undocking_progress = 0.0  # Progress of smooth undocking movement
        self.undocking_duration = 1800  # Adjusted duration for smooth 2-second animation
        self.undocking_target_depth = 80  # Target depth to move submarine to (increased for better clearance)
        self.undocking_start_y = 0  # Store the starting Y position for undocking
        self.undocking_initiated = False  # Flag to prevent multiple undocking attempts
        self._last_undocking_submarine = None  # Track submarine that just finished undocking
        
        # Upgrade levels
        self.upgrades = {
            'processing_speed': 0,    # Faster unloading
            'storage_capacity': 0,    # Hold more resources
            'automation': 0,          # Auto-dock when full
            'efficiency': 0,          # Better resource yields
            'crew_quarters': 0        # More crew members
        }
        
        # Storage system
        self.stored_resources = {
            'kelp': 0,
            'coral': 0,
            'pearl': 0,
            'treasure': 0
        }
        self.max_storage = 100  # Base storage capacity
        
        # Visual effects
        self.wave_animation = 0
        self.crane_animation = 0
        self.unloading_particles = []
        
        # Crew unloading system (visual only)
        self.unloading_crew = []
        self.crew_spawn_timer = 0

        # Dock worker system (functional)
        self.dock_workers = []
        self.base_worker_count = 1
        self.worker_spawn_timer = 0
        self.unloading_queue = []  # Queue of resources to unload from submarine

        # Warehouse system
        self.warehouse = Warehouse(self.x, self.y)
        
    def update(self, dt, submarine=None, resources_collected=0):
        """Update dock systems and animations."""
        # dt is now in seconds, scale for animation speed
        self.wave_animation += dt * 1.2  # 0.02 * 60

        # Update warehouse
        self.warehouse.update(dt)

        # Update dock cooldown
        if self.dock_cooldown > 0:
            self.dock_cooldown -= 1

        # Handle submarine docking
        if submarine and not self.is_occupied and self.dock_cooldown <= 0:
            # Check for auto-dock if automation is upgraded
            if self.upgrades['automation'] > 0:
                auto_dock_threshold = 1.0 - (self.upgrades['automation'] * 0.1)  # 90%, 80%, etc.
                if resources_collected >= auto_dock_threshold * 20:  # Assume 20 is "full"
                    self._auto_dock_submarine(submarine)
            else:
                self._check_docking(submarine)

        # Handle unloading process
        unloading_complete = False
        if self.is_occupied and self.docked_submarine:
            unloading_complete = self._update_unloading(dt)

        # Handle smooth undocking process
        self._update_smooth_undocking(dt)

        # Update dock workers
        self._update_dock_workers(dt, submarine)

        # Update visual effects
        self._update_animations(dt)
        self._update_particles(dt)
        self._update_unloading_crew(dt)

        return unloading_complete
        
    def _check_docking(self, submarine):
        """Check if submarine is close enough to dock."""
        try:
            # Larger, more forgiving dock zone
            dock_zone = pygame.Rect(int(self.x - 80), int(self.y - 50),
                                   int(self.width + 160), int(100))
            sub_rect = pygame.Rect(int(submarine.x), int(submarine.y),
                                  int(submarine.width), int(submarine.height))

            # More forgiving depth check and collision detection
            if submarine.depth <= 20 and dock_zone.colliderect(sub_rect):
                self._dock_submarine(submarine)
        except Exception as e:
            # Silently handle any errors
            pass
    
    def _dock_submarine(self, submarine):
        """Dock the submarine and start unloading process."""
        self.is_occupied = True
        self.docked_submarine = submarine
        self.unloading_progress = 0.0
        self.dock_start_time = 0  # Track how long submarine has been docked

        # Set submarine's docked state
        submarine.set_docked_state(True, self)

        # Reset undocking flag for new docking session
        self.undocking_initiated = False

        # Calculate unloading duration based on resources in submarine storage
        total_resources = submarine.get_storage_used()
        if total_resources > 0:
            # Base duration + additional time per resource
            resource_time_factor = total_resources * 4  # 4 frames per resource (0.067 seconds each, significantly reduced for faster unloading)
            self.current_unloading_duration = self.base_unloading_duration + resource_time_factor
        else:
            # Minimum duration even if no resources (for oxygen refill, etc.)
            self.current_unloading_duration = 60  # 1 second minimum (significantly reduced for faster early game)

        # Apply processing speed upgrades to duration
        speed_multiplier = 1.0 + (self.upgrades.get('processing_speed', 0) * 0.15)
        self.current_unloading_duration = int(self.current_unloading_duration / speed_multiplier)

        # Move submarine to dock position
        submarine.x = self.x + self.width // 2 - submarine.width // 2
        submarine.y = self.y - submarine.height + 5
        submarine.depth = 0

        # Start crew unloading animation
        self._spawn_unloading_crew()

    def _auto_dock_submarine(self, submarine):
        """Automatically dock submarine when conditions are met."""
        # Move submarine towards dock automatically
        dock_center_x = self.x + self.width // 2
        dock_y = self.y - submarine.height + 5

        # Smooth movement towards dock
        dx = dock_center_x - submarine.x
        dy = dock_y - submarine.y

        if abs(dx) > 5 or abs(dy) > 5:
            # Move towards dock
            submarine.x += dx * 0.02  # Slow automatic movement
            submarine.y += dy * 0.02
            submarine.depth = max(0, submarine.y - SURFACE_LEVEL)
        else:
            # Close enough, complete docking
            self._dock_submarine(submarine)
        
    def _update_unloading(self, dt):
        """Update the unloading process."""
        if not self.docked_submarine:
            return False

        # Track docking time
        self.dock_start_time += 1

        # Use the calculated duration for this specific unloading session
        self.unloading_progress += dt / self.current_unloading_duration

        # Create unloading particles more frequently for longer unloading
        particle_chance = 0.2 + (self.unloading_progress * 0.3)  # More particles as progress increases
        if random.random() < particle_chance:
            self._create_unloading_particle()

        # Create additional work particles from dock workers
        if random.random() < 0.4:
            self._create_worker_particle()

        # Complete unloading when timer is done OR submarine is empty
        # For submarines that start docked and empty, we still need a minimum dock time
        if self.unloading_progress >= 1.0:
            return self._complete_unloading()
        elif self.docked_submarine.get_storage_used() == 0 and self.dock_start_time >= 60:  # 1 second minimum for empty subs
            return self._complete_unloading()

        return False
    
    def _complete_unloading(self):
        """Complete the unloading process - dock workers handle the actual resource transfer."""
        if not self.docked_submarine:
            return

        # Check if dock workers have finished unloading all resources
        submarine_has_resources = self.docked_submarine.get_storage_used() > 0
        workers_busy = any(worker.state != 'idle' for worker in self.dock_workers)

        # Only complete unloading if submarine is completely empty AND minimum dock time has passed
        # Use shorter minimum time for submarines that start empty (like at game start)
        minimum_dock_time = 30 if not submarine_has_resources else 60  # 0.5 second for empty, 1 second for loaded
        if submarine_has_resources or self.dock_start_time < minimum_dock_time:
            return False  # Still has resources or hasn't been docked long enough

        # Calculate efficiency bonus for oxygen refill
        efficiency_bonus = 1.0 + (self.upgrades['efficiency'] * 0.1)

        # Set submarine's docked state to false and start smooth undocking
        if self.docked_submarine and not self.undocking_initiated:
            self.docked_submarine.set_docked_state(False, None)

            # Start smooth undocking process
            self.undocking_submarine = self.docked_submarine
            self.undocking_progress = 0.0
            self.undocking_start_y = self.docked_submarine.y  # Store actual starting position
            self.undocking_initiated = True  # Mark undocking as initiated


        # Reset dock state
        self.is_occupied = False
        self.docked_submarine = None
        self.unloading_progress = 0.0
        self.dock_start_time = 0  # Reset dock timer
        self.current_unloading_duration = self.base_unloading_duration  # Reset to base duration
        self.unloading_crew.clear()

        # Set cooldown to prevent immediate re-docking (2 seconds at 60 FPS, reduced for faster early game)
        self.dock_cooldown = 120

        return True  # Signal that unloading is complete

    def _update_smooth_undocking(self, dt):
        """Handle smooth undocking movement to avoid immediate re-docking."""
        if not self.undocking_submarine:
            return



        # Update undocking progress
        self.undocking_progress += dt / self.undocking_duration

        if self.undocking_progress >= 1.0:
            # Undocking complete - ensure submarine reaches final position
            final_y = self.undocking_start_y + self.undocking_target_depth
            self.undocking_submarine.y = final_y
            self.undocking_submarine.depth = max(0, final_y - SURFACE_LEVEL)

            # Set a longer cooldown to prevent immediate re-docking after undocking
            self.dock_cooldown = 300  # 5 seconds cooldown after undocking

            # Mark that this submarine just finished undocking (for autopilot grace period)
            self._last_undocking_submarine = self.undocking_submarine

            # Clean up undocking state
            self.undocking_submarine = None
            self.undocking_progress = 0.0
            self.undocking_start_y = 0
            self.undocking_initiated = False  # Reset flag

            return

        # Calculate smooth movement using linear interpolation for more predictable movement
        progress = self.undocking_progress

        # Use ease-out cubic for smoother deceleration
        eased_progress = 1 - (1 - progress) ** 3  # Cubic ease-out for smoother movement

        # Calculate target position using stored starting position
        start_y = self.undocking_start_y
        target_y = start_y + self.undocking_target_depth

        # Smoothly interpolate position
        current_y = start_y + (target_y - start_y) * eased_progress

        # Update submarine position
        self.undocking_submarine.y = current_y
        self.undocking_submarine.depth = max(0, current_y - SURFACE_LEVEL)



    def _create_worker_particle(self):
        """Create particles from dock workers during unloading."""
        if not self.docked_submarine:
            return

        # Create particles around the submarine and dock area
        for _ in range(random.randint(1, 3)):
            particle_x = self.docked_submarine.x + random.randint(0, self.docked_submarine.width)
            particle_y = self.docked_submarine.y + random.randint(0, self.docked_submarine.height)

            particle = {
                'x': particle_x,
                'y': particle_y,
                'vx': random.uniform(-1.0, 1.0),
                'vy': random.uniform(-2.0, -0.5),
                'life': random.randint(30, 60),
                'max_life': 60,
                'color': random.choice([(255, 255, 0), (255, 200, 0), (200, 255, 100)]),
                'size': random.randint(1, 3),
                'type': 'work'
            }
            self.unloading_particles.append(particle)

    def _spawn_unloading_crew(self):
        """Spawn crew members for unloading animation."""
        crew_count = 2 + self.upgrades['crew_quarters']

        for i in range(crew_count):
            crew_member = {
                'x': self.x + 20 + (i * 30),
                'y': self.y + self.platform_height,
                'start_x': self.x + 20 + (i * 30),
                'start_y': self.y + self.platform_height,
                'target_x': self.x + 20 + (i * 30),
                'target_y': self.y + self.platform_height,
                'state': 'walking_to_sub',  # walking_to_sub, unloading, walking_to_dock, carrying
                'animation': random.random() * math.pi * 2,
                'carry_resource': None,
                'work_timer': 0,
                'move_speed': 1.5 + random.uniform(-0.3, 0.3),  # Slight speed variation
                'id': i,
                'path_offset': random.uniform(-5, 5)  # Slight path variation
            }
            self.unloading_crew.append(crew_member)
    
    def _update_unloading_crew(self, dt):
        """Update crew member animations during unloading."""
        for crew in self.unloading_crew:
            # dt is now in seconds, scale for animation speed
            crew['animation'] += dt * 6.0  # 0.1 * 60
            crew['work_timer'] += dt * 60  # Convert to frame-based timing

            if crew['state'] == 'walking_to_sub':
                # Move towards submarine with path variation
                if self.docked_submarine:
                    target_x = self.docked_submarine.x + self.docked_submarine.width // 2 + crew['path_offset']
                    target_y = self.docked_submarine.y + self.docked_submarine.height

                    dx = target_x - crew['x']
                    dy = target_y - crew['y']
                    distance = math.sqrt(dx**2 + dy**2)

                    if distance > 8:
                        # Move towards submarine
                        crew['x'] += (dx / distance) * crew['move_speed']
                        crew['y'] += (dy / distance) * crew['move_speed']
                    else:
                        crew['state'] = 'unloading'
                        crew['work_timer'] = 0
                        # Assign resource based on crew member and time
                        resources = ['kelp', 'coral', 'pearl', 'treasure']
                        crew['carry_resource'] = resources[crew['id'] % len(resources)]

            elif crew['state'] == 'unloading':
                # Simulate unloading work with timer
                if crew['work_timer'] > 30 + random.randint(0, 30):  # 0.5-1 seconds (reduced for faster unloading)
                    crew['state'] = 'carrying'
                    crew['work_timer'] = 0
                    crew['target_x'] = crew['start_x']
                    crew['target_y'] = crew['start_y']

            elif crew['state'] == 'carrying':
                # Move back to dock with resource
                dx = crew['target_x'] - crew['x']
                dy = crew['target_y'] - crew['y']
                distance = math.sqrt(dx**2 + dy**2)

                if distance > 3:
                    crew['x'] += (dx / distance) * crew['move_speed']
                    crew['y'] += (dy / distance) * crew['move_speed']
                else:
                    # Drop off resource and start again
                    crew['carry_resource'] = None
                    crew['state'] = 'walking_to_sub'
                    crew['work_timer'] = 0
    
    def _update_animations(self, dt):
        """Update visual animations."""
        # dt is now in seconds, scale for animation speed
        self.crane_animation += dt * 3.0  # 0.05 * 60
        
    def _create_unloading_particle(self):
        """Create a particle effect for unloading."""
        if self.docked_submarine:
            # Create multiple particles for better effect
            for _ in range(random.randint(1, 3)):
                particle_type = random.choice(['resource', 'steam', 'sparkle'])

                if particle_type == 'resource':
                    particle = {
                        'x': self.docked_submarine.x + random.randint(0, self.docked_submarine.width),
                        'y': self.docked_submarine.y,
                        'vx': random.uniform(-1.5, 1.5),
                        'vy': random.uniform(-3, -1),
                        'life': 90,
                        'max_life': 90,
                        'color': random.choice([GREEN, ORANGE, WHITE, GOLD]),
                        'size': random.randint(2, 4),
                        'type': 'resource'
                    }
                elif particle_type == 'steam':
                    particle = {
                        'x': self.docked_submarine.x + random.randint(10, self.docked_submarine.width - 10),
                        'y': self.docked_submarine.y - 5,
                        'vx': random.uniform(-0.5, 0.5),
                        'vy': random.uniform(-1.5, -0.8),
                        'life': 120,
                        'max_life': 120,
                        'color': (200, 200, 255),
                        'size': random.randint(3, 6),
                        'type': 'steam'
                    }
                else:  # sparkle
                    particle = {
                        'x': self.x + random.randint(0, self.width),
                        'y': self.y + random.randint(0, self.platform_height),
                        'vx': random.uniform(-0.3, 0.3),
                        'vy': random.uniform(-0.8, -0.3),
                        'life': 45,
                        'max_life': 45,
                        'color': (255, 255, 255),
                        'size': 1,
                        'type': 'sparkle'
                    }

                self.unloading_particles.append(particle)
    
    def _update_particles(self, dt):
        """Update unloading particle effects."""
        for particle in self.unloading_particles[:]:
            particle['x'] += particle['vx']
            particle['y'] += particle['vy']

            # Different physics for different particle types
            if particle['type'] == 'resource':
                particle['vy'] += 0.15  # Gravity for resources
            elif particle['type'] == 'steam':
                particle['vy'] += 0.02  # Light upward drift for steam
                particle['vx'] *= 0.98  # Air resistance
            elif particle['type'] == 'warehouse_transfer':
                # Move toward warehouse
                dx = particle['target_x'] - particle['x']
                dy = particle['target_y'] - particle['y']
                distance = math.sqrt(dx*dx + dy*dy)

                if distance > 5:  # Still moving toward warehouse
                    # Normalize direction and apply speed
                    particle['vx'] = (dx / distance) * particle['speed']
                    particle['vy'] = (dy / distance) * particle['speed']
                else:
                    # Reached warehouse, create arrival effect
                    particle['life'] = 0  # Remove particle
            elif particle['type'] == 'transfer':
                particle['vy'] -= 0.1  # Move upward to show transfer to storage
            else:  # sparkle
                particle['vy'] += 0.05  # Light gravity for sparkles

            particle['life'] -= 1

            if particle['life'] <= 0:
                self.unloading_particles.remove(particle)

    def _update_dock_workers(self, dt, submarine):
        """Update dock worker system."""
        # Spawn workers based on upgrades
        target_worker_count = self.base_worker_count + self.upgrades.get('dock_workers', 0)

        # Spawn new workers if needed
        while len(self.dock_workers) < target_worker_count:
            worker = DockWorker(self.x + self.width // 2, self.y, len(self.dock_workers))
            self._apply_worker_upgrades(worker)
            self.dock_workers.append(worker)

        # Remove excess workers if downgraded (shouldn't happen but safety)
        while len(self.dock_workers) > target_worker_count:
            self.dock_workers.pop()

        # Update all workers
        for worker in self.dock_workers:
            self._apply_worker_upgrades(worker)  # Apply current upgrade levels

            # Allow workers to work on submarines that are near the dock and have resources
            active_submarine = None
            if submarine:
                # Prioritize docked submarine - workers should always work on docked submarines
                if self.is_occupied and self.docked_submarine == submarine and submarine.get_storage_used() > 0:
                    active_submarine = submarine
                else:
                    # Check if submarine is near dock and has resources to unload
                    dock_center_x = self.x + self.width // 2
                    dock_center_y = self.y
                    sub_distance = math.sqrt((submarine.x - dock_center_x)**2 + (submarine.y - dock_center_y)**2)

                    # Allow workers to work if submarine is reasonably close and has resources
                    if sub_distance < 150 and submarine.get_storage_used() > 0:
                        active_submarine = submarine

            worker.update(dt, active_submarine)

            # Collect resources from workers who are ready to unload
            if worker.state == 'ready_to_unload':
                carried = worker.get_carried_resources()
                for resource_type, amount in carried.items():
                    # Try to store in warehouse first, fallback to dock storage
                    stored_in_warehouse = self.warehouse.store_resource(resource_type, amount)
                    remaining = amount - stored_in_warehouse

                    if remaining > 0:
                        # If warehouse is full, store remainder in dock storage
                        self.stored_resources[resource_type] += remaining

                    # Create enhanced visual feedback for resource transfer to warehouse
                    if stored_in_warehouse > 0:
                        # Create particles moving toward warehouse
                        for _ in range(stored_in_warehouse):
                            self._create_warehouse_transfer_particle(worker.x, worker.y, resource_type)

                    if remaining > 0:
                        # Create particles for dock storage
                        self._create_transfer_particle(worker.x, worker.y, resource_type)
                        for _ in range(remaining):
                            self._create_transfer_particle(
                                worker.x + random.randint(-5, 5),
                                worker.y + random.randint(-5, 5),
                                resource_type
                            )

                # Worker is now idle and ready for next task
                worker.state = 'idle'
                worker.work_timer = 0

    def _apply_worker_upgrades(self, worker):
        """Apply current upgrade bonuses to a worker."""
        worker.speed_bonus = self.upgrades.get('worker_speed', 0) * 0.2  # 20% per level
        worker.capacity_bonus = self.upgrades.get('worker_capacity', 0)  # +1 per level
        worker.work_speed_bonus = self.upgrades.get('worker_speed', 0) * 0.15  # 15% per level

    def get_dock_worker_count(self):
        """Get current number of dock workers."""
        return len(self.dock_workers)

    def get_dock_worker_efficiency(self):
        """Get overall dock worker efficiency as a percentage."""
        if not self.dock_workers:
            return 0.0

        # Calculate based on upgrades
        base_efficiency = 1.0
        speed_bonus = self.upgrades.get('worker_speed', 0) * 0.2
        capacity_bonus = self.upgrades.get('worker_capacity', 0) * 0.1
        worker_count_bonus = self.upgrades.get('dock_workers', 0) * 0.15

        total_efficiency = base_efficiency + speed_bonus + capacity_bonus + worker_count_bonus
        return min(total_efficiency, 3.0)  # Cap at 300%

    def _create_transfer_particle(self, worker_x, worker_y, resource_type):
        """Create visual particle when worker transfers resource to dock storage."""
        resource_colors = {
            'kelp': (50, 200, 50),
            'coral': (255, 120, 120),
            'pearl': (220, 220, 255),
            'treasure': (255, 215, 0)
        }

        color = resource_colors.get(resource_type, (255, 255, 255))

        # Create a more visible transfer particle that moves toward dock storage
        particle = {
            'x': worker_x,
            'y': worker_y,
            'vx': random.uniform(-1.0, 1.0),
            'vy': random.uniform(-2.0, -0.5),  # Move upward to show transfer to storage
            'life': 60,  # Longer life for better visibility
            'max_life': 60,
            'color': color,
            'size': random.randint(3, 6),  # Larger particles
            'type': 'transfer',
            'resource_type': resource_type,
            'glow': True  # Add glow effect
        }
        self.unloading_particles.append(particle)

    def _create_warehouse_transfer_particle(self, worker_x, worker_y, resource_type):
        """Create a particle that moves from worker to warehouse."""
        resource_colors = {
            'kelp': (50, 150, 50),
            'coral': (255, 100, 100),
            'pearl': (200, 200, 255),
            'treasure': (255, 215, 0)
        }

        color = resource_colors.get(resource_type, (255, 255, 255))

        # Create a particle that moves toward warehouse
        particle = {
            'x': worker_x,
            'y': worker_y,
            'target_x': self.warehouse.x + self.warehouse.width // 2,
            'target_y': self.warehouse.y + self.warehouse.height // 2,
            'vx': 0,
            'vy': 0,
            'life': 90,  # Longer life for warehouse transfer
            'max_life': 90,
            'color': color,
            'size': random.randint(4, 7),  # Larger particles for warehouse
            'type': 'warehouse_transfer',
            'resource_type': resource_type,
            'glow': True,
            'speed': 2.0  # Speed of movement toward warehouse
        }
        self.unloading_particles.append(particle)

    def draw(self, screen):
        """Draw the dock and all its components."""
        # Draw warehouse first (background)
        self.warehouse.draw(screen)

        # Draw dock platform
        self._draw_platform(screen)

        # Draw dock structures
        self._draw_structures(screen)

        # Draw unloading crew (visual only)
        self._draw_crew(screen)

        # Draw dock workers (functional)
        self._draw_dock_workers(screen)

        # Draw particles
        self._draw_particles(screen)

        # Draw UI elements
        self._draw_ui(screen)
    
    def _draw_platform(self, screen):
        """Draw the main dock platform with detailed planks and textures."""
        # Draw platform foundation first
        self._draw_platform_foundation(screen)

        # Draw wooden planks
        self._draw_wooden_planks(screen)

        # Draw platform edge details
        self._draw_platform_edges(screen)

        # Draw platform supports with more detail
        self._draw_platform_supports(screen)

        # Draw dock clutter and details
        self._draw_dock_clutter(screen)

        # Water effects around dock
        self._draw_water_effects(screen)

    def _draw_platform_foundation(self, screen):
        """Draw the platform foundation and base structure."""
        # Main platform base with gradient effect
        platform_rect = pygame.Rect(self.x, self.y, self.width, self.platform_height)

        # Base color with slight gradient
        base_color = (139, 69, 19)  # Brown wood
        dark_color = (120, 60, 16)  # Darker brown for depth

        # Draw base with gradient effect
        for i in range(self.platform_height):
            gradient_factor = i / self.platform_height
            r = int(base_color[0] + (dark_color[0] - base_color[0]) * gradient_factor)
            g = int(base_color[1] + (dark_color[1] - base_color[1]) * gradient_factor)
            b = int(base_color[2] + (dark_color[2] - base_color[2]) * gradient_factor)

            pygame.draw.rect(screen, (r, g, b),
                           (self.x, self.y + i, self.width, 1))

        # Platform outline
        pygame.draw.rect(screen, (101, 67, 33), platform_rect, 3)

    def _draw_wooden_planks(self, screen):
        """Draw individual wooden planks on the platform."""
        # Scale plank dimensions with current scale factor
        scale = getattr(self, '_current_scale_factor', 1.0)
        plank_width = max(3, int(25 * scale))
        plank_height = max(2, self.platform_height - max(1, int(2 * scale)))
        plank_colors = [
            (145, 75, 25),  # Lighter wood
            (135, 65, 15),  # Medium wood
            (125, 55, 10),  # Darker wood
        ]

        # Draw planks across the platform
        for i in range(0, self.width, plank_width):
            plank_x = self.x + i
            plank_color = plank_colors[i // plank_width % len(plank_colors)]

            # Main plank
            pygame.draw.rect(screen, plank_color,
                           (plank_x, self.y + 1, min(plank_width - 2, self.width - i), plank_height))

            # Plank grain lines
            grain_color = (plank_color[0] - 20, plank_color[1] - 15, plank_color[2] - 5)
            for j in range(2, plank_height - 1, 3):
                pygame.draw.line(screen, grain_color,
                               (plank_x + 2, self.y + j),
                               (plank_x + min(plank_width - 4, self.width - i - 2), self.y + j), 1)

            # Plank separation lines
            if i + plank_width < self.width:
                pygame.draw.line(screen, (80, 50, 20),
                               (plank_x + plank_width - 1, self.y + 1),
                               (plank_x + plank_width - 1, self.y + plank_height), 1)

    def _draw_platform_edges(self, screen):
        """Draw detailed platform edges and trim."""
        edge_color = (90, 50, 25)
        trim_color = (110, 60, 30)

        # Top edge trim
        pygame.draw.rect(screen, trim_color,
                        (self.x - 2, self.y - 2, self.width + 4, 3))

        # Side edge details
        pygame.draw.rect(screen, edge_color,
                        (self.x - 3, self.y, 3, self.platform_height + 5))
        pygame.draw.rect(screen, edge_color,
                        (self.x + self.width, self.y, 3, self.platform_height + 5))

        # Corner reinforcements
        corner_size = 8
        corner_positions = [
            (self.x - 2, self.y - 2),
            (self.x + self.width - corner_size + 2, self.y - 2),
            (self.x - 2, self.y + self.platform_height - corner_size + 2),
            (self.x + self.width - corner_size + 2, self.y + self.platform_height - corner_size + 2)
        ]

        for corner_x, corner_y in corner_positions:
            pygame.draw.rect(screen, (70, 40, 15), (corner_x, corner_y, corner_size, corner_size))
            pygame.draw.rect(screen, (50, 30, 10), (corner_x, corner_y, corner_size, corner_size), 1)

    def _draw_platform_supports(self, screen):
        """Draw detailed platform supports and pilings."""
        support_color = (101, 67, 33)
        support_detail_color = (80, 50, 25)

        # Main support posts
        for i in range(0, self.width, 35):
            support_x = self.x + i
            support_width = 12
            support_height = 25

            # Main support post
            pygame.draw.rect(screen, support_color,
                           (support_x, self.y + self.platform_height, support_width, support_height))

            # Support post details
            pygame.draw.rect(screen, support_detail_color,
                           (support_x, self.y + self.platform_height, support_width, support_height), 2)

            # Cross bracing between supports
            if i + 35 < self.width:
                brace_y = self.y + self.platform_height + 15
                pygame.draw.line(screen, support_detail_color,
                               (support_x + support_width, brace_y),
                               (support_x + 35, brace_y), 3)

            # Support caps
            cap_color = (120, 80, 40)
            pygame.draw.rect(screen, cap_color,
                           (support_x - 2, self.y + self.platform_height - 2, support_width + 4, 4))

    def _draw_dock_clutter(self, screen):
        """Draw themed clutter and details on the dock."""
        # Rope coils
        self._draw_rope_coils(screen)

        # Barrels and crates
        self._draw_barrels_and_crates(screen)

        # Dock equipment
        self._draw_dock_equipment(screen)

        # Nautical details
        self._draw_nautical_details(screen)

    def _draw_water_effects(self, screen):
        """Draw enhanced water effects around the dock."""
        wave_offset = math.sin(self.wave_animation) * 3
        foam_offset = math.cos(self.wave_animation * 1.5) * 2

        # Main water waves
        for i in range(0, self.width + 40, 15):
            wave_x = self.x + i - 20
            wave_y = self.y + self.platform_height + 25 + wave_offset

            # Wave circles with varying sizes
            wave_size = 4 + math.sin(self.wave_animation + i * 0.1) * 2
            pygame.draw.circle(screen, (80, 130, 180), (wave_x, int(wave_y)), int(wave_size), 2)

        # Foam effects near dock
        for i in range(0, self.width, 25):
            foam_x = self.x + i
            foam_y = self.y + self.platform_height + 20 + foam_offset

            # Small foam bubbles
            bubble_size = 2 + math.sin(self.wave_animation * 2 + i * 0.2) * 1
            pygame.draw.circle(screen, (200, 220, 240), (foam_x, int(foam_y)), int(bubble_size))

        # Water reflection of dock
        reflection_alpha = 80
        reflection_surface = pygame.Surface((self.width, 10))
        reflection_surface.set_alpha(reflection_alpha)
        reflection_surface.fill((100, 150, 200))
        screen.blit(reflection_surface, (self.x, self.y + self.platform_height + 30))

    def _draw_rope_coils(self, screen):
        """Draw rope coils scattered on the dock."""
        # Scale rope positions and sizes with current scale factor
        scale = getattr(self, '_current_scale_factor', 1.0)

        rope_positions = [
            (self.x + max(5, int(30 * scale)), self.y - max(2, int(8 * scale))),
            (self.x + self.width - max(10, int(50 * scale)), self.y - max(2, int(6 * scale))),
            (self.x + max(30, int(150 * scale)), self.y - max(2, int(10 * scale)))
        ]

        rope_color = (160, 120, 80)
        rope_shadow = (120, 90, 60)

        for rope_x, rope_y in rope_positions:
            # Scale rope coil sizes
            shadow_radius = max(2, int(8 * scale))
            main_radius = max(2, int(8 * scale))
            mid_radius = max(1, int(5 * scale))
            inner_radius = max(1, int(3 * scale))
            line_width = max(1, int(2 * scale))

            # Rope coil shadow
            pygame.draw.circle(screen, rope_shadow, (int(rope_x + 2 * scale), int(rope_y + 2 * scale)), shadow_radius)

            # Main rope coil
            pygame.draw.circle(screen, rope_color, (rope_x, rope_y), main_radius, max(1, int(3 * scale)))
            pygame.draw.circle(screen, rope_color, (rope_x, rope_y), mid_radius, max(1, int(2 * scale)))
            pygame.draw.circle(screen, rope_color, (rope_x, rope_y), inner_radius, 1)

            # Rope end
            end_x = rope_x + max(2, int(6 * scale))
            end_y = rope_y - max(1, int(3 * scale))
            pygame.draw.line(screen, rope_color, (rope_x, rope_y), (end_x, end_y), line_width)

    def _draw_barrels_and_crates(self, screen):
        """Draw barrels and crates on the dock."""
        # Scale barrel and crate dimensions with current scale factor
        scale = getattr(self, '_current_scale_factor', 1.0)

        # Barrels
        barrel_positions = [
            (self.x + max(10, int(60 * scale)), self.y - max(3, int(15 * scale))),
            (self.x + self.width - max(15, int(80 * scale)), self.y - max(3, int(15 * scale)))
        ]

        barrel_color = (120, 80, 40)
        barrel_band_color = (80, 60, 30)

        for barrel_x, barrel_y in barrel_positions:
            # Scale barrel dimensions
            barrel_width = max(3, int(15 * scale))
            barrel_height = max(4, int(20 * scale))
            shadow_width = max(3, int(16 * scale))
            shadow_height = max(2, int(8 * scale))
            band_height = max(1, int(3 * scale))
            top_height = max(1, int(6 * scale))

            # Barrel shadow
            pygame.draw.ellipse(screen, (60, 40, 20),
                              (int(barrel_x + 2 * scale), int(barrel_y + 12 * scale), shadow_width, shadow_height))

            # Main barrel
            pygame.draw.ellipse(screen, barrel_color, (barrel_x, barrel_y, barrel_width, barrel_height))

            # Barrel bands
            band_positions = [
                barrel_y + max(1, int(3 * scale)),
                barrel_y + max(2, int(10 * scale)),
                barrel_y + max(3, int(17 * scale))
            ]
            for band_y in band_positions:
                if band_y + band_height <= barrel_y + barrel_height:
                    pygame.draw.ellipse(screen, barrel_band_color, (barrel_x, band_y, barrel_width, band_height))

            # Barrel top
            pygame.draw.ellipse(screen, (140, 100, 60), (barrel_x, barrel_y, barrel_width, top_height))

        # Crates
        crate_positions = [
            (self.x + max(20, int(100 * scale)), self.y - max(3, int(12 * scale))),
            (self.x + self.width - max(25, int(120 * scale)), self.y - max(4, int(18 * scale)))
        ]

        crate_color = (100, 70, 40)
        crate_detail_color = (80, 50, 20)

        for crate_x, crate_y in crate_positions:
            crate_width = max(4, int(18 * scale))
            crate_height = max(3, int(15 * scale))

            # Crate shadow
            pygame.draw.rect(screen, (50, 35, 15),
                           (int(crate_x + 2 * scale), int(crate_y + 2 * scale), crate_width, crate_height))

            # Main crate
            pygame.draw.rect(screen, crate_color, (crate_x, crate_y, crate_width, crate_height))
            pygame.draw.rect(screen, crate_detail_color, (crate_x, crate_y, crate_width, crate_height), max(1, int(2 * scale)))

            # Crate slats
            slat_spacing = max(2, int(4 * scale))
            for i in range(max(1, int(3 * scale)), crate_width - max(1, int(2 * scale)), slat_spacing):
                slat_x = crate_x + i
                pygame.draw.line(screen, crate_detail_color,
                               (slat_x, crate_y + max(1, int(2 * scale))),
                               (slat_x, crate_y + crate_height - max(1, int(2 * scale))), 1)

            # Crate label/marking
            label_width = max(2, int(8 * scale))
            label_height = max(1, int(4 * scale))
            pygame.draw.rect(screen, (200, 200, 200),
                           (crate_x + max(1, int(4 * scale)), crate_y + max(1, int(4 * scale)),
                            label_width, label_height))

    def _draw_dock_equipment(self, screen):
        """Draw dock equipment and tools."""
        # Dock cleats for tying boats
        cleat_positions = [
            (self.x + 20, self.y + 2),
            (self.x + self.width - 30, self.y + 2)
        ]

        cleat_color = (150, 150, 150)

        for cleat_x, cleat_y in cleat_positions:
            # Cleat base
            pygame.draw.ellipse(screen, cleat_color, (cleat_x, cleat_y, 12, 6))
            # Cleat arms
            pygame.draw.ellipse(screen, cleat_color, (cleat_x - 2, cleat_y + 1, 16, 4))
            pygame.draw.rect(screen, (100, 100, 100), (cleat_x, cleat_y, 12, 6), 1)

        # Dock lights/lanterns
        light_positions = [
            (self.x + self.width // 4, self.y - 25),
            (self.x + 3 * self.width // 4, self.y - 25)
        ]

        for light_x, light_y in light_positions:
            # Light post
            pygame.draw.rect(screen, (60, 60, 60), (light_x, light_y, 4, 20))

            # Lantern
            pygame.draw.rect(screen, (100, 100, 100), (light_x - 3, light_y, 10, 8))
            pygame.draw.rect(screen, (255, 255, 200), (light_x - 2, light_y + 1, 8, 6))
            pygame.draw.rect(screen, (80, 80, 80), (light_x - 3, light_y, 10, 8), 1)

    def _draw_nautical_details(self, screen):
        """Draw nautical-themed details."""
        # Anchor decoration
        anchor_x = self.x + self.width - 40
        anchor_y = self.y - 20

        anchor_color = (80, 80, 80)

        # Anchor shaft
        pygame.draw.rect(screen, anchor_color, (anchor_x + 5, anchor_y, 3, 15))

        # Anchor arms
        arm_points = [
            (anchor_x, anchor_y + 12),
            (anchor_x + 5, anchor_y + 8),
            (anchor_x + 8, anchor_y + 8),
            (anchor_x + 13, anchor_y + 12),
            (anchor_x + 10, anchor_y + 15),
            (anchor_x + 3, anchor_y + 15)
        ]
        pygame.draw.polygon(screen, anchor_color, arm_points)

        # Anchor ring
        pygame.draw.circle(screen, anchor_color, (anchor_x + 6, anchor_y + 2), 4, 2)

        # Ship wheel decoration
        wheel_x = self.x + 40
        wheel_y = self.y - 30

        wheel_color = (120, 80, 40)

        # Wheel rim
        pygame.draw.circle(screen, wheel_color, (wheel_x, wheel_y), 12, 3)

        # Wheel spokes
        for angle in range(0, 360, 45):
            spoke_end_x = wheel_x + math.cos(math.radians(angle)) * 8
            spoke_end_y = wheel_y + math.sin(math.radians(angle)) * 8
            pygame.draw.line(screen, wheel_color, (wheel_x, wheel_y), (spoke_end_x, spoke_end_y), 2)

        # Wheel center
        pygame.draw.circle(screen, wheel_color, (wheel_x, wheel_y), 3)

    def _draw_structures(self, screen):
        """Draw dock structures like cranes and buildings."""
        # Enhanced crane system - positioned to work with warehouse
        crane_x = self.x + 80  # Position between dock and warehouse
        crane_y = self.y - 60

        # Crane base with more detail
        base_rect = pygame.Rect(crane_x, crane_y + 40, 25, 50)
        pygame.draw.rect(screen, (100, 100, 100), base_rect)
        pygame.draw.rect(screen, (80, 80, 80), base_rect, 3)

        # Crane tower
        tower_rect = pygame.Rect(crane_x + 8, crane_y, 8, 40)
        pygame.draw.rect(screen, (120, 120, 120), tower_rect)

        # Crane arm with animation
        arm_angle = math.sin(self.crane_animation) * 0.3
        arm_length = 45
        arm_end_x = crane_x + 12 + math.cos(arm_angle) * arm_length
        arm_end_y = crane_y + 15 + math.sin(arm_angle) * 15

        # Main arm
        pygame.draw.line(screen, (140, 140, 140),
                        (crane_x + 12, crane_y + 15), (arm_end_x, arm_end_y), 5)

        # Support cable
        pygame.draw.line(screen, (200, 200, 200),
                        (arm_end_x, arm_end_y), (arm_end_x, arm_end_y + 20), 2)

        # Hook
        pygame.draw.circle(screen, (180, 180, 180), (int(arm_end_x), int(arm_end_y + 20)), 3)

        # Enhanced dock office/control building
        building_rect = pygame.Rect(self.x - 70, self.y - 60, 60, 60)
        pygame.draw.rect(screen, (140, 140, 160), building_rect)
        pygame.draw.rect(screen, (80, 80, 100), building_rect, 3)

        # Roof
        roof_points = [
            (building_rect.x, building_rect.y),
            (building_rect.x + building_rect.width // 2, building_rect.y - 15),
            (building_rect.x + building_rect.width, building_rect.y)
        ]
        pygame.draw.polygon(screen, (120, 80, 80), roof_points)
        pygame.draw.polygon(screen, (80, 60, 60), roof_points, 2)

        # Windows with frames
        window_positions = [
            (self.x - 60, self.y - 45, 15, 15),
            (self.x - 35, self.y - 45, 15, 15),
            (self.x - 60, self.y - 25, 15, 15),
            (self.x - 35, self.y - 25, 15, 15)
        ]

        for wx, wy, ww, wh in window_positions:
            # Window frame
            pygame.draw.rect(screen, (100, 100, 120), (wx, wy, ww, wh))
            # Window glass
            pygame.draw.rect(screen, (180, 200, 255), (wx + 2, wy + 2, ww - 4, wh - 4))
            # Window cross
            pygame.draw.line(screen, (100, 100, 120),
                           (wx + ww//2, wy + 2), (wx + ww//2, wy + wh - 2), 1)
            pygame.draw.line(screen, (100, 100, 120),
                           (wx + 2, wy + wh//2), (wx + ww - 2, wy + wh//2), 1)

        # Door
        door_rect = pygame.Rect(self.x - 50, self.y - 15, 12, 20)
        pygame.draw.rect(screen, (80, 60, 40), door_rect)
        pygame.draw.rect(screen, (60, 40, 20), door_rect, 2)

        # Door handle
        pygame.draw.circle(screen, (200, 180, 100), (self.x - 42, self.y - 5), 2)

        # Dock equipment boxes
        equipment_positions = [
            (self.x + 20, self.y - 25, 15, 15),
            (self.x + 40, self.y - 20, 12, 12),
            (self.x + self.width - 60, self.y - 30, 18, 18)
        ]

        for ex, ey, ew, eh in equipment_positions:
            pygame.draw.rect(screen, (100, 120, 100), (ex, ey, ew, eh))
            pygame.draw.rect(screen, (80, 100, 80), (ex, ey, ew, eh), 2)
    
    def _draw_crew(self, screen):
        """Draw unloading crew members."""
        for crew in self.unloading_crew:
            # Determine crew member appearance based on state
            if crew['state'] == 'carrying' and crew['carry_resource']:
                crew_color = (100, 150, 100)  # Green when carrying
                outline_color = (50, 100, 50)
            elif crew['state'] == 'unloading':
                crew_color = (200, 150, 100)  # Orange when working
                outline_color = (150, 100, 50)
            else:
                crew_color = (100, 100, 200)  # Blue when walking
                outline_color = (50, 50, 150)

            # Walking animation
            bob_offset = math.sin(crew['animation'] * 2) * 1.5 if crew['state'] in ['walking_to_sub', 'carrying'] else 0
            work_bob = math.sin(crew['animation'] * 4) * 1 if crew['state'] == 'unloading' else 0

            crew_x = int(crew['x'])
            crew_y = int(crew['y'] + bob_offset + work_bob)

            # Draw crew member body (larger and more detailed)
            pygame.draw.circle(screen, crew_color, (crew_x, crew_y), 8)
            pygame.draw.circle(screen, outline_color, (crew_x, crew_y), 8, 2)

            # Draw crew member head
            pygame.draw.circle(screen, (255, 220, 177), (crew_x, crew_y - 6), 4)  # Skin tone
            pygame.draw.circle(screen, (139, 69, 19), (crew_x, crew_y - 6), 4, 1)  # Hair outline

            # Draw carried resource with better positioning
            if crew['carry_resource']:
                resource_color = RESOURCE_TYPES[crew['carry_resource']]['color']
                carry_x = crew_x + 10
                carry_y = crew_y - 3

                # Draw resource with glow effect
                pygame.draw.circle(screen, resource_color, (carry_x, carry_y), 4)
                pygame.draw.circle(screen, WHITE, (carry_x, carry_y), 4, 1)

                # Add small sparkle effect
                sparkle_offset = math.sin(crew['animation'] * 3) * 2
                pygame.draw.circle(screen, WHITE,
                                 (carry_x + int(sparkle_offset), carry_y - 2), 1)

            # Draw work indicator when unloading
            if crew['state'] == 'unloading':
                # Draw small work particles
                for i in range(3):
                    particle_x = crew_x + random.randint(-5, 5)
                    particle_y = crew_y - 10 + random.randint(-3, 3)
                    pygame.draw.circle(screen, (255, 255, 0), (particle_x, particle_y), 1)
    
    def _draw_particles(self, screen):
        """Draw unloading particle effects."""
        for particle in self.unloading_particles:
            alpha = max(0, particle['life'] / particle['max_life'])

            if particle['type'] == 'steam':
                # Draw steam with transparency
                steam_surface = pygame.Surface((particle['size'] * 2, particle['size'] * 2))
                steam_surface.set_alpha(int(alpha * 100))
                steam_surface.fill(particle['color'])
                screen.blit(steam_surface, (int(particle['x'] - particle['size']),
                                          int(particle['y'] - particle['size'])))
            elif particle['type'] == 'sparkle':
                # Draw sparkles with twinkling effect
                if random.random() < 0.7:  # 70% chance to show sparkle
                    pygame.draw.circle(screen, particle['color'],
                                     (int(particle['x']), int(particle['y'])), particle['size'])
            elif particle['type'] == 'transfer':
                # Draw transfer particles with enhanced glow effect
                size = int(particle['size'] * alpha)
                if size > 0:
                    # Draw glow effect for transfer particles
                    if particle.get('glow', False):
                        glow_size = size + 3
                        glow_color = tuple(min(255, c + 80) for c in particle['color'][:3])
                        pygame.draw.circle(screen, glow_color,
                                         (int(particle['x']), int(particle['y'])), glow_size)

                    # Draw main particle
                    pygame.draw.circle(screen, particle['color'],
                                     (int(particle['x']), int(particle['y'])), size)

                    # Add bright highlight for transfer particles
                    highlight_color = (255, 255, 255)
                    pygame.draw.circle(screen, highlight_color,
                                     (int(particle['x'] - 1), int(particle['y'] - 1)), max(1, size - 2))

                    # Add resource type indicator (small letter)
                    if particle.get('resource_type'):
                        resource_letters = {'kelp': 'K', 'coral': 'C', 'pearl': 'P', 'treasure': 'T'}
                        letter = resource_letters.get(particle['resource_type'], '?')
                        # Note: This would need a font to render properly, skipping for now
            elif particle['type'] == 'warehouse_transfer':
                # Draw warehouse transfer particles with movement toward warehouse
                size = int(particle['size'] * alpha)
                if size > 0:
                    # Draw enhanced glow effect for warehouse particles
                    glow_size = size + 4
                    glow_color = tuple(min(255, c + 100) for c in particle['color'][:3])
                    pygame.draw.circle(screen, glow_color,
                                     (int(particle['x']), int(particle['y'])), glow_size)

                    # Draw main particle with pulsing effect
                    pulse = math.sin(particle['life'] * 0.3) * 0.3 + 0.7
                    pulsed_size = int(size * pulse)
                    pygame.draw.circle(screen, particle['color'],
                                     (int(particle['x']), int(particle['y'])), pulsed_size)

                    # Draw trail effect
                    trail_color = tuple(c // 2 for c in particle['color'][:3])
                    pygame.draw.circle(screen, trail_color,
                                     (int(particle['x'] - particle['vx']),
                                      int(particle['y'] - particle['vy'])), max(1, size - 2))
            else:  # regular resource particles
                # Draw resource particles with size variation
                size = int(particle['size'] * alpha)
                if size > 0:
                    pygame.draw.circle(screen, particle['color'],
                                     (int(particle['x']), int(particle['y'])), size)
                    # Add highlight
                    highlight_color = tuple(min(255, c + 50) for c in particle['color'][:3])
                    pygame.draw.circle(screen, highlight_color,
                                     (int(particle['x'] - 1), int(particle['y'] - 1)), max(1, size - 1))

    def _draw_dock_workers(self, screen):
        """Draw functional dock workers with enhanced diver-like animations and status indicators."""
        for worker in self.dock_workers:
            # Enhanced worker appearance based on state - more like divers
            if worker.state == 'loading' or worker.state == 'unloading' or worker.state == 'ready_to_unload':
                worker_color = (255, 165, 0)  # Orange when working (like diver collecting)
                outline_color = (200, 120, 0)
                worker_size = 8  # Larger when working
            elif worker.carrying_resources:
                worker_color = (0, 255, 0)  # Green when carrying (like diver returning)
                outline_color = (0, 200, 0)
                worker_size = 8
            elif worker.state == 'moving_to_sub' or worker.state == 'moving_to_dock' or worker.state == 'moving_to_warehouse':
                if worker.state == 'moving_to_warehouse':
                    worker_color = (255, 200, 0)  # Orange when moving to warehouse
                    outline_color = (200, 150, 0)
                else:
                    worker_color = (100, 150, 255)  # Blue when moving (like diver swimming)
                    outline_color = (50, 100, 200)
                worker_size = 7
            else:
                worker_color = (150, 150, 150)  # Light gray when idle
                outline_color = (100, 100, 100)
                worker_size = 6  # Smaller when idle

            # Enhanced animation based on state - more pronounced like divers
            worker_x = int(worker.x)
            if worker.state == 'moving_to_sub' or worker.state == 'moving_to_dock' or worker.state == 'moving_to_warehouse':
                # Walking animation - more pronounced bobbing like diver swimming
                if worker.state == 'moving_to_warehouse':
                    # Different animation for warehouse movement
                    worker_y = int(worker.y + math.sin(worker.animation_offset * 4) * 3)
                else:
                    worker_y = int(worker.y + math.sin(worker.animation_offset * 3) * 4)
            elif worker.state == 'loading' or worker.state == 'unloading' or worker.state == 'ready_to_unload':
                # Working animation - vibration like diver collecting
                worker_y = int(worker.y + math.sin(worker.animation_offset * 6) * 2)
            else:
                # Idle animation - gentle bobbing
                worker_y = int(worker.y + math.sin(worker.animation_offset) * 1.5)

            # Enhanced shadow with state-based size
            shadow_width = worker_size + 3
            shadow_height = max(4, worker_size // 2)
            pygame.draw.ellipse(screen, (40, 40, 40),
                              (worker_x - shadow_width//2, worker_y + worker_size + 3,
                               shadow_width, shadow_height))

            # Draw worker body (main circle) - larger and more prominent
            pygame.draw.circle(screen, worker_color, (worker_x, worker_y), worker_size)
            pygame.draw.circle(screen, outline_color, (worker_x, worker_y), worker_size, 2)

            # Draw worker details like divers
            # Hard hat/helmet
            helmet_size = max(2, worker_size - 2)
            helmet_color = (255, 255, 0) if worker.state in ['loading', 'unloading'] else (200, 200, 200)
            pygame.draw.circle(screen, helmet_color, (worker_x, worker_y - 2), helmet_size)
            pygame.draw.circle(screen, (150, 150, 150), (worker_x, worker_y - 2), helmet_size, 1)

            # Work vest/uniform
            vest_width = max(3, worker_size - 1)
            vest_height = max(2, worker_size // 2)
            vest_color = (255, 100, 0) if worker.carrying_resources else (100, 100, 200)
            pygame.draw.rect(screen, vest_color,
                           (worker_x - vest_width//2, worker_y - vest_height//2, vest_width, vest_height))

            # Work tools indicator
            if worker.state in ['loading', 'unloading']:
                tool_x = worker_x + worker_size - 2
                tool_y = worker_y
                pygame.draw.circle(screen, (139, 69, 19), (tool_x, tool_y), 2)  # Tool handle

            # Special unloading visual - show resources being placed on dock
            if worker.state == 'unloading' and worker.carrying_resources:
                # Draw resources being placed on the dock surface
                dock_surface_y = worker_y + 15  # Below the worker
                resource_x_offset = -10
                for resource_type, amount in worker.carrying_resources.items():
                    if amount > 0:
                        resource_colors = {
                            'kelp': (50, 200, 50),
                            'coral': (255, 120, 120),
                            'pearl': (220, 220, 255),
                            'treasure': (255, 215, 0)
                        }
                        color = resource_colors.get(resource_type, (255, 255, 255))

                        # Draw resource pile on dock
                        pile_x = worker_x + resource_x_offset
                        pile_y = dock_surface_y

                        # Draw multiple small resources to show a pile
                        for i in range(min(3, amount)):
                            pile_offset_x = random.randint(-3, 3)
                            pile_offset_y = random.randint(-2, 2)
                            resource_size = 2 + i

                            pygame.draw.circle(screen, color,
                                             (pile_x + pile_offset_x, pile_y + pile_offset_y), resource_size)
                            pygame.draw.circle(screen, (255, 255, 255),
                                             (pile_x + pile_offset_x, pile_y + pile_offset_y), resource_size, 1)

                        # Draw transfer arrow from worker to pile
                        arrow_start_x = worker_x
                        arrow_start_y = worker_y + worker_size
                        arrow_end_x = pile_x
                        arrow_end_y = pile_y - 5

                        pygame.draw.line(screen, (255, 255, 255),
                                       (arrow_start_x, arrow_start_y), (arrow_end_x, arrow_end_y), 2)
                        # Arrow head
                        pygame.draw.circle(screen, (255, 255, 255), (arrow_end_x, arrow_end_y), 2)

                        resource_x_offset += 8

            # Enhanced carried resources visualization - more like diver carrying resources
            if worker.carrying_resources:
                resource_y = worker_y - worker_size - 12
                total_resources = sum(worker.carrying_resources.values())

                # Draw enhanced backpack/container when carrying multiple items
                if total_resources > 1:
                    container_size = min(10, 6 + total_resources)
                    # Draw backpack with straps
                    pygame.draw.rect(screen, (139, 69, 19),
                                   (worker_x - container_size//2, resource_y - 4,
                                    container_size, container_size))
                    pygame.draw.rect(screen, (101, 67, 33),
                                   (worker_x - container_size//2, resource_y - 4,
                                    container_size, container_size), 2)
                    # Backpack straps
                    pygame.draw.line(screen, (101, 67, 33),
                                   (worker_x - 3, worker_y - 2), (worker_x - container_size//2 + 1, resource_y), 2)
                    pygame.draw.line(screen, (101, 67, 33),
                                   (worker_x + 3, worker_y - 2), (worker_x + container_size//2 - 1, resource_y), 2)

                # Draw individual resource indicators with glow effect like divers
                resource_x_offset = -8
                for resource_type, amount in worker.carrying_resources.items():
                    if amount > 0:
                        resource_colors = {
                            'kelp': (50, 200, 50),
                            'coral': (255, 120, 120),
                            'pearl': (220, 220, 255),
                            'treasure': (255, 215, 0)
                        }
                        color = resource_colors.get(resource_type, (255, 255, 255))

                        # Draw resource with glow effect and amount-based size
                        resource_size = min(5, 3 + amount)

                        # Glow effect
                        glow_size = resource_size + 2
                        glow_color = tuple(min(255, c + 50) for c in color)
                        pygame.draw.circle(screen, glow_color,
                                         (worker_x + resource_x_offset, resource_y), glow_size)

                        # Main resource
                        pygame.draw.circle(screen, color,
                                         (worker_x + resource_x_offset, resource_y), resource_size)
                        pygame.draw.circle(screen, (255, 255, 255),
                                         (worker_x + resource_x_offset, resource_y), resource_size, 1)

                        resource_x_offset += 6

            # Draw movement path line like diver tether
            if worker.state in ['moving_to_sub', 'moving_to_dock', 'loading']:
                if worker.state == 'moving_to_sub' and worker.target_submarine:
                    # Draw line to submarine like diver tether
                    target_x = worker.target_submarine.x + worker.target_submarine.width // 2
                    target_y = worker.target_submarine.y + worker.target_submarine.height
                    path_color = (100, 150, 255)  # Blue path to submarine
                elif worker.state == 'moving_to_dock' or worker.state == 'loading':
                    # Draw line to dock
                    target_x = worker.dock_x
                    target_y = worker.dock_y
                    path_color = (150, 255, 150)  # Green path to dock
                else:
                    target_x, target_y = worker.target_x, worker.target_y
                    path_color = (200, 200, 200)  # Gray default path

                # Draw dashed path line
                distance = math.sqrt((target_x - worker_x)**2 + (target_y - worker_y)**2)
                if distance > 10:  # Only draw if not at target
                    steps = max(3, int(distance / 15))  # Dash every 15 pixels
                    for i in range(0, steps, 2):  # Every other step for dashed effect
                        t1 = i / steps
                        t2 = min(1.0, (i + 1) / steps)
                        line_x1 = worker_x + (target_x - worker_x) * t1
                        line_y1 = worker_y + (target_y - worker_y) * t1
                        line_x2 = worker_x + (target_x - worker_x) * t2
                        line_y2 = worker_y + (target_y - worker_y) * t2
                        pygame.draw.line(screen, path_color, (int(line_x1), int(line_y1)), (int(line_x2), int(line_y2)), 2)

            # Enhanced state indicators
            if worker.state == 'loading' or worker.state == 'unloading':
                # Enhanced work particles with different colors and sparkle effect
                particle_colors = [(255, 255, 100), (255, 200, 100), (200, 255, 100), (100, 255, 255)]
                for i in range(5):  # More particles
                    particle_x = worker_x + random.randint(-8, 8)
                    particle_y = worker_y - 8 + random.randint(-5, 5)
                    color = random.choice(particle_colors)
                    size = random.randint(1, 3)
                    pygame.draw.circle(screen, color, (particle_x, particle_y), size)
                    # Add sparkle effect
                    if random.random() < 0.3:
                        pygame.draw.circle(screen, (255, 255, 255), (particle_x, particle_y), 1)

                # Enhanced work progress indicator like diver oxygen bar
                if hasattr(worker, 'work_timer') and hasattr(worker, 'base_work_time'):
                    # Calculate progress based on worker state
                    if worker.state == 'unloading':
                        base_unload_time = worker.base_work_time * 1.2  # Match the 1.2x unloading time
                        work_progress = worker.work_timer / (base_unload_time * (1.0 - worker.work_speed_bonus))
                    else:
                        work_progress = worker.work_timer / (worker.base_work_time * (1.0 - worker.work_speed_bonus))

                    if work_progress > 0:
                        progress_width = 16
                        progress_height = 3
                        progress_x = worker_x - progress_width // 2
                        progress_y = worker_y - worker_size - 18

                        # Progress bar background with border
                        pygame.draw.rect(screen, (40, 40, 40),
                                       (progress_x, progress_y, progress_width, progress_height))
                        pygame.draw.rect(screen, (200, 200, 200),
                                       (progress_x, progress_y, progress_width, progress_height), 1)

                        # Progress bar fill with color gradient
                        fill_width = int(progress_width * min(1.0, work_progress))
                        if fill_width > 0:
                            # Different colors for different states
                            if worker.state == 'unloading':
                                # Blue gradient for unloading
                                if work_progress < 0.3:
                                    fill_color = (100, 100, 255)  # Light blue at start
                                elif work_progress < 0.7:
                                    fill_color = (100, 200, 255)  # Cyan in middle
                                else:
                                    fill_color = (100, 255, 100)  # Green near completion
                            else:
                                # Original colors for loading
                                if work_progress < 0.3:
                                    fill_color = (255, 100, 100)  # Red at start
                                elif work_progress < 0.7:
                                    fill_color = (255, 255, 100)  # Yellow in middle
                                else:
                                    fill_color = (100, 255, 100)  # Green near completion
                            pygame.draw.rect(screen, fill_color,
                                           (progress_x, progress_y, fill_width, progress_height))

            elif worker.state == 'moving_to_sub' or worker.state == 'moving_to_dock' or worker.state == 'moving_to_warehouse':
                # Enhanced movement trail effect
                trail_length = 5
                trail_color = worker_color if worker.state == 'moving_to_warehouse' else (100, 150, 255)
                for i in range(trail_length):
                    trail_alpha = 120 - (i * 20)
                    if trail_alpha > 0:
                        # Calculate trail position based on movement direction
                        dx = worker.target_x - worker_x
                        dy = worker.target_y - worker_y
                        distance = math.sqrt(dx**2 + dy**2)
                        if distance > 0:
                            trail_x = worker_x - (dx / distance) * (i * 3)
                            trail_y = worker_y - (dy / distance) * (i * 3)
                        else:
                            trail_x = worker_x - (i * 2)
                            trail_y = worker_y

                        trail_surface = pygame.Surface((6, 6))
                        trail_surface.set_alpha(trail_alpha)
                        trail_surface.fill(worker_color)
                        screen.blit(trail_surface, (int(trail_x - 3), int(trail_y - 3)))

            # Enhanced Worker ID and efficiency indicator
            if len(self.dock_workers) > 1:
                font = pygame.font.Font(None, 14)
                id_text = f"W{worker.worker_id + 1}"

                # Color based on worker efficiency/state
                if worker.state in ['loading', 'unloading']:
                    text_color = (255, 255, 100)  # Yellow when working
                elif worker.carrying_resources:
                    text_color = (100, 255, 100)  # Green when carrying
                elif worker.state in ['moving_to_sub', 'moving_to_dock']:
                    text_color = (100, 150, 255)  # Blue when moving
                else:
                    text_color = (200, 200, 200)  # Gray when idle

                # Draw text with shadow for better visibility
                shadow_text = font.render(id_text, True, (0, 0, 0))
                main_text = font.render(id_text, True, text_color)

                text_rect = main_text.get_rect(center=(worker_x, worker_y + worker_size + 15))
                shadow_rect = text_rect.copy()
                shadow_rect.x += 1
                shadow_rect.y += 1

                screen.blit(shadow_text, shadow_rect)
                screen.blit(main_text, text_rect)

            # Worker efficiency indicator (small bar above worker)
            if worker.state != 'idle':
                efficiency = 1.0 + worker.speed_bonus + worker.work_speed_bonus
                if efficiency > 1.0:
                    eff_width = 12
                    eff_height = 2
                    eff_x = worker_x - eff_width // 2
                    eff_y = worker_y - worker_size - 25

                    # Background
                    pygame.draw.rect(screen, (60, 60, 60), (eff_x, eff_y, eff_width, eff_height))

                    # Efficiency fill (green for good efficiency)
                    fill_width = int(eff_width * min(1.0, (efficiency - 1.0) / 1.0))  # Scale 1.0-2.0 to 0-1
                    if fill_width > 0:
                        pygame.draw.rect(screen, (0, 255, 0), (eff_x, eff_y, fill_width, eff_height))

    def _draw_ui(self, screen):
        """Draw dock UI elements."""
        if self.is_occupied:
            # Enhanced unloading progress bar
            bar_width = 120
            bar_height = 12
            bar_x = self.x + self.width // 2 - bar_width // 2
            bar_y = self.y - 70

            # Background with border
            pygame.draw.rect(screen, BLACK, (bar_x - 2, bar_y - 2, bar_width + 4, bar_height + 4))
            pygame.draw.rect(screen, (60, 60, 60), (bar_x, bar_y, bar_width, bar_height))

            # Progress with gradient effect
            progress_width = int(self.unloading_progress * bar_width)
            if progress_width > 0:
                # Create gradient effect
                for i in range(progress_width):
                    alpha = 0.7 + 0.3 * (i / max(1, bar_width))
                    color_intensity = int(255 * alpha)
                    progress_color = (0, color_intensity, 0)
                    pygame.draw.rect(screen, progress_color, (bar_x + i, bar_y, 1, bar_height))

            # Progress text with better styling
            font = pygame.font.Font(None, 28)
            progress_percent = int(self.unloading_progress * 100)
            text = font.render(f"Unloading... {progress_percent}%", True, WHITE)
            text_rect = text.get_rect(center=(self.x + self.width // 2, bar_y - 20))

            # Text shadow
            shadow_text = font.render(f"Unloading... {progress_percent}%", True, BLACK)
            shadow_rect = text_rect.copy()
            shadow_rect.x += 1
            shadow_rect.y += 1
            screen.blit(shadow_text, shadow_rect)
            screen.blit(text, text_rect)

            # Draw crew count indicator
            crew_count = len(self.unloading_crew)
            crew_font = pygame.font.Font(None, 20)
            crew_text = crew_font.render(f"Crew: {crew_count}", True, WHITE)
            crew_rect = crew_text.get_rect(center=(self.x + self.width // 2, bar_y + 25))
            screen.blit(crew_text, crew_rect)

        # Draw dock status indicator when not occupied
        else:
            font = pygame.font.Font(None, 24)
            status_text = font.render("🚢 Dock Available", True, (100, 255, 100))
            status_rect = status_text.get_rect(center=(self.x + self.width // 2, self.y - 30))
            screen.blit(status_text, status_rect)

        # Always draw dock storage indicators
        self._draw_dock_storage_indicators(screen)

    def _draw_dock_storage_indicators(self, screen):
        """Draw dock storage capacity and contents indicators."""
        try:
            # Calculate storage metrics
            total_stored = sum(self.stored_resources.values())
            max_capacity = self.max_storage + (self.upgrades.get('storage_capacity', 0) * 50)
            storage_percentage = total_stored / max_capacity if max_capacity > 0 else 0

            # Storage area on the dock platform
            storage_area_width = min(80, self.width - 20)
            storage_area_height = 15
            storage_x = self.x + (self.width - storage_area_width) // 2
            storage_y = self.y + 5

            # Background for storage area
            pygame.draw.rect(screen, (60, 40, 20), (storage_x, storage_y, storage_area_width, storage_area_height))
            pygame.draw.rect(screen, (40, 25, 10), (storage_x, storage_y, storage_area_width, storage_area_height), 2)

            # Storage capacity bar
            if storage_percentage > 0:
                fill_width = int(storage_area_width * storage_percentage)
                if storage_percentage >= 0.9:
                    fill_color = (200, 100, 50)  # Orange when nearly full
                elif storage_percentage >= 0.7:
                    fill_color = (150, 150, 50)  # Yellow when getting full
                else:
                    fill_color = (50, 150, 100)  # Green when normal

                pygame.draw.rect(screen, fill_color, (storage_x, storage_y, fill_width, storage_area_height))

            # Resource type indicators (stacked boxes)
            box_size = 8
            box_spacing = 10
            start_x = storage_x + 5
            box_y = storage_y + storage_area_height + 5

            resource_colors = {
                'kelp': (50, 150, 50),
                'coral': (255, 100, 100),
                'pearl': (200, 200, 255),
                'treasure': (255, 215, 0)
            }

            box_x = start_x
            for resource_type, color in resource_colors.items():
                stored_amount = self.stored_resources.get(resource_type, 0)
                if stored_amount > 0:
                    # Draw stacked boxes to represent quantity
                    max_boxes = min(5, stored_amount // 10 + 1)  # 1-5 boxes based on amount
                    for i in range(max_boxes):
                        box_offset_y = box_y - (i * 2)
                        pygame.draw.rect(screen, color, (box_x, box_offset_y, box_size, box_size))
                        pygame.draw.rect(screen, (255, 255, 255), (box_x, box_offset_y, box_size, box_size), 1)

                    # Draw amount text
                    if stored_amount > 0:
                        font = pygame.font.Font(None, 16)
                        amount_text = font.render(str(stored_amount), True, (255, 255, 255))
                        text_rect = amount_text.get_rect(center=(box_x + box_size // 2, box_y + box_size + 8))

                        # Text shadow
                        shadow_text = font.render(str(stored_amount), True, (0, 0, 0))
                        shadow_rect = text_rect.copy()
                        shadow_rect.x += 1
                        shadow_rect.y += 1
                        screen.blit(shadow_text, shadow_rect)
                        screen.blit(amount_text, text_rect)

                    box_x += box_spacing

            # Storage capacity text
            font = pygame.font.Font(None, 18)
            capacity_text = f"Storage: {total_stored}/{max_capacity}"
            capacity_color = (255, 255, 255)
            if storage_percentage >= 0.9:
                capacity_color = (255, 150, 150)
            elif storage_percentage >= 0.7:
                capacity_color = (255, 255, 150)

            text_surface = font.render(capacity_text, True, capacity_color)
            text_rect = text_surface.get_rect(center=(self.x + self.width // 2, storage_y - 15))

            # Text shadow
            shadow_surface = font.render(capacity_text, True, (0, 0, 0))
            shadow_rect = text_rect.copy()
            shadow_rect.x += 1
            shadow_rect.y += 1
            screen.blit(shadow_surface, shadow_rect)
            screen.blit(text_surface, text_rect)

            # Dock worker efficiency indicator
            worker_count = self.get_dock_worker_count()
            if worker_count > 0:
                efficiency = self.get_dock_worker_efficiency()
                efficiency_text = f"Workers: {worker_count} ({efficiency:.0%})"
                efficiency_color = (150, 255, 150) if efficiency >= 1.5 else (255, 255, 255)

                worker_font = pygame.font.Font(None, 16)
                worker_surface = worker_font.render(efficiency_text, True, efficiency_color)
                worker_rect = worker_surface.get_rect(center=(self.x + self.width // 2, self.y + self.platform_height + 35))

                # Text shadow
                worker_shadow = worker_font.render(efficiency_text, True, (0, 0, 0))
                worker_shadow_rect = worker_rect.copy()
                worker_shadow_rect.x += 1
                worker_shadow_rect.y += 1
                screen.blit(worker_shadow, worker_shadow_rect)
                screen.blit(worker_surface, worker_rect)

        except (ValueError, OverflowError, pygame.error):
            pass

    def draw_approach_indicator(self, screen, submarine):
        """Draw visual indicator when submarine is approaching dock."""
        if not submarine:
            return

        try:
            # Always show dock zone when submarine is at surface
            if submarine.depth <= 30:
                dock_zone = self.get_dock_zone_rect()

                # Draw dock zone outline
                zone_color = (0, 255, 0, 100) if not self.is_occupied else (255, 0, 0, 100)
                zone_surface = pygame.Surface((dock_zone.width, dock_zone.height))
                zone_surface.set_alpha(100)
                zone_surface.fill(zone_color[:3])
                screen.blit(zone_surface, (dock_zone.x, dock_zone.y))

                # Draw zone border
                pygame.draw.rect(screen, zone_color[:3], dock_zone, 3)

                # Check if submarine is in approach range
                sub_rect = pygame.Rect(int(submarine.x), int(submarine.y),
                                     int(submarine.width), int(submarine.height))

                if submarine.depth <= 20 and dock_zone.colliderect(sub_rect):
                    # Draw approach indicator
                    approach_surface = pygame.Surface((dock_zone.width, dock_zone.height))
                    approach_surface.set_alpha(128)
                    approach_surface.fill((255, 255, 0))
                    screen.blit(approach_surface, (dock_zone.x, dock_zone.y))

                    # Draw approach text
                    font = pygame.font.Font(None, 28)
                    if not self.is_occupied:
                        approach_text = font.render("🔄 Approaching Dock", True, WHITE)
                    else:
                        approach_text = font.render("⏳ Dock Occupied", True, WHITE)
                    text_rect = approach_text.get_rect(center=(self.x + self.width // 2, self.y - 50))

                    # Text shadow
                    text_content = "🔄 Approaching Dock" if not self.is_occupied else "⏳ Dock Occupied"
                    shadow_text = font.render(text_content, True, BLACK)
                    shadow_rect = text_rect.copy()
                    shadow_rect.x += 1
                    shadow_rect.y += 1
                    screen.blit(shadow_text, shadow_rect)
                    screen.blit(approach_text, text_rect)
        except Exception as e:
            # Silently handle any drawing errors
            pass
    
    def get_dock_zone_rect(self):
        """Get the docking zone rectangle for collision detection."""
        return pygame.Rect(int(self.x - 50), int(self.y - 30),
                          int(self.width + 100), int(60))
    
    def can_dock(self):
        """Check if dock is available for docking."""
        return not self.is_occupied
    
    def get_upgrade_cost(self, upgrade_type):
        """Get the cost for upgrading a dock feature."""
        base_costs = {
            'processing_speed': {'kelp': 20, 'coral': 5},
            'storage_capacity': {'kelp': 15, 'coral': 8, 'pearl': 2},
            'automation': {'coral': 10, 'pearl': 5, 'treasure': 1},
            'efficiency': {'pearl': 8, 'treasure': 2},
            'crew_quarters': {'kelp': 25, 'coral': 10, 'pearl': 3}
        }
        
        if upgrade_type not in base_costs:
            return None
            
        current_level = self.upgrades[upgrade_type]
        multiplier = 1.5 ** current_level
        
        cost = {}
        for resource, amount in base_costs[upgrade_type].items():
            cost[resource] = int(amount * multiplier)
            
        return cost
    
    def upgrade(self, upgrade_type, resources):
        """Upgrade a dock feature if affordable."""
        cost = self.get_upgrade_cost(upgrade_type)
        if not cost:
            return False
            
        # Check if affordable
        for resource, amount in cost.items():
            if resources.get(resource, 0) < amount:
                return False
        
        # Deduct resources
        for resource, amount in cost.items():
            resources[resource] -= amount
            
        # Apply upgrade
        self.upgrades[upgrade_type] += 1
        
        # Update storage capacity if upgraded
        if upgrade_type == 'storage_capacity':
            self.update_storage_capacity()

        return True

    def update_storage_capacity(self):
        """Update storage capacity based on current upgrade level."""
        self.max_storage = 100 + (self.upgrades['storage_capacity'] * 50)

    def get_combined_resources(self):
        """Get combined resources from dock storage and warehouse."""
        combined = {}

        # Start with dock storage
        for resource_type, amount in self.stored_resources.items():
            combined[resource_type] = amount

        # Add warehouse storage
        if hasattr(self, 'warehouse'):
            for resource_type, amount in self.warehouse.stored_resources.items():
                combined[resource_type] = combined.get(resource_type, 0) + amount

        return combined

    def deduct_combined_resources(self, resource_costs):
        """Deduct resources from warehouse first, then dock storage."""
        for resource_type, amount_needed in resource_costs.items():
            remaining_needed = amount_needed

            # First try to deduct from warehouse
            if hasattr(self, 'warehouse'):
                warehouse_available = self.warehouse.stored_resources.get(resource_type, 0)
                warehouse_deducted = min(remaining_needed, warehouse_available)
                if warehouse_deducted > 0:
                    self.warehouse.stored_resources[resource_type] -= warehouse_deducted
                    remaining_needed -= warehouse_deducted

            # Then deduct remaining from dock storage
            if remaining_needed > 0:
                dock_available = self.stored_resources.get(resource_type, 0)
                dock_deducted = min(remaining_needed, dock_available)
                if dock_deducted > 0:
                    self.stored_resources[resource_type] -= dock_deducted
                    remaining_needed -= dock_deducted

            # If we couldn't deduct everything, there's an error
            if remaining_needed > 0:
                print(f"Warning: Could not deduct {remaining_needed} {resource_type} from combined storage")

    def can_afford_combined(self, resource_costs):
        """Check if combined dock and warehouse storage can afford the costs."""
        combined_resources = self.get_combined_resources()

        for resource_type, amount_needed in resource_costs.items():
            available = combined_resources.get(resource_type, 0)
            if available < amount_needed:
                return False

        return True

    def get_storage_capacity(self):
        """Get current storage capacity."""
        return self.max_storage + (self.upgrades.get('storage_capacity', 0) * 50)

    def get_storage_used(self):
        """Get current storage used."""
        return sum(self.stored_resources.values())

    def get_storage_percentage(self):
        """Get storage usage as a percentage."""
        capacity = self.get_storage_capacity()
        if capacity <= 0:
            return 0.0
        return self.get_storage_used() / capacity
